import 'package:intl/intl.dart' as intl;

import 'l10n.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class SZh extends S {
  SZh([String locale = 'zh']) : super(locale);

  @override
  String get app_name => '验货在线';

  @override
  String get search => '搜索';

  @override
  String get shortcut_tab_name => '快捷导航';

  @override
  String get loading => '加载中...';

  @override
  String get nomore => '暂无更多内容';

  @override
  String get confirm => '确定';

  @override
  String get more_replies => '更多回复';

  @override
  String get purchase_paid_publish_information_title => '以下是客户付费查看的内容';

  @override
  String get purchase_set_fee => '设置查看费用';

  @override
  String get purchase_comment_paid_supplier_hint => '请输入供应商名称';

  @override
  String get purchase_comment_paid_contact_hint => '请输入联系人姓名';

  @override
  String get purchase_comment_paid_phone_hint => '请输入联系人电话';

  @override
  String get purchase_comment_paid_email_hint => '请输入联系人邮箱';

  @override
  String get purchase_comment_paid_address_hint => '请输入工厂地址';

  @override
  String get purchase_comment_paid_other_hint => '其它信息（选填）';

  @override
  String get purchase_comment_paid_low_price_hint => '请输入产品底价（选填）';

  @override
  String get purchase_reply_paid_title => '付费回复';

  @override
  String get purchase_reply_paid_desc => '（供应商信息及商品参考价格）';

  @override
  String purchase_reply_paid_evaluated_people(Object people_num) {
    return '（$people_num人已评价）';
  }

  @override
  String get language_setting => '语言设置';

  @override
  String get public_and => '与';

  @override
  String get public_publish => '发布';

  @override
  String get public_distance => '距离';

  @override
  String get public_deny => '拒绝';

  @override
  String get public_see_more => '查看更多';

  @override
  String get general_select => '选择';

  @override
  String get language_page_title => '多语言';

  @override
  String get language_chinese => '中文';

  @override
  String get language_english => '英文';

  @override
  String get public_seconds_ago => '秒前';

  @override
  String get public_minutes_ago => '分钟前';

  @override
  String get public_hours_ago => '小时前';

  @override
  String get public_status_applied => '已申请';

  @override
  String get public_status_refused => '已拒绝';

  @override
  String get public_status_approved => '已通过';

  @override
  String get public_status_canceled => '已取消';

  @override
  String get public_app_name => 'Inspector';

  @override
  String get public_send => '发送';

  @override
  String get public_ok => '确定';

  @override
  String get public_price => '价格';

  @override
  String get public_cancel => '取消';

  @override
  String get public_manage => '管理';

  @override
  String get public_finish => '完成';

  @override
  String get public_reset => '重置';

  @override
  String get public_leave_message => '留言';

  @override
  String get public_share => '分享';

  @override
  String get login_submit => '登录';

  @override
  String get login_mobile_login => '手机登录';

  @override
  String get login_mobile_tips => '请输入手机号';

  @override
  String get login_email_login => '邮箱登录';

  @override
  String get login_email_tips => '请输入邮箱';

  @override
  String get registry_email_phone_tips => '请输入邮箱或手机号';

  @override
  String get login_verify_tips => '输入验证码';

  @override
  String get login_password_tips => '输入密码';

  @override
  String get login_password_login => '使用密码登录';

  @override
  String get login_verify_login => '使用验证码登录';

  @override
  String get login_register => '注册';

  @override
  String get login_take_code => '获取验证码';

  @override
  String get login_forget_password => '忘记密码';

  @override
  String get login_agreement => '同意《inspector.Itd协议》';

  @override
  String get login_area_selected => '选择国区';

  @override
  String get tab_home => '首页';

  @override
  String get tab_order => '订单';

  @override
  String get tab_shortcut => '快捷';

  @override
  String get tab_purchase => '采购';

  @override
  String get tab_message => '消息';

  @override
  String get tab_mine => '我的';

  @override
  String get supplement_title => '请先完善个人信息';

  @override
  String get supplement_next => '去完善';

  @override
  String get home_title => '验货广场';

  @override
  String get home_record => '申请记录';

  @override
  String get home_newest => '最新验货';

  @override
  String get home_nearest => '附近验货';

  @override
  String home_recommend(Object money) {
    return '推荐奖励 RMB$money元';
  }

  @override
  String get home_sampling => '抽样检验';

  @override
  String get home_word => 'WORD报告';

  @override
  String home_unit(Object day, Object people) {
    return '$people人/$day天';
  }

  @override
  String get home_product_tip => '产品：';

  @override
  String get home_person_apply => '人申请';

  @override
  String get home_know_tip => '验货需知';

  @override
  String get home_inspection_tip => '验货费用默认为商定价，可修改，费用低可能优先指派';

  @override
  String get home_reviewed => '我已查看并遵守';

  @override
  String get home_apply => '申请';

  @override
  String get home_apply_price => '请输入金额￥';

  @override
  String get home_apply_check => '请查看验货须知';

  @override
  String get home_apply_tips => '您不是验货员，若您有1年以上外贸验货工作经验，请提相关资历证明';

  @override
  String get home_complete_profile_tips => '完善验货员档案等相关资料，可以提高验货申请通过率';

  @override
  String get home_apply_sure => '提交审核';

  @override
  String get home_complete_profile_sure => '去完善';

  @override
  String get home_apply_cancel => '取消申请';

  @override
  String get home_update => '修改';

  @override
  String get home_navi => '导航';

  @override
  String get mine_unauth => '未认证';

  @override
  String get mine_checking => '待审核';

  @override
  String get mine_check_failed => '审核失败';

  @override
  String get mine_vip_level => 'VIP等级';

  @override
  String get mine_credit_quota => '信用额度';

  @override
  String get mine_authed => '修改认证信息';

  @override
  String get mine_authed_inspector => '修改验货员信息';

  @override
  String get mine_amount => '账户金额';

  @override
  String get mine_cash => '充值/提现';

  @override
  String get mine_order => '我的订单';

  @override
  String get mine_purchase => '我的采购';

  @override
  String get mine_check => '我的验货';

  @override
  String get mine_address => '地址簿(供应商)';

  @override
  String get mine_recommend => '推荐';

  @override
  String get mine_setting => '设置';

  @override
  String get mine_header_inspect => '验货管理';

  @override
  String get mine_header_purchase => '采购管理';

  @override
  String get mine_header_other => '其他';

  @override
  String get mine_inspect_mine => '我的验货';

  @override
  String get mine_inspect_order => '订单管理';

  @override
  String get mine_inspect_history => '申请记录';

  @override
  String get mine_purchase_mine => '我的采购';

  @override
  String get mine_purchase_reply => '回复记录';

  @override
  String get mine_purchase_appeal => '申诉管理';

  @override
  String get mine_other_recommend => '推荐';

  @override
  String get mine_other_address => '地址簿(供应商)';

  @override
  String get mine_other_settings => '设置';

  @override
  String get profile_title => '个人信息';

  @override
  String get profile_avatar => '头像';

  @override
  String get profile_name => '昵称';

  @override
  String get profile_mobile => '手机号';

  @override
  String get profile_country => '国家';

  @override
  String get profile_real_name => '真实姓名';

  @override
  String get profile_city => '城市';

  @override
  String get profile_email => '邮箱';

  @override
  String get profile_wechat => '微信';

  @override
  String get profile_bind_manage => '账号绑定管理';

  @override
  String get profile_info_failed => '信息更新失败';

  @override
  String get apply_title => '验货员资格申请';

  @override
  String get apply_nick => '昵称';

  @override
  String get apply_sex => '性别';

  @override
  String get apply_birthday => '生日';

  @override
  String get apply_education => '学历';

  @override
  String get apply_address => '常住地';

  @override
  String get apply_price => '最低验货费用';

  @override
  String get apply_shebao => '社保';

  @override
  String get apply_id_card => '身份证号';

  @override
  String get apply_file => '编辑简历';

  @override
  String get apply_file_tip => '请输入自己的基本资料和经历';

  @override
  String get apply_upload_file => '上传简历';

  @override
  String get apply_upload_file_failed => '上传简历失败';

  @override
  String get apply_upload_card => '上传身份证照片';

  @override
  String get apply_card_front => '正面照片(人脸面)';

  @override
  String get apply_card_back => '反面照片(国徽面)';

  @override
  String get apply_submit => '提交';

  @override
  String get apply_enter => '请输入';

  @override
  String get apply_next_tip => '请信息确认完善后再提交';

  @override
  String get apply_auth_failed => '身份证验证失败, 请传入正确的照片';

  @override
  String get apply_checking => '身份待审核';

  @override
  String get apply_check_success => '身份审核通过';

  @override
  String get apply_check_failed => '审核失败，请修改内容后重新提交';

  @override
  String get order_title => '我的订单';

  @override
  String get order_input => '我的验货';

  @override
  String get order_output => '发布订单';

  @override
  String get order_all => '全部';

  @override
  String get order_wait_pay => '待支付';

  @override
  String get order_cancelled => '已取消';

  @override
  String get order_status => '订单状态';

  @override
  String get order_status_unknown => '未知';

  @override
  String get order_refund_pending => '退款待审核';

  @override
  String get order_cancelled_refund_pending => '已取消,退款待审核';

  @override
  String get order_refund_partial => '部分退款';

  @override
  String get order_refund_denied => '拒绝退款';

  @override
  String get order_wait_dispatch => '待派单';

  @override
  String get order_ready_inspect => '准备验货';

  @override
  String get order_need_pay => '支付';

  @override
  String get order_wait => '准备验货';

  @override
  String get order_confirm => '派单确认';

  @override
  String get order_doing => '验货中';

  @override
  String get order_comment => '待评价';

  @override
  String get order_finished => '已完成';

  @override
  String get order_goods_info => '产品信息';

  @override
  String get order_goods_name => '产品名称';

  @override
  String get order_goods_model => '产品型号';

  @override
  String get order_goods_count => '数量';

  @override
  String get order_goods_unit => '单位';

  @override
  String get order_order_time => '订单时间';

  @override
  String get order_order_amount => '订单金额';

  @override
  String get order_detail_title => '订单详情';

  @override
  String get order_applying => '已申请';

  @override
  String get order_apply_expired => '已过期';

  @override
  String get order_apply_dispatched => '已派单';

  @override
  String get order_create_time => '下单时间';

  @override
  String get order_look => '查看验货报告';

  @override
  String get order_report_next => '提交验货报告';

  @override
  String get order_detail_inspection_info => '验货信息';

  @override
  String get order_inspection_status_unpaid => '未付款';

  @override
  String get order_inspection_status_returned => '已退回';

  @override
  String get order_inspection_status_waiting_start => '等待开始';

  @override
  String get order_detail_related_info => '关联子订单';

  @override
  String get order_detail_inspection_product => '产品名称';

  @override
  String get order_detail_inspection_time => '验货时间';

  @override
  String get order_detail_inspection_city => '验货城市';

  @override
  String get order_detail_inspection_factory => '验货工厂';

  @override
  String get order_detail_inspection_address => '验货地址';

  @override
  String get order_detail_inspection_person => '联系人';

  @override
  String get order_detail_inspection_phone => '联系人电话';

  @override
  String get order_detail_inspection_email => '联系人邮箱';

  @override
  String get order_detail_inspection_amount => '价格信息';

  @override
  String get order_detail_inspection_sample => '样品';

  @override
  String get order_detail_inspection_standard => '抽样等级';

  @override
  String get order_detail_inspection_critical => 'AQL';

  @override
  String get order_detail_inspection_major => 'Major';

  @override
  String get order_detail_inspection_minor => 'Minor';

  @override
  String get order_detail_inspection_day => '验货天数';

  @override
  String get order_detail_inspection_number => '验货人数';

  @override
  String get order_detail_inspection_price => '验货价格';

  @override
  String get order_detail_inspection_price_apply => '申请价格';

  @override
  String get order_detail_inspection_template => '验货模板';

  @override
  String get order_detail_inspection_file => '附件';

  @override
  String get order_detail_inspection_image => '图片';

  @override
  String get order_detail_inspection_type => '验货类型';

  @override
  String get order_tips => '样品及注意事项';

  @override
  String get order_apply => '申请验货';

  @override
  String get order_cancel => '取消';

  @override
  String get order_canceled => '已取消';

  @override
  String get order_dispatch_accept => '同意';

  @override
  String get order_dispatch_refuse => '拒绝';

  @override
  String get order_inspection => '开始验货';

  @override
  String get order_sure_cancel => '确定取消订单';

  @override
  String get order_sure_refuse => '确定拒绝此订单吗？';

  @override
  String get order_sure_confirm => '确定接受此订单吗？';

  @override
  String get publish_title => '发布订单';

  @override
  String get publish_edit_title => '编辑订单';

  @override
  String get publish_welcome => '欢迎下单，报价自由，与验货员沟通自由';

  @override
  String get publish_sampling => '抽样检验';

  @override
  String get publish_sampling_content => '适用于产品100%完成，至少80%产品已包装好，准备出货前，我们根据国际抽样方案ANSI/ASQZ1.4(MIL-STD-105E)以及参照您的特殊要求进行随机抽样检验，在抽样检验报告中，我们将全面反映产品的完成数量，包装情况以及是否符合AQL（可接受质量水平）要求，让您在出货前对整批次的产品质量有全面的了解，避免让您的订单遭受任何风险';

  @override
  String get publish_point => '检验要点';

  @override
  String get publish_sampling_point => '● 客户资料/样品核对\n● 完成数量核对\n● 产品尺寸、款式、颜色核对\n● 外观工艺检查\n● 产品功能和安全性检测\n● 箱唛检查\n● 包装完整性\n● 具体包装细节\n● 客户特殊要求';

  @override
  String get publish_all => '全数检验';

  @override
  String get publish_all_content => '全数检验可以在包装前或者包装后进行，根据客户要求，对每一件产品的外观、尺寸、工艺、功能及安全性等进行检验，区分良品与不良品，并将检品结果及时汇报给客户';

  @override
  String get publish_online => '在线检验';

  @override
  String get publish_online_content => '在线检验是在生产过程中检验或者全部生产完成包装之前检验，可以及时帮您确认产品的质量，功能，外观及其它要素是否在整个生产过程都同您的规格要求保持一致，同时也有利于尽早发现任何不符点，从而降低工厂延迟交货的风险';

  @override
  String get publish_online_point => '● 生产情况跟进\n● 生产线评估及生产进度确认\n● 抽检半成品和成品\n● 检查包装信息及包装材料\n● 使有缺陷的产品改进\n● 评估交付时间';

  @override
  String get publish_factory => '工厂审核';

  @override
  String get publish_factory_content => '工厂审核主要采用客观判断法，依据事先制定好的标准或准则对工厂进行量化考核和审定，根据现场打分评比以及对工厂的综合审核结果等形成评估报告，以供客户判定该工厂是否作为其合格供应商的依据';

  @override
  String get publish_factory_point_title => '审计内容';

  @override
  String get publish_factory_point => '● 工厂概况（基本信息）\n● 组织架构\n● 生产流程\n● 生产能力\n● 研发技术能力\n● 机械设备和设施';

  @override
  String get publish_factory_review => '综合评价';

  @override
  String get publish_factory_review_content => '● 针对每个审核项目，权衡彼此的重要性，分别给予不同的分数，再根据审核调查表及实地调查的资料，出具资格评分表';

  @override
  String get publish_watch => '监装';

  @override
  String get publish_watch_content => '货柜监装主要包括评估货柜状况、核对产品信息、清点装柜的箱数，检查包装信息及监督整个装柜过程。为了降低货物在装柜后被替换的高风险，检验员在装箱现场进行监督，以确保您支付的产品安全装箱';

  @override
  String get publish_watch_point => '● 记录货柜号码和拖车号码\n● 检查货柜是否存在破损、潮湿和特殊气味，并对空柜进行拍照\n● 检查待装箱数以及外包装状况，随机抽查几箱以确认实际装箱的产品\n● 监督装柜过程，以确保破损最小化和空间利用最大化\n● 对货柜封门情况，货柜铅封号，装箱单进行拍照留底，记录货柜离开时间';

  @override
  String get publish_watch_inspection => '验货+监装';

  @override
  String get publish_watch_inspection_content => '为了确保产品的最终质量及完成情况，在准备出货前，我们根据国际抽样方案随机从成品中抽取样品进行抽样检查，并核对客户提供的资料，对整个装柜流程进行全程监控';

  @override
  String get publish_watch_inspection_point => '● 在集装箱未到达前核对客户资料/样品比对，抽样检验产品的外观工艺、功能和安全性以及产品包装、箱唛等\n● 发现不良品及时与工厂沟通，进行替换或者返工\n● 检查货柜是否存在破损、潮湿和特殊气味，并对空柜进行拍照\n● 监督装柜过程，以确保破损最小化和空间利用最大化\n● 对货柜封门情况，货柜铅封号，装箱单进行拍照留底，记录货柜离开时间';

  @override
  String get publish_next => '下一步';

  @override
  String get publish_inspection_time => '验货时间';

  @override
  String get publish_inspection_time_selected => '选择验货时间';

  @override
  String get publish_inspection_time_tip => '请选择';

  @override
  String get publish_inspection_people => '验货人数';

  @override
  String get publish_people => '人';

  @override
  String get publish_day => '天';

  @override
  String get publish_inspection_factory => '验货工厂';

  @override
  String get publish_factory_tips => '输入验货工厂';

  @override
  String get publish_address_book => '地址簿';

  @override
  String get publish_goods_name => '产品名称';

  @override
  String get publish_name_tips => '多种产品时输入一两个代表名称';

  @override
  String get publish_po_tips => '请输入P.O号';

  @override
  String get publish_file_tips => '上传附件';

  @override
  String get publish_camera => '拍照上传';

  @override
  String get publish_file => '文件上传';

  @override
  String get publish_purchase => '发布采购订单';

  @override
  String get publish_inspection => '发布验货订单';

  @override
  String get publish_factory_tip => '请先选择验货地址等信息';

  @override
  String get publish_attention => '注意事项';

  @override
  String get publish_attention_tips => '请输入哪些问题需要验货员重点关注';

  @override
  String get publish_stand_price => '一口价';

  @override
  String get publish_click_price => '切换';

  @override
  String get publish_vip_price => 'VIP价';

  @override
  String get publish_vip_tips => '提供人工全程跟单等服务';

  @override
  String get publish_total => '合计';

  @override
  String get publish_submit => '提交';

  @override
  String get publish_only_price_failed => '无一口价权限';

  @override
  String get publish_price_tip => '请选择价格';

  @override
  String get publish_date_tips => '请选择日期';

  @override
  String get date_title => '验货日期';

  @override
  String get date_save => '保存';

  @override
  String get address_title => '编辑工厂信息';

  @override
  String get address_auto_tips => '请粘贴或输入文本，点击“识别”自动工厂名称、姓名电话、地址等';

  @override
  String get address_paste => '粘贴';

  @override
  String get address_ocr => '识别';

  @override
  String get address_name => '工厂名称';

  @override
  String get address_name_tip => '请输入验货工厂信息';

  @override
  String get address_person => '联系人';

  @override
  String get address_person_tip => '请输入联系人';

  @override
  String get address_mobile => '手机号';

  @override
  String get address_mobile_tip => '请输入手机号';

  @override
  String get address_email => '邮箱';

  @override
  String get address_email_tip => '请输入邮箱号';

  @override
  String get address_area => '省市区';

  @override
  String get address_area_tip => '请选择省-市-区  〉';

  @override
  String get address_detail => '详细地址';

  @override
  String get address_detail_tip => '输入街道、门牌号等信息';

  @override
  String get address_location => '定位';

  @override
  String get address_save_tip => '保存到地址薄';

  @override
  String get address_clear => '清除';

  @override
  String get address_submit => '提交';

  @override
  String get address_recent => '最近使用地址';

  @override
  String get address_more => '更多地址';

  @override
  String get address_list_title => '地址管理';

  @override
  String get address_insert => '添加地址';

  @override
  String get address_delete => '删除';

  @override
  String get address_delete_result => '删除失败';

  @override
  String get address_edit => '编辑';

  @override
  String get address_delete_tips => '确认删除地址?';

  @override
  String get address_detected_paste => '检测到地址信息，是否使用该地址';

  @override
  String get pay_title => '支付订单';

  @override
  String get pay_time => '支付剩余时间';

  @override
  String get pay_paypal => 'PayPal';

  @override
  String get pay_zfb => '支付宝';

  @override
  String get pay_usd => '美元账户';

  @override
  String get pay_rmb => '人民币账户';

  @override
  String get pay_pay => '支付';

  @override
  String get pay_result_success => '支付成功';

  @override
  String get pay_result_success_wait => '支付成功，等待订单处理中。';

  @override
  String get pay_result_failed => '支付失败';

  @override
  String get pay_keep => '可记账';

  @override
  String get check_title => '验货报告';

  @override
  String get check_picture => '验货图片';

  @override
  String get check_file => '廉政承诺函';

  @override
  String get check_report => '廉政承诺书+手写报告';

  @override
  String get check_draft => '草稿报告';

  @override
  String get check_template => '报告模板';

  @override
  String get check_submit => '提交';

  @override
  String get check_hint => '请输入图片描述';

  @override
  String get check_checking => '报告审核中';

  @override
  String get check_check_success => '报告审核通过';

  @override
  String get check_check_failed => '报告审核失败，请修改内容后重新提交';

  @override
  String get review_title => '服务评价';

  @override
  String get review_next => '去评价';

  @override
  String get contact_bnt => '马上沟通';

  @override
  String get review_score => '服务水平';

  @override
  String get review_score1 => '失望';

  @override
  String get review_score2 => '不满';

  @override
  String get review_score3 => '一般';

  @override
  String get review_score4 => '满意';

  @override
  String get review_score5 => '惊喜';

  @override
  String get review_tips => '多种角度评价，以帮助我们进一步了解验货员的工作能力';

  @override
  String get review_picture => '图片';

  @override
  String get review_submit => '提交';

  @override
  String get setting_title => '设置';

  @override
  String get setting_address => '地址管理';

  @override
  String get setting_clear_cache => '清除缓存';

  @override
  String get setting_clear_success => '清理成功';

  @override
  String get setting_about_us => '关于我们';

  @override
  String get setting_receive_msg => '接收消息提醒';

  @override
  String get setting_version => '版本号';

  @override
  String get setting_check_update => '检查更新';

  @override
  String get setting_login_out => '退出登录';

  @override
  String get setting_login_out_tips => '是否确定退出登录？';

  @override
  String get setting_delete_account_tips => '是否确定删除账号？';

  @override
  String get setting_policy_title => '隐私政策授权提示';

  @override
  String get setting_policy_sub_title => '进入下一步前，请先阅读并同意';

  @override
  String get setting_privacy_policy => '隐私协议';

  @override
  String get setting_user_agreement => '用户协议';

  @override
  String get setting_privacy_content => '欢迎使用验货在线App\n我们非常重视您的隐私和个人信息保护，在您使用本App的过程中，我们会对您的部分个人信息进行收集和使用\n1. 在您同意App隐私政策后，我们将进行集成SDK的初始化工作，会收集您的设备MAC地址、IMSI、Android ID、IP地址、硬件型号、操作系统版本号、唯一设备标识符（IMEI等）、网络设备硬件地址（MAC）、软件版本号、网络接入方式、类型、状态、网络质量数据、操作日志、硬件序列号、服务日志信息等以保障App正常数据统计及安全风控。\n2. 未经您的同意，我们不会从第三方获取、共享或对外提供您的信息。\n3. 您可以访问、更正、删除您的个人信息，我们也将提供注销、投诉方式。\n';

  @override
  String get setting_ihavereadandagreed => '我已阅读并同意';

  @override
  String get setting_policy_tips2 => '请认真阅读并理解';

  @override
  String get wallet_title => '钱包';

  @override
  String get wallet_bill => '账单';

  @override
  String get wallet_rmb_account => '人民币账户';

  @override
  String get wallet_usd_account => '美元账户';

  @override
  String get wallet_account_heading => '提现账户及设置';

  @override
  String get wallet_bank => '银行卡';

  @override
  String get wallet_wechat => '微信';

  @override
  String get wallet_alipay => '支付宝';

  @override
  String get wallet_charge => '充值';

  @override
  String get wallet_cash => '提现';

  @override
  String get wallet_balance => '余额';

  @override
  String get wallet_default_account => '默认账户';

  @override
  String get wallet_set_default_account => '设为默认账户';

  @override
  String get bill_title => '账单';

  @override
  String get bill_out => '支出';

  @override
  String get bill_in => '收入';

  @override
  String get bill_month => '月';

  @override
  String get bill_fenxi => '收支分析';

  @override
  String get bill_unfreeze => '解冻';

  @override
  String get bill_all => '全部账单';

  @override
  String get bill_income => '收入';

  @override
  String get bill_outcome => '支出';

  @override
  String get bill_freeze => '冻结';

  @override
  String get bill_withdraw => '提现';

  @override
  String get bank_title => '我的银行卡';

  @override
  String get bank_add => '添加银行卡';

  @override
  String get add_bank_title => '添加银行卡';

  @override
  String get add_bank_name => '银行卡名称';

  @override
  String get add_bank_branch => '开户行';

  @override
  String get add_bank_card => '卡号';

  @override
  String get add_bank_real_name => '姓名';

  @override
  String get add_bank_address => '开户地址';

  @override
  String bind_title(Object bind) {
    return '绑定$bind';
  }

  @override
  String get bind_account => '账户';

  @override
  String get bind_image => '收款码';

  @override
  String get bind_name => '姓名';

  @override
  String get bind_hint => '请输入';

  @override
  String get charge_title => '充值';

  @override
  String get charge_account => '充值账户';

  @override
  String get charge_money => '充值金额';

  @override
  String get charge_deposit_type_title => '充值方式';

  @override
  String get charge_deposit_type_online => '在线充值';

  @override
  String get charge_deposit_type_offline => '线下转账';

  @override
  String get charge_offline_nopic_hint => '请上传充值凭证';

  @override
  String get charge_upload_proof => '请上传转账凭据';

  @override
  String get withdraw_list_title => '提现历史';

  @override
  String get withdraw_rmb => '人民币提现';

  @override
  String get withdraw_usd => '美元提现';

  @override
  String get withdraw_status_checking => '审核中';

  @override
  String get withdraw_status_approved => '审核通过';

  @override
  String get withdraw_status_denied => '审核不通过';

  @override
  String get withdraw_cash_status_unfinished => '未打款';

  @override
  String get withdraw_cash_status_done => '已打款';

  @override
  String get withdraw_cash_status_refused => '驳回';

  @override
  String get charge_hint => '请输入充值金额';

  @override
  String get charge_submit => '确定';

  @override
  String get charge_rmb => '人民币充值';

  @override
  String get charge_usd => '美元充值';

  @override
  String get charge_history_title => '充值历史';

  @override
  String get cash_title => '提现';

  @override
  String get cash_account => '选择提现账户';

  @override
  String get cash_money => '提现金额';

  @override
  String get cash_invoice_money => '发票金额';

  @override
  String get cash_invoice_money_hint => '请输入发票金额';

  @override
  String get cash_invoice_upload => '发票上传';

  @override
  String get cash_account_list_title => '申请通过后，提现款将随机打入以下账户：';

  @override
  String get cash_hint => '请输入提现金额';

  @override
  String get cash_withdraw_tips1 => '您正在提现至';

  @override
  String get cash_withdraw_tips2 => ', 提现金额为';

  @override
  String get cash_amount => '到账金额';

  @override
  String get cash_other => '手续费';

  @override
  String get cash_submit => '确定';

  @override
  String get location_permission => '需要使用定位权限，请开启';

  @override
  String get location_cancel => '取消';

  @override
  String get location_author => '去授权';

  @override
  String get group_title => '组成员';

  @override
  String get unknown_error => '未知错误';

  @override
  String get data_parsing_exception => '数据解析异常';

  @override
  String get edit => '编辑';

  @override
  String get no_data => '暂无数据';

  @override
  String get note => '备注说明';

  @override
  String get msg_locating => '定位中';

  @override
  String get failed_to_download => '下载更新失败';

  @override
  String get pick_address => '点击输入工厂地址';

  @override
  String get update_now => '立即更新';

  @override
  String get message => '消息';

  @override
  String get view_order => '查看订单';

  @override
  String get today => '今天';

  @override
  String get yesterday => '昨天';

  @override
  String get send_file => '发送文件';

  @override
  String get login_expired => '登录已过期,请重新登录';

  @override
  String get exit_group_chat_confirm => '确定退出群聊?';

  @override
  String get exit_group_chat_success => '已退出群聊';

  @override
  String get exit_group_chat_page_title => '聊天信息';

  @override
  String get exit_group_chat_button_title => '退出群聊';

  @override
  String get group_chat_setting_view_more => '查看更多群成员';

  @override
  String get group_chat_setting_name => '群聊名称';

  @override
  String get group_chat_setting_owner_update => '只允许群主修改群名称';

  @override
  String get group_chat_name_page_title => '修改群聊名称';

  @override
  String get group_chat_name_page_required => '请输入群聊名称';

  @override
  String get group_chat_name_save => '保存';

  @override
  String get group_chat_name_saved => '群聊名称已修改';

  @override
  String get conversation_manage_view_please => '请选择需要操作的会话';

  @override
  String get conversation_manage_view_list => '会话列表';

  @override
  String get group_manage_select => '请选着需要操作的群组';

  @override
  String get group_manage_list => '群组列表';

  @override
  String get please_enter => '请输入';

  @override
  String get address_keyword => '请输入地址关键字';

  @override
  String get inspector_min_fee => '请输入最低验货费用';

  @override
  String get inspector_id_card_required => '请输入身份证号';

  @override
  String get inspector_id_card_upload => '请上传身份证照片';

  @override
  String get inspector_id_card_upload_fail => '身份证照片上传出错,请重新上传';

  @override
  String get inspector_revoke => '确定撤销验货员资格？';

  @override
  String get inspector_revoke_completed => '已撤销验货员资格';

  @override
  String get male => '男';

  @override
  String get female => '女';

  @override
  String get elementary => '小学';

  @override
  String get junior => '初中';

  @override
  String get technical => '中专';

  @override
  String get senior => '高中';

  @override
  String get college => '大专';

  @override
  String get bachelor => '本科';

  @override
  String get master => '硕士';

  @override
  String get doctor => '博士';

  @override
  String get yes => '有';

  @override
  String get no => '无';

  @override
  String get upload_image => '上传图片';

  @override
  String get upload_file => '上传文件';

  @override
  String get revoke_inspector => '撤销验货员资格';

  @override
  String get deposit_card => '储蓄卡';

  @override
  String get withdrawal_balance => '提现金额不能超过账户余额';

  @override
  String get failed_get_payment_info => '获取支付信息失败';

  @override
  String get recommended_order => '推荐下单';

  @override
  String get withdrawal_method => '请提供至少一种提现方式';

  @override
  String get withdrawal_bind_alipay => '请先绑定支付宝';

  @override
  String get enabled_camera => '请设置允许使用相机拍照';

  @override
  String get valid_email_mobile => '请输入正确的邮箱地址或手机号';

  @override
  String get apple_map => '苹果地图';

  @override
  String get baidu_map => '百度地图';

  @override
  String get amap => '高德地图';

  @override
  String get google_map => '谷歌地图';

  @override
  String get tencent_map => '腾讯地图';

  @override
  String get image_format => '图片格式需为png,jpg,jpeg';

  @override
  String get enable_location_service => '需开启定位权限';

  @override
  String get enable_location_service_tips => '开启定位权限，可精准查找周边验货订单';

  @override
  String get enable_permission_not_now => '暂不设置';

  @override
  String get enable_permission_goto_setting => '去设置';

  @override
  String get failed_location_service => '获取位置信息出错';

  @override
  String get turn_on_location_service => '请打开手机定位服务';

  @override
  String get no_install_map => '您未安装地图';

  @override
  String get camera => '相机';

  @override
  String get photo_album => '相册';

  @override
  String get new_version => '版本全新上线';

  @override
  String get invalid_mail => '用户邮箱不存在';

  @override
  String get invalid_password => '密码错误';

  @override
  String get invalid_mobile => '手机号不存在';

  @override
  String get invalid_auth_code => '验证码不正确';

  @override
  String get invalid_login => '登录失败，请重试';

  @override
  String get grabbing => '抢单中';

  @override
  String get hour_ago => '小时前发布';

  @override
  String get minute_ago => '分钟前发布';

  @override
  String get report_type => '报告类型';

  @override
  String get fri => '抽检';

  @override
  String get fui => '';

  @override
  String get oli => '';

  @override
  String get fat => '';

  @override
  String get cls => '监柜';

  @override
  String get fri_cls => '';

  @override
  String get order_payment => '订单支付';

  @override
  String get order_refund => '订单退回';

  @override
  String get expend_withdrawal => '支出-提现';

  @override
  String get incoming_refund => '收入-订单退款';

  @override
  String get incoming_recharge => '收入-充值';

  @override
  String get chat_not_member => '你已经不是群组成员，不能发送信息';

  @override
  String get admins => '联系客服';

  @override
  String get theme_title => '主题';

  @override
  String get theme_light => '亮色主题';

  @override
  String get theme_dark => '暗色主题';

  @override
  String get theme_auto => '跟随系统';

  @override
  String get amount_total => '总额';

  @override
  String get amount_available => '可用';

  @override
  String get amount_blocked => '冻结';

  @override
  String get download => '点击下载';

  @override
  String get downloading => '正在下载';

  @override
  String get saved => '已保存';

  @override
  String get order_number => '订单编号';

  @override
  String get order_detail_inspection_cost => '验货费用';

  @override
  String get delete_account => '删除账号';

  @override
  String get delete_account_confirm => '所有信息都将不会保留。\n确定删除吗？';

  @override
  String get delete_account_result => '账号已删除';

  @override
  String get not_exist_account => '账号不存在';

  @override
  String get new_password => '输入新密码';

  @override
  String get supervisor => '跟单员';

  @override
  String get downloadFiles => '下载的文件';

  @override
  String get home_search_hint_inspector => '按城市/产品名搜索订单';

  @override
  String get home_search_hint_admin => '按城市/产品名搜索订单';

  @override
  String get search_recent_history => '最近搜索';

  @override
  String get assign => '指派';

  @override
  String get assigned => '已指派';

  @override
  String get approve => '通过';

  @override
  String get assign_inspector => '指派验货员';

  @override
  String get unassigned => '未指派';

  @override
  String get general_all => '全部';

  @override
  String get general_date => '日期';

  @override
  String get general_desc => '说明';

  @override
  String get general_amount => '金额';

  @override
  String get assign_search_hint => '请输入昵称/姓名/邮箱/手机号';

  @override
  String get assign_cancel_message => '确认取消指派该验货员';

  @override
  String get assign_inspect_times => '验货次数';

  @override
  String get assign_leave_message_batch => '批量留言';

  @override
  String get assign_price_zero_tips => '验货费用不能为0';

  @override
  String get assign_applied => '已申请';

  @override
  String get is_auth_forbidden => '已禁用';

  @override
  String get apply_time => '申请于';

  @override
  String get assign_message => '留言';

  @override
  String get chat_send_message => '发消息';

  @override
  String get chat_send_order => '发送订单';

  @override
  String get chat_panel_album => '相册';

  @override
  String get chat_panel_camera => '拍照';

  @override
  String get chat_panel_file => '文件';

  @override
  String get chat_toolbar_custom_service => '专属客服';

  @override
  String get chat_toolbar_submit_order => '验货下单';

  @override
  String get home_navigation => '点击导航';

  @override
  String get price_input_error_zero => '订单必须在0元与100万元之间';

  @override
  String get filter_all => '全部筛选';

  @override
  String get filter_heading_order_status => '按订单状态';

  @override
  String get filter_heading_insp_date => '按验货日期';

  @override
  String get filter_heading_order_date => '按发布日期';

  @override
  String get filter_heading_area => '按地区';

  @override
  String get filter_date_start => '起始时间';

  @override
  String get filter_date_end => '截止时间';

  @override
  String get filter_date_today => '今日订单';

  @override
  String get filter_date_tomorrow => '明日订单';

  @override
  String get filter_date_2days_later => '2日内订单';

  @override
  String get filter_date_3days_later => '3日内订单';

  @override
  String get sort_by_order_date => '按订单日期排序';

  @override
  String get sort_by_insp_date => '按验货日期排序';

  @override
  String get sort_by_distance => '按距离排序';

  @override
  String get purchase_all_replies => '全部回复';

  @override
  String get purchase_replies_count => '条回复';

  @override
  String get purchase_no_more_replies => '暂无更多回复';

  @override
  String get purchase_save_draft_title => '是否保存草稿';

  @override
  String get purchase_save_draft_choice => '保存为草稿';

  @override
  String get purchase_save_draft_quit => '直接退出';

  @override
  String get purchase_search_hint => '搜索采购订单';

  @override
  String get purchase_reply_hint => '回复帖子内容';

  @override
  String get purchase_reply_reason_hint => '相似度描述或推荐理由';

  @override
  String get purchase_complaint_hint => '提供更多信息有助于举报被快速处理';

  @override
  String get purchase_reply_paid_hint => '输入悬赏内容';

  @override
  String get purchase_edit => '编辑帖子';

  @override
  String get purchase_publish => '悬赏求购';

  @override
  String get purchase_publish_product_label => '产品名称';

  @override
  String get purchase_publish_title_label => '悬赏标题';

  @override
  String get purchase_publish_quantity => '采购数量';

  @override
  String get purchase_publish_content_label => '详细描述';

  @override
  String get purchase_publish_product_hint => '请输入产品名称';

  @override
  String get purchase_publish_title_hint => '可写明名称型号、地区等信息';

  @override
  String get end_date => '截止日期';

  @override
  String get purchase_area => '采购地区';

  @override
  String get purchase_permission_author_only => '只允许楼主查看回复';

  @override
  String get purchase_publish_quantity_hint => '请输入数量';

  @override
  String get purchase_publish_content_hint => '请输入详细描述';

  @override
  String get purchase_publish_price => '悬赏价格';

  @override
  String get purchase_publish_choose_category => '选择分类';

  @override
  String get purchase_publish_choose_category_hint => '请选择分类';

  @override
  String get purchase_paid_publish_switch => '有偿查看';

  @override
  String get purchase_paid_publish_set_price => '设置价格';

  @override
  String get purchase_detail_response_all => '全部回复';

  @override
  String get purchase_detail_response_author_only => '只看楼主';

  @override
  String get purchase_detail_response_asc => '正序';

  @override
  String get purchase_detail_response_desc => '倒序';

  @override
  String get purchase_detail_more_reply => '回复';

  @override
  String get purchase_detail_more_up => '赞';

  @override
  String get purchase_detail_more_cancel_up => '取消赞';

  @override
  String get purchase_my_posts => '主贴';

  @override
  String get purchase_my_replies => '回复';

  @override
  String get purchase_my_appeals => '申诉';

  @override
  String get purchase_appeal_detail => '申诉详情';

  @override
  String get purchase_appeal_submit => '提交申诉';

  @override
  String get purchase_appeal_cancel => '取消申诉';

  @override
  String get purchase_appeal_approve => '申诉已通过';

  @override
  String get purchase_appeal_denied => '申诉已驳回';

  @override
  String get purchase_paid_content_owner_tips => '付费内容';

  @override
  String get purchase_paid_content_tips => '付费内容，支付查看';

  @override
  String get purchase_paid_content_paid_tips => '付费内容已解锁';

  @override
  String get purchase_review_leave => '评价一下';

  @override
  String get purchase_review_my_score => '我的评价';

  @override
  String get purchase_my_replies_original_header => '原帖';

  @override
  String get purchase_publish_bounty_tips => '注：悬赏金额可自由选填，较高的悬赏金能吸引更多的验货员积极为您提供您需要的信息。';

  @override
  String get purchase_reply_to => '回复';

  @override
  String get purchase_modify_bounty => '修改悬赏';

  @override
  String get purchase_bounty_money => '悬赏';

  @override
  String get purchase_evaluated_person => '人已评价';

  @override
  String get purchase_comment_paid_supplier => '供应商';

  @override
  String get purchase_comment_paid_contact => '联系人';

  @override
  String get purchase_comment_paid_phone => '联系电话';

  @override
  String get purchase_comment_paid_email => '邮箱';

  @override
  String get purchase_comment_paid_address => '工厂地址';

  @override
  String get purchase_comment_paid_other => '其它';

  @override
  String get purchase_comment_paid_low_price => '产品底价';

  @override
  String get purchase_appeal_title => '申请退款';

  @override
  String get purchase_appeal_reason => '请输入申诉理由';

  @override
  String get purchase_appeal_request_price => '申诉金额：';

  @override
  String get purchase_appeal_request_reason => '申诉理由：';

  @override
  String get purchase_post_status_draft => '草稿';

  @override
  String get purchase_post_status_reviewing => '审核中';

  @override
  String get purchase_post_status_published => '审核通过';

  @override
  String get purchase_post_status_denied => '未通过';

  @override
  String get purchase_post_publish => '发布帖子';

  @override
  String get purchase_complaint_type_leading => '请选择举报类型';

  @override
  String get purchase_complaint_type_1 => '色情低俗';

  @override
  String get purchase_complaint_type_2 => '垃圾广告';

  @override
  String get purchase_complaint_type_3 => '辱骂攻击';

  @override
  String get purchase_complaint_type_4 => '违法犯罪';

  @override
  String get purchase_complaint_type_5 => '时政不实信息';

  @override
  String get purchase_complaint_type_6 => '侵犯权益';

  @override
  String get purchase_complaint_type_7 => '其他';

  @override
  String get shop_goods_detail_title => '商品详情';

  @override
  String get mall_buy_immediate => '立即购买';

  @override
  String get mall_goods_count => '数量';

  @override
  String get mall_confirm_pay => '确认支付';

  @override
  String get mall_order_confirm => '订单确认';

  @override
  String get mall_submit_order => '提交订单';

  @override
  String get mall_goods_price => '商品金额';

  @override
  String get mall_express_price => '运费';

  @override
  String get mall_price_total => '合计：';

  @override
  String get mall_payment => '收银台';

  @override
  String get mall_payment_methods => '支付方式';

  @override
  String get mall_pay_succeed => '支付成功';

  @override
  String get mall_check_order_detail => '查看订单详情';

  @override
  String get mall_order_remark => '订单备注';

  @override
  String get mall_order_remark_input => '填写备注';

  @override
  String get purchase_detail_more_report => '举报';

  @override
  String get purchase_reply_paid_content_tips => '注：请填写真实信息，有偿回复发表后需等待审核，审核之后才能被他人查看到';

  @override
  String get public_ip_address => 'IP属地:';

  @override
  String get inspection_widget_suit_tips => '验货时需佩戴工牌或穿工作服，若还没有可在首页进入购买';

  @override
  String get purchase_paid_content_appeal => '申诉';

  @override
  String get report_success => '举报成功';

  @override
  String tabbar_tab_names(String names) {
    String _temp0 = intl.Intl.selectLogic(
      names,
      {
        'tab_home': '首页',
        'tab_shortcut': '快捷',
        'tab_message': '消息',
        'tab_mine': '我的',
        'tab_publish': '',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String order_status_desc(String status) {
    String _temp0 = intl.Intl.selectLogic(
      status,
      {
        'order_status_unknown': '未知',
        'order_wait_pay': '待支付',
        'order_cancelled': '已取消',
        'order_cancelled_refund_pending': '已取消,退款待审核',
        'order_refund_pending': '退款待审核',
        'order_refund_partial': '部分退款',
        'order_refund_denied': '拒绝退款',
        'order_wait_dispatch': '待派单',
        'order_doing': '验货中',
        'order_finished': '已完成',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String pay_type(String type) {
    String _temp0 = intl.Intl.selectLogic(
      type,
      {
        'pay_rmb': '人民币账户',
        'pay_usd': '美元账户',
        'pay_zfb': '支付宝',
        'pay_paypal': 'PayPal',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String purchase_complaint_type(String type) {
    String _temp0 = intl.Intl.selectLogic(
      type,
      {
        'type_1': '色情低俗',
        'type_2': '垃圾广告',
        'type_3': '辱骂攻击',
        'type_4': '违法犯罪',
        'type_5': '时政不实信息',
        'type_6': '侵犯权益',
        'type_7': '其他',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String get purchase_reply_paid_my_evaluate => '（我的评价）';

  @override
  String get bind_now => '立即绑定';

  @override
  String get cancel_register => '取消注册';

  @override
  String register_and_bind_email0(Object email) {
    return '注册并绑定$email为主邮箱';
  }

  @override
  String register_and_bind_email1(Object email) {
    return '您的邮箱$email已注册，继续绑定';
  }

  @override
  String get register_and_bind_email2 => '注册并绑定主邮箱';

  @override
  String get new_register_bind_title => '设置主登陆邮箱并注册';

  @override
  String get new_register_bind_field_account_tips => '绑定邮箱/手机号';

  @override
  String get register => '注册';

  @override
  String get switch_account => '切换账号';

  @override
  String get switch_account_confirm_tips => '确定切换账户?';

  @override
  String get password_must_have => '您的密码必须包含：';

  @override
  String get password_must_have_1 => '长度为 8-32 个字符';

  @override
  String get password_must_have_2 => '1 个小写字母 (a-z)';

  @override
  String get password_must_have_3 => '1 个数字';

  @override
  String get password_must_have_4 => '1 个符号 (如!@#\\\$%^&*)';

  @override
  String get password_login => '输入登录密码';

  @override
  String get password_login_again => '再次输入新密码';

  @override
  String get choose_account_to_login => '选择账号快速登录';

  @override
  String get finish => '完成';

  @override
  String get done => '完成';

  @override
  String get account_apple => '苹果登录';

  @override
  String get account_google => '谷歌登录';

  @override
  String get account_wechat => '微信登录';

  @override
  String get account_facebook => 'Facebook登录';

  @override
  String get third_account_unbind => '解除绑定';

  @override
  String get third_account_bind => '绑定';

  @override
  String get confirm_unbind => '确定解绑?';

  @override
  String get inspection_requirement => '验货须知';

  @override
  String get liveroom_entrance => '直播间排单';

  @override
  String get add_account => '新增账户';

  @override
  String get message_order => '订单';

  @override
  String get message_email => '邮件';

  @override
  String get message_wallet => '钱包';

  @override
  String get message_user => '用户信息';

  @override
  String get salesman => '跟单员';

  @override
  String get public_continue => '继续';

  @override
  String get camera_permission_tips => '为方便您使用App时可以正常上传图片、文件或拍照上传，需要您允许使用照相机、存储权限';

  @override
  String get ai_category_inspector => '验货';

  @override
  String get ai_nothing_category => '没有可检测类目';

  @override
  String get ai_category_name => '验货首页';

  @override
  String get ai_quantity => '数量';

  @override
  String get ai_packaging => '包装';

  @override
  String get ai_shipping_mark => '唛头';

  @override
  String get ai_product_style => '产品样式';

  @override
  String get ai_test => '测试';

  @override
  String get ai_craftsmanship => '工艺';

  @override
  String get ai_test_verification => '测试校验';

  @override
  String get ai_category_measure => '测量';

  @override
  String get ai_spare_parts => '备件';

  @override
  String get ai_sampling_number => '抽样编号';

  @override
  String ai_input_range_number(Object range) {
    return '可输入$range内的数字';
  }

  @override
  String ai_enter_range_number(Object range) {
    return '请输入$range内的数字';
  }

  @override
  String get ai_selected => '选中';

  @override
  String get ai_selected_status => '已选中';

  @override
  String get ai_order_quantity => '订单数量';

  @override
  String get ai_packaged_boxes_quantity => '已包装箱数量(成品)';

  @override
  String get ai_unpackaged_boxes_quantity => '未包装箱数量(成品)';

  @override
  String get ai_sample_from_packaged => '从已包装里抽样';

  @override
  String get ai_sample_from_unpackaged => '从未包装里抽样';

  @override
  String get ai_spare_parts_quantity => '备件数量';

  @override
  String get ai_sampling_packaging_number => '抽样包装编号';

  @override
  String get ai_sampling_packaging_number_record => '抽样编号记录';

  @override
  String get ai_sampling_packaging_number_list => '记录抽样外箱的编号';

  @override
  String get ai_judgment => '判定';

  @override
  String get ai_judgment_item => '判定项';

  @override
  String get ai_standard => '标准';

  @override
  String get ai_result => '结果';

  @override
  String get ai_conclusion => '结论';

  @override
  String get ai_overall_conclusion => '整体结论';

  @override
  String get ai_consistency => '是否一致';

  @override
  String get ai_yes => '是';

  @override
  String get ai_no => '否';

  @override
  String get ai_remarks => '备注';

  @override
  String get ai_numerical => '序号';

  @override
  String get ai_recommended_test_items => '推荐检测项';

  @override
  String get ai_test_item => '检测项';

  @override
  String get ai_add_all => '一键添加';

  @override
  String get ai_add_plus => '+添加';

  @override
  String get ai_add => '添加';

  @override
  String ai_confirm_delete(Object name) {
    return '您确定要删除$name吗';
  }

  @override
  String get ai_enter_test_item => '请输入检测项';

  @override
  String get ai_defect_record => '缺陷记录';

  @override
  String get ai_defect_photo => '缺陷拍照';

  @override
  String get ai_defect_description => '缺陷描述';

  @override
  String get ai_defect_level => '缺陷等级';

  @override
  String get ai_found_quantity => '发现数量';

  @override
  String get ai_handling_method => '处理方式';

  @override
  String get ai_edit => '修改';

  @override
  String get ai_delete => '删除';

  @override
  String get ai_pick_out => '挑出';

  @override
  String get ai_replace => '替换';

  @override
  String get ai_rework => '返工';

  @override
  String get ai_edit_description => '修改描述';

  @override
  String get ai_critical => '致命';

  @override
  String get ai_important => '严重';

  @override
  String get ai_minor => '轻微';

  @override
  String get ai_defect_list => '缺陷列表';

  @override
  String get ai_test_level => '测试等级';

  @override
  String get ai_sampling_sample => '抽样样本';

  @override
  String get ai_sampling_level => '抽样等级';

  @override
  String get ai_additional_information => '补充说明';

  @override
  String get ai_inspection_record => '检测记录';

  @override
  String get ai_sample_count => '样本数';

  @override
  String get ai_maximum_allowable_value => '最大允许值';

  @override
  String get ai_test_item_name => '测试项';

  @override
  String get ai_test_result => '测试结果';

  @override
  String get ai_basic_information => '基本信息';

  @override
  String get ai_new_test_item => '新建测试项';

  @override
  String get ai_test_project => '测试项目';

  @override
  String get ai_measurement_project => '测量项目';

  @override
  String get ai_measure_need_num => '个数要求';

  @override
  String get ai_measurement_unit => '测量单位';

  @override
  String get ai_measurement_method => '测量方式';

  @override
  String get ai_measurement_record => '测量记录';

  @override
  String get ai_measured => '已测量';

  @override
  String get ai_unit_of_measurement => '计量单位';

  @override
  String get ai_measured_value => '测量值';

  @override
  String get ai_product_number => '产品编号';

  @override
  String get ai_number => '编号';

  @override
  String get ai_new_measurement_item => '新建测量项';

  @override
  String get ai_length_width_height => '长宽高';

  @override
  String get ai_dimensions_length => '长';

  @override
  String get ai_dimensions_width => '宽';

  @override
  String get ai_dimensions_height => '高';

  @override
  String get ai_length_width => '长宽';

  @override
  String get ai_other => '其它';

  @override
  String get ai_allowable_error => '允许误差';

  @override
  String get ai_report_summary => '报告总结';

  @override
  String get ai_special_note => '特别注意';

  @override
  String get ai_overall_conclusion_2 => '总体结论';

  @override
  String get ai_summary => '概要';

  @override
  String get ai_category_name_table => '检测类目';

  @override
  String get ai_compliance => '是否符合';

  @override
  String get ai_remarks_2 => '备注';

  @override
  String get ai_defect_summary => '缺陷汇总';

  @override
  String get ai_no_guidance_instructions => '暂无指导说明';

  @override
  String get ai_no_standard_instructions => '暂无标准说明';

  @override
  String get ai_please_fill_in => '请填写';

  @override
  String ai_please_supplement_level_or_sample(Object level, Object sample) {
    return '请补充$level或$sample';
  }

  @override
  String get ai_please_add => '请添加';

  @override
  String get ai_please_input => '请输入';

  @override
  String get ai_please_select => '请选择';

  @override
  String ai_name_not_filled(Object name) {
    return '$name未填写';
  }

  @override
  String get ai_addition_successful => '添加成功';

  @override
  String get ai_confirm_action => '确定';

  @override
  String get ai_cancel_action => '取消';

  @override
  String get ai_submit => '提交';

  @override
  String get ai_next_item => '下一项';

  @override
  String get ai_complete => '完成';

  @override
  String get ai_change_description => '修改描述';

  @override
  String get ai_action_guidance_instructions => '操作指导';

  @override
  String get ai_action_standard_instructions => '行业标准';

  @override
  String get ai_add_description => '添加描述';

  @override
  String get ai_change_description_note => '注意：下方为已发现的缺陷，如果修改，历史数据也将采用新描述！';

  @override
  String get ai_packing_completion_rate => '装箱完成率';

  @override
  String get ai_unprocessed_quantity => '未生产完成数量';

  @override
  String get ai_sample_level_type_0 => 'I级';

  @override
  String get ai_sample_level_type_1 => 'II级';

  @override
  String get ai_sample_level_type_2 => 'III级';

  @override
  String get ai_sample_level_type_s1 => 'S-1';

  @override
  String get ai_sample_level_type_s2 => 'S-2';

  @override
  String get ai_sample_level_type_s3 => 'S-3';

  @override
  String get ai_sample_level_type_s4 => 'S-4';

  @override
  String get ai_sample_level_type_nothing => '自定义';

  @override
  String get ai_inspection_image => '图片';

  @override
  String get ai_photo_confirm => '确认';

  @override
  String get ai_add_product_ask_save => '是否需要保存本次编辑？';

  @override
  String get ai_add_product_save => '保存';

  @override
  String get ai_add_product_edit_model => '型号编辑';

  @override
  String get ai_add_product_model_name => '型号名称';

  @override
  String get ai_add_product_input_model => '输入型号名称';

  @override
  String get ai_add_product_num => '数量';

  @override
  String get ai_add_product_input_num => '输入型号数量';

  @override
  String get ai_add_product_unit => '单位';

  @override
  String get ai_add_product_ask_delete => '是否需要删除该型号?';

  @override
  String get ai_add_product_edit_product => '产品编辑';

  @override
  String get ai_add_product_product_name => '产品名称';

  @override
  String get ai_add_product_model => '型号';

  @override
  String get ai_add_product_input_product_name => '输入产品名称';

  @override
  String get ai_add_product_new_model => '新增型号';

  @override
  String get ai_add_product_ask_product => '是否需要删除该产品及所有型号?';

  @override
  String get ai_add_product_picture_lost => '图片缺失';

  @override
  String ai_add_product_lost_des(Object lostStr) {
    return '当前缺失$lostStr，请将信息补充完整后，再进行验货';
  }

  @override
  String get ai_add_product_model_full => '产品型号名称';

  @override
  String get ai_add_product_model_title => '产品型号';

  @override
  String get ai_add_product_new => '添加产品';

  @override
  String get ai_model_unit_piece => '个';

  @override
  String get ai_model_unit_only => '只';

  @override
  String get ai_model_unit_item => '件';

  @override
  String get ai_model_unit_pair => '双';

  @override
  String get ai_model_unit_set => '套';

  @override
  String get ai_model_unit_dozen => '打';

  @override
  String get ai_model_unit_roll => '卷';

  @override
  String get ai_model_unit_vehicle => '辆';

  @override
  String get ai_model_unit_head => '头';

  @override
  String get ai_model_unit_bag => '袋';

  @override
  String get ai_model_unit_box => '箱';

  @override
  String get ai_model_unit_pack => '包';

  @override
  String get ai_model_unit_yard => '码';

  @override
  String get ai_model_unit_meter => '米';

  @override
  String get ai_model_unit_kilogram => '千克';

  @override
  String get ai_model_unit_metric_ton => '公吨';

  @override
  String get ai_model_unit_liter => '公升';

  @override
  String get ai_model_unit_gallon => '加仑';

  @override
  String get ai_model_unit_other => '其它';

  @override
  String get ai_default_config_des => '当前产品暂无检测模板，你可以选择下方模板，或致电（+86）进行模板配置。';

  @override
  String get ai_default_config_category_all => '分类(全部)';

  @override
  String get ai_default_config_select_template => '配置模版';

  @override
  String get ai_default_config_template_selection => '模版选择';

  @override
  String get ai_default_config_search_template => '搜索模版';

  @override
  String get ai_default_config_classify => '分类';

  @override
  String get ai_default_config_preview => '预览';

  @override
  String get ai_default_config_use => '应用';

  @override
  String get ai_default_config_current_use_button => '应用到本产品';

  @override
  String get ai_default_config_more_use_button => '应用到更多产品';

  @override
  String get ai_default_config_product_list => '产品列表';

  @override
  String get ai_default_config_use_warning => '注：【模】产品已经载入模板；【运】运营已经配置模板。如果重新载入新模板将会覆盖之前数据。';

  @override
  String get ai_default_config_tag_default => '运';

  @override
  String get ai_default_config_tag_manual => '模';

  @override
  String get ai_default_config_load_progress => '载入进度';

  @override
  String ai_default_config_template_progress(Object name) {
    return '模版载入完成 $name。';
  }

  @override
  String ai_default_config_template_fail_count(Object name) {
    return '失败$name个，点击重试';
  }

  @override
  String get ai_default_config_load => '载入';

  @override
  String get ai_default_config_success => '成功';

  @override
  String get ai_default_config_fail => '失败';

  @override
  String get ai_default_config_template => '模板';

  @override
  String get ai_add_model_count_warning => '数量必须大于0';

  @override
  String get ai_default_config_product_edit => '编辑产品';

  @override
  String get ai_wait => '待定';

  @override
  String get ai_product_info => '产品信息';

  @override
  String get ai_product_category => '产品分类';

  @override
  String get ai_product_unit => '产品单位';

  @override
  String get ai_package_num_done => '已包装箱数';

  @override
  String get ai_product_full => '补充信息';

  @override
  String get ai_product_full_tip => '订单中的产品信息不完善，请根据验货现场中的实际产品信息进行补充。';

  @override
  String get ai_each_box => '每箱';

  @override
  String get ai_simple_count => '抽样样本数';

  @override
  String get ai_simple_level => '抽样标准';

  @override
  String get ai_simple_num => '抽样数量';

  @override
  String get ai_simple_no => '抽样外箱编号';

  @override
  String get ai_simple_result => '结果判定';

  @override
  String get ai_simple_project => '检查项目';

  @override
  String get ai_simple_project_manage => '管理检查项目';

  @override
  String get ai_simple_project_edit => '编辑检查项目';

  @override
  String get ai_simple_project_recmend => '智能推荐';

  @override
  String get ai_simple_project_input => '填写检查记录';

  @override
  String get ai_simple_help => '帮助';

  @override
  String get ai_simple_project_record => '检查记录';

  @override
  String get ai_simple_require => '客户要求';

  @override
  String get ai_simple_record => '记录';

  @override
  String get ai_simple_dsec => '描述';

  @override
  String get ai_simple_before => '上一项';

  @override
  String get ai_simple_add => '添加一组';

  @override
  String get ai_simple_add_desc => '为照片添加描述信息';

  @override
  String get ai_simple_add_citations => '次引用';

  @override
  String get ai_no_more => '没有更多数据了';

  @override
  String get ai_wrong_tip => '数量不能大于总数';

  @override
  String get ai_defect_records => '缺陷记录';

  @override
  String get ai_check_require => '抽样要求';

  @override
  String get ai_find_defect => '发现缺陷';

  @override
  String get ai_defect_question => '缺陷问题';

  @override
  String get ai_modify_level => '修改抽样等级';

  @override
  String get ai_defect_quick => '快捷添加工艺缺陷';

  @override
  String get ai_defect_self => '自定义缺陷名称';

  @override
  String get ai_defect_record_list => '瑕疵记录列表';

  @override
  String get ai_measure_require => '测量要求';

  @override
  String get ai_measurement_item => '测量项';

  @override
  String get ai_measurement_error => '误差';

  @override
  String get ai_measurement_standard => '测量标准';

  @override
  String get ai_measurement_value_standard => '标准值';

  @override
  String get ai_measurement_camera => '测量拍照';

  @override
  String get ai_measurement_add => '快捷添加测量标准';

  @override
  String get ai_product_first => '产品首图';

  @override
  String get ai_product_report => '保存并生成报告';

  @override
  String get ai_product_report_tip => '请选择产品首图';

  @override
  String get ai_product_report_special => '请输入需要特别注意的内容';

  @override
  String get ai_product_report_sign => '签名';

  @override
  String get ai_product_report_sign_done => '签名完成';

  @override
  String get ai_defect_names => '缺陷名称';

  @override
  String get ai_input_tip => '请输入名称';

  @override
  String get ai_add_measure_tip => '请先添加测量标准';

  @override
  String get ai_wrong_num => '数量错误';

  @override
  String get ai_wrong_name => '请输入产品名称';

  @override
  String get ai_wrong_sample_num => '不能大于抽样样本数';

  @override
  String get ai_per_box => '每箱数量';

  @override
  String get ai_wrong_sample_num_cal => '已包装抽样+未包装抽样需要等于抽样样本数';

  @override
  String get ai_sure_delete => '确定删除吗？';

  @override
  String get ai_choose_tip => '请选择缺陷处理方式和个数';

  @override
  String get ai_weight => '毛重';

  @override
  String get sampling_plan => '抽样计划';

  @override
  String get single => '单个';

  @override
  String get normal => '普通';

  @override
  String get summarize => '总结';

  @override
  String get po_number => 'PO号';

  @override
  String get product_quantity => '产品数量';

  @override
  String get customer_name => '客户名称';

  @override
  String get supplier_name => '供应商名称';

  @override
  String get inspection_date => '验货日期';

  @override
  String get arrival_time => '到达时间';

  @override
  String get completion_time => '完成时间';

  @override
  String get inspection_address => '验货地址';

  @override
  String get inspector => '验货员';

  @override
  String get inspection_report_note => '本验货报告只提供参考，是否确定通过等待客户确定';

  @override
  String get remark_toast => '请先填写备注';

  @override
  String get process_appearance_judgment => '工艺外观判定';

  @override
  String get test_validation_judgment => '测试校验判定';

  @override
  String check_save(Object name) {
    return '您确定要保存$name吗？';
  }

  @override
  String get select_template_config_tip => '若需要配置验货模板，请联系您的跟单员';
}

/// The translations for Chinese, as used in China (`zh_CN`).
class SZhCn extends SZh {
  SZhCn(): super('zh_CN');

  @override
  String get app_name => '验货在线';

  @override
  String get search => '搜索';

  @override
  String get shortcut_tab_name => '快捷导航';

  @override
  String get loading => '加载中...';

  @override
  String get nomore => '暂无更多内容';

  @override
  String get confirm => '确定';

  @override
  String get more_replies => '更多回复';

  @override
  String get purchase_paid_publish_information_title => '以下是客户付费查看的内容';

  @override
  String get purchase_set_fee => '设置查看费用';

  @override
  String get purchase_comment_paid_supplier_hint => '请输入供应商名称';

  @override
  String get purchase_comment_paid_contact_hint => '请输入联系人姓名';

  @override
  String get purchase_comment_paid_phone_hint => '请输入联系人电话';

  @override
  String get purchase_comment_paid_email_hint => '请输入联系人邮箱';

  @override
  String get purchase_comment_paid_address_hint => '请输入工厂地址';

  @override
  String get purchase_comment_paid_other_hint => '其它信息（选填）';

  @override
  String get purchase_comment_paid_low_price_hint => '请输入产品底价（选填）';

  @override
  String get purchase_reply_paid_title => '付费回复';

  @override
  String get purchase_reply_paid_desc => '（供应商信息及商品参考价格）';

  @override
  String purchase_reply_paid_evaluated_people(Object people_num) {
    return '（$people_num人已评价）';
  }

  @override
  String get language_setting => '语言设置';

  @override
  String get public_and => '与';

  @override
  String get public_publish => '发布';

  @override
  String get public_distance => '距离';

  @override
  String get public_deny => '拒绝';

  @override
  String get public_see_more => '查看更多';

  @override
  String get general_select => '选择';

  @override
  String get language_page_title => '多语言';

  @override
  String get language_chinese => '中文';

  @override
  String get language_english => '英文';

  @override
  String get public_seconds_ago => '秒前';

  @override
  String get public_minutes_ago => '分钟前';

  @override
  String get public_hours_ago => '小时前';

  @override
  String get public_status_applied => '已申请';

  @override
  String get public_status_refused => '已拒绝';

  @override
  String get public_status_approved => '已通过';

  @override
  String get public_status_canceled => '已取消';

  @override
  String get public_app_name => 'Inspector';

  @override
  String get public_send => '发送';

  @override
  String get public_ok => '确定';

  @override
  String get public_price => '价格';

  @override
  String get public_cancel => '取消';

  @override
  String get public_manage => '管理';

  @override
  String get public_finish => '完成';

  @override
  String get public_reset => '重置';

  @override
  String get public_leave_message => '留言';

  @override
  String get public_share => '分享';

  @override
  String get login_submit => '登录';

  @override
  String get login_mobile_login => '手机登录';

  @override
  String get login_mobile_tips => '请输入手机号';

  @override
  String get login_email_login => '邮箱登录';

  @override
  String get login_email_tips => '请输入邮箱';

  @override
  String get registry_email_phone_tips => '请输入邮箱或手机号';

  @override
  String get login_verify_tips => '输入验证码';

  @override
  String get login_password_tips => '输入密码';

  @override
  String get login_password_login => '使用密码登录';

  @override
  String get login_verify_login => '使用验证码登录';

  @override
  String get login_register => '注册';

  @override
  String get login_take_code => '获取验证码';

  @override
  String get login_forget_password => '忘记密码';

  @override
  String get login_agreement => '同意《inspector.Itd协议》';

  @override
  String get login_area_selected => '选择国区';

  @override
  String get tab_home => '首页';

  @override
  String get tab_order => '订单';

  @override
  String get tab_shortcut => '快捷';

  @override
  String get tab_purchase => '采购';

  @override
  String get tab_message => '消息';

  @override
  String get tab_mine => '我的';

  @override
  String get supplement_title => '请先完善个人信息';

  @override
  String get supplement_next => '去完善';

  @override
  String get home_title => '验货广场';

  @override
  String get home_record => '申请记录';

  @override
  String get home_newest => '最新验货';

  @override
  String get home_nearest => '附近验货';

  @override
  String home_recommend(Object money) {
    return '推荐奖励 RMB$money元';
  }

  @override
  String get home_sampling => '抽样检验';

  @override
  String get home_word => 'WORD报告';

  @override
  String home_unit(Object day, Object people) {
    return '$people人/$day天';
  }

  @override
  String get home_product_tip => '产品：';

  @override
  String get home_person_apply => '人申请';

  @override
  String get home_know_tip => '验货需知';

  @override
  String get home_inspection_tip => '验货费用默认为商定价，可修改，费用低可能优先指派';

  @override
  String get home_reviewed => '我已查看并遵守';

  @override
  String get home_apply => '申请';

  @override
  String get home_apply_price => '请输入金额￥';

  @override
  String get home_apply_check => '请查看验货须知';

  @override
  String get home_apply_tips => '您不是验货员，若您有1年以上外贸验货工作经验，请提相关资历证明';

  @override
  String get home_complete_profile_tips => '完善验货员档案等相关资料，可以提高验货申请通过率';

  @override
  String get home_apply_sure => '提交审核';

  @override
  String get home_complete_profile_sure => '去完善';

  @override
  String get home_apply_cancel => '取消申请';

  @override
  String get home_update => '修改';

  @override
  String get home_navi => '导航';

  @override
  String get mine_unauth => '未认证';

  @override
  String get mine_checking => '待审核';

  @override
  String get mine_check_failed => '审核失败';

  @override
  String get mine_vip_level => 'VIP等级';

  @override
  String get mine_credit_quota => '信用额度';

  @override
  String get mine_authed => '修改认证信息';

  @override
  String get mine_authed_inspector => '修改验货员信息';

  @override
  String get mine_amount => '账户金额';

  @override
  String get mine_cash => '充值/提现';

  @override
  String get mine_order => '我的订单';

  @override
  String get mine_purchase => '我的采购';

  @override
  String get mine_check => '我的验货';

  @override
  String get mine_address => '地址簿(供应商)';

  @override
  String get mine_recommend => '推荐';

  @override
  String get mine_setting => '设置';

  @override
  String get mine_header_inspect => '验货管理';

  @override
  String get mine_header_purchase => '采购管理';

  @override
  String get mine_header_other => '其他';

  @override
  String get mine_inspect_mine => '我的验货';

  @override
  String get mine_inspect_order => '订单管理';

  @override
  String get mine_inspect_history => '申请记录';

  @override
  String get mine_purchase_mine => '我的采购';

  @override
  String get mine_purchase_reply => '回复记录';

  @override
  String get mine_purchase_appeal => '申诉管理';

  @override
  String get mine_other_recommend => '推荐';

  @override
  String get mine_other_address => '地址簿(供应商)';

  @override
  String get mine_other_settings => '设置';

  @override
  String get profile_title => '个人信息';

  @override
  String get profile_avatar => '头像';

  @override
  String get profile_name => '昵称';

  @override
  String get profile_mobile => '手机号';

  @override
  String get profile_country => '国家';

  @override
  String get profile_real_name => '真实姓名';

  @override
  String get profile_city => '城市';

  @override
  String get profile_email => '邮箱';

  @override
  String get profile_wechat => '微信';

  @override
  String get profile_bind_manage => '账号绑定管理';

  @override
  String get profile_info_failed => '信息更新失败';

  @override
  String get apply_title => '验货员资格申请';

  @override
  String get apply_nick => '昵称';

  @override
  String get apply_sex => '性别';

  @override
  String get apply_birthday => '生日';

  @override
  String get apply_education => '学历';

  @override
  String get apply_address => '常住地';

  @override
  String get apply_price => '最低验货费用';

  @override
  String get apply_shebao => '社保';

  @override
  String get apply_id_card => '身份证号';

  @override
  String get apply_file => '编辑简历';

  @override
  String get apply_file_tip => '请输入自己的基本资料和经历';

  @override
  String get apply_upload_file => '上传简历';

  @override
  String get apply_upload_file_failed => '上传简历失败';

  @override
  String get apply_upload_card => '上传身份证照片';

  @override
  String get apply_card_front => '正面照片(人脸面)';

  @override
  String get apply_card_back => '反面照片(国徽面)';

  @override
  String get apply_submit => '提交';

  @override
  String get apply_enter => '请输入';

  @override
  String get apply_next_tip => '请信息确认完善后再提交';

  @override
  String get apply_auth_failed => '身份证验证失败, 请传入正确的照片';

  @override
  String get apply_checking => '身份待审核';

  @override
  String get apply_check_success => '身份审核通过';

  @override
  String get apply_check_failed => '审核失败，请修改内容后重新提交';

  @override
  String get order_title => '我的订单';

  @override
  String get order_input => '我的验货';

  @override
  String get order_output => '发布订单';

  @override
  String get order_all => '全部';

  @override
  String get order_wait_pay => '待支付';

  @override
  String get order_cancelled => '已取消';

  @override
  String get order_status => '订单状态';

  @override
  String get order_status_unknown => '未知';

  @override
  String get order_refund_pending => '退款待审核';

  @override
  String get order_cancelled_refund_pending => '已取消,退款待审核';

  @override
  String get order_refund_partial => '部分退款';

  @override
  String get order_refund_denied => '拒绝退款';

  @override
  String get order_wait_dispatch => '待派单';

  @override
  String get order_ready_inspect => '准备验货';

  @override
  String get order_need_pay => '支付';

  @override
  String get order_wait => '准备验货';

  @override
  String get order_confirm => '派单确认';

  @override
  String get order_doing => '验货中';

  @override
  String get order_comment => '待评价';

  @override
  String get order_finished => '已完成';

  @override
  String get order_goods_info => '产品信息';

  @override
  String get order_goods_name => '产品名称';

  @override
  String get order_goods_model => '产品型号';

  @override
  String get order_goods_count => '数量';

  @override
  String get order_goods_unit => '单位';

  @override
  String get order_order_time => '订单时间';

  @override
  String get order_order_amount => '订单金额';

  @override
  String get order_detail_title => '订单详情';

  @override
  String get order_applying => '已申请';

  @override
  String get order_apply_expired => '已过期';

  @override
  String get order_apply_dispatched => '已派单';

  @override
  String get order_create_time => '下单时间';

  @override
  String get order_look => '查看验货报告';

  @override
  String get order_report_next => '提交验货报告';

  @override
  String get order_detail_inspection_info => '验货信息';

  @override
  String get order_inspection_status_unpaid => '未付款';

  @override
  String get order_inspection_status_returned => '已退回';

  @override
  String get order_inspection_status_waiting_start => '等待开始';

  @override
  String get order_detail_related_info => '关联子订单';

  @override
  String get order_detail_inspection_product => '产品名称';

  @override
  String get order_detail_inspection_time => '验货时间';

  @override
  String get order_detail_inspection_city => '验货城市';

  @override
  String get order_detail_inspection_factory => '验货工厂';

  @override
  String get order_detail_inspection_address => '验货地址';

  @override
  String get order_detail_inspection_person => '联系人';

  @override
  String get order_detail_inspection_phone => '联系人电话';

  @override
  String get order_detail_inspection_email => '联系人邮箱';

  @override
  String get order_detail_inspection_amount => '价格信息';

  @override
  String get order_detail_inspection_sample => '样品';

  @override
  String get order_detail_inspection_standard => '抽样等级';

  @override
  String get order_detail_inspection_critical => 'AQL';

  @override
  String get order_detail_inspection_major => 'Major';

  @override
  String get order_detail_inspection_minor => 'Minor';

  @override
  String get order_detail_inspection_day => '验货天数';

  @override
  String get order_detail_inspection_number => '验货人数';

  @override
  String get order_detail_inspection_price => '验货价格';

  @override
  String get order_detail_inspection_price_apply => '申请价格';

  @override
  String get order_detail_inspection_template => '验货模板';

  @override
  String get order_detail_inspection_file => '附件';

  @override
  String get order_detail_inspection_image => '图片';

  @override
  String get order_detail_inspection_type => '验货类型';

  @override
  String get order_tips => '样品及注意事项';

  @override
  String get order_apply => '申请验货';

  @override
  String get order_cancel => '取消';

  @override
  String get order_canceled => '已取消';

  @override
  String get order_dispatch_accept => '同意';

  @override
  String get order_dispatch_refuse => '拒绝';

  @override
  String get order_inspection => '开始验货';

  @override
  String get order_sure_cancel => '确定取消订单';

  @override
  String get order_sure_refuse => '确定拒绝此订单吗？';

  @override
  String get order_sure_confirm => '确定接受此订单吗？';

  @override
  String get publish_title => '发布订单';

  @override
  String get publish_edit_title => '编辑订单';

  @override
  String get publish_welcome => '欢迎下单，报价自由，与验货员沟通自由';

  @override
  String get publish_sampling => '抽样检验';

  @override
  String get publish_sampling_content => '适用于产品100%完成，至少80%产品已包装好，准备出货前，我们根据国际抽样方案ANSI/ASQZ1.4(MIL-STD-105E)以及参照您的特殊要求进行随机抽样检验，在抽样检验报告中，我们将全面反映产品的完成数量，包装情况以及是否符合AQL（可接受质量水平）要求，让您在出货前对整批次的产品质量有全面的了解，避免让您的订单遭受任何风险';

  @override
  String get publish_point => '检验要点';

  @override
  String get publish_sampling_point => '● 客户资料/样品核对\n● 完成数量核对\n● 产品尺寸、款式、颜色核对\n● 外观工艺检查\n● 产品功能和安全性检测\n● 箱唛检查\n● 包装完整性\n● 具体包装细节\n● 客户特殊要求';

  @override
  String get publish_all => '全数检验';

  @override
  String get publish_all_content => '全数检验可以在包装前或者包装后进行，根据客户要求，对每一件产品的外观、尺寸、工艺、功能及安全性等进行检验，区分良品与不良品，并将检品结果及时汇报给客户';

  @override
  String get publish_online => '在线检验';

  @override
  String get publish_online_content => '在线检验是在生产过程中检验或者全部生产完成包装之前检验，可以及时帮您确认产品的质量，功能，外观及其它要素是否在整个生产过程都同您的规格要求保持一致，同时也有利于尽早发现任何不符点，从而降低工厂延迟交货的风险';

  @override
  String get publish_online_point => '● 生产情况跟进\n● 生产线评估及生产进度确认\n● 抽检半成品和成品\n● 检查包装信息及包装材料\n● 使有缺陷的产品改进\n● 评估交付时间';

  @override
  String get publish_factory => '工厂审核';

  @override
  String get publish_factory_content => '工厂审核主要采用客观判断法，依据事先制定好的标准或准则对工厂进行量化考核和审定，根据现场打分评比以及对工厂的综合审核结果等形成评估报告，以供客户判定该工厂是否作为其合格供应商的依据';

  @override
  String get publish_factory_point_title => '审计内容';

  @override
  String get publish_factory_point => '● 工厂概况（基本信息）\n● 组织架构\n● 生产流程\n● 生产能力\n● 研发技术能力\n● 机械设备和设施';

  @override
  String get publish_factory_review => '综合评价';

  @override
  String get publish_factory_review_content => '● 针对每个审核项目，权衡彼此的重要性，分别给予不同的分数，再根据审核调查表及实地调查的资料，出具资格评分表';

  @override
  String get publish_watch => '监装';

  @override
  String get publish_watch_content => '货柜监装主要包括评估货柜状况、核对产品信息、清点装柜的箱数，检查包装信息及监督整个装柜过程。为了降低货物在装柜后被替换的高风险，检验员在装箱现场进行监督，以确保您支付的产品安全装箱';

  @override
  String get publish_watch_point => '● 记录货柜号码和拖车号码\n● 检查货柜是否存在破损、潮湿和特殊气味，并对空柜进行拍照\n● 检查待装箱数以及外包装状况，随机抽查几箱以确认实际装箱的产品\n● 监督装柜过程，以确保破损最小化和空间利用最大化\n● 对货柜封门情况，货柜铅封号，装箱单进行拍照留底，记录货柜离开时间';

  @override
  String get publish_watch_inspection => '验货+监装';

  @override
  String get publish_watch_inspection_content => '为了确保产品的最终质量及完成情况，在准备出货前，我们根据国际抽样方案随机从成品中抽取样品进行抽样检查，并核对客户提供的资料，对整个装柜流程进行全程监控';

  @override
  String get publish_watch_inspection_point => '● 在集装箱未到达前核对客户资料/样品比对，抽样检验产品的外观工艺、功能和安全性以及产品包装、箱唛等\n● 发现不良品及时与工厂沟通，进行替换或者返工\n● 检查货柜是否存在破损、潮湿和特殊气味，并对空柜进行拍照\n● 监督装柜过程，以确保破损最小化和空间利用最大化\n● 对货柜封门情况，货柜铅封号，装箱单进行拍照留底，记录货柜离开时间';

  @override
  String get publish_next => '下一步';

  @override
  String get publish_inspection_time => '验货时间';

  @override
  String get publish_inspection_time_selected => '选择验货时间';

  @override
  String get publish_inspection_time_tip => '请选择';

  @override
  String get publish_inspection_people => '验货人数';

  @override
  String get publish_people => '人';

  @override
  String get publish_day => '天';

  @override
  String get publish_inspection_factory => '验货工厂';

  @override
  String get publish_factory_tips => '输入验货工厂';

  @override
  String get publish_address_book => '地址簿';

  @override
  String get publish_goods_name => '产品名称';

  @override
  String get publish_name_tips => '多种产品时输入一两个代表名称';

  @override
  String get publish_po_tips => '请输入P.O号';

  @override
  String get publish_file_tips => '上传附件';

  @override
  String get publish_camera => '拍照上传';

  @override
  String get publish_file => '文件上传';

  @override
  String get publish_purchase => '发布采购订单';

  @override
  String get publish_inspection => '发布验货订单';

  @override
  String get publish_factory_tip => '请先选择验货地址等信息';

  @override
  String get publish_attention => '注意事项';

  @override
  String get publish_attention_tips => '请输入哪些问题需要验货员重点关注';

  @override
  String get publish_stand_price => '一口价';

  @override
  String get publish_click_price => '切换';

  @override
  String get publish_vip_price => 'VIP价';

  @override
  String get publish_vip_tips => '提供人工全程跟单等服务';

  @override
  String get publish_total => '合计';

  @override
  String get publish_submit => '提交';

  @override
  String get publish_only_price_failed => '无一口价权限';

  @override
  String get publish_price_tip => '请选择价格';

  @override
  String get publish_date_tips => '请选择日期';

  @override
  String get date_title => '验货日期';

  @override
  String get date_save => '保存';

  @override
  String get address_title => '编辑工厂信息';

  @override
  String get address_auto_tips => '请粘贴或输入文本，点击“识别”自动工厂名称、姓名电话、地址等';

  @override
  String get address_paste => '粘贴';

  @override
  String get address_ocr => '识别';

  @override
  String get address_name => '工厂名称';

  @override
  String get address_name_tip => '请输入验货工厂信息';

  @override
  String get address_person => '联系人';

  @override
  String get address_person_tip => '请输入联系人';

  @override
  String get address_mobile => '手机号';

  @override
  String get address_mobile_tip => '请输入手机号';

  @override
  String get address_email => '邮箱';

  @override
  String get address_email_tip => '请输入邮箱号';

  @override
  String get address_area => '省市区';

  @override
  String get address_area_tip => '请选择省-市-区  〉';

  @override
  String get address_detail => '详细地址';

  @override
  String get address_detail_tip => '输入街道、门牌号等信息';

  @override
  String get address_location => '定位';

  @override
  String get address_save_tip => '保存到地址薄';

  @override
  String get address_clear => '清除';

  @override
  String get address_submit => '提交';

  @override
  String get address_recent => '最近使用地址';

  @override
  String get address_more => '更多地址';

  @override
  String get address_list_title => '地址管理';

  @override
  String get address_insert => '添加地址';

  @override
  String get address_delete => '删除';

  @override
  String get address_delete_result => '删除失败';

  @override
  String get address_edit => '编辑';

  @override
  String get address_delete_tips => '确认删除地址?';

  @override
  String get address_detected_paste => '检测到地址信息，是否使用该地址';

  @override
  String get pay_title => '支付订单';

  @override
  String get pay_time => '支付剩余时间';

  @override
  String get pay_paypal => 'PayPal';

  @override
  String get pay_zfb => '支付宝';

  @override
  String get pay_usd => '美元账户';

  @override
  String get pay_rmb => '人民币账户';

  @override
  String get pay_pay => '支付';

  @override
  String get pay_result_success => '支付成功';

  @override
  String get pay_result_success_wait => '支付成功，等待订单处理中。';

  @override
  String get pay_result_failed => '支付失败';

  @override
  String get pay_keep => '可记账';

  @override
  String get check_title => '验货报告';

  @override
  String get check_picture => '验货图片';

  @override
  String get check_file => '廉政承诺函';

  @override
  String get check_report => '廉政承诺书+手写报告';

  @override
  String get check_draft => '草稿报告';

  @override
  String get check_template => '报告模板';

  @override
  String get check_submit => '提交';

  @override
  String get check_hint => '请输入图片描述';

  @override
  String get check_checking => '报告审核中';

  @override
  String get check_check_success => '报告审核通过';

  @override
  String get check_check_failed => '报告审核失败，请修改内容后重新提交';

  @override
  String get review_title => '服务评价';

  @override
  String get review_next => '去评价';

  @override
  String get contact_bnt => '马上沟通';

  @override
  String get review_score => '服务水平';

  @override
  String get review_score1 => '失望';

  @override
  String get review_score2 => '不满';

  @override
  String get review_score3 => '一般';

  @override
  String get review_score4 => '满意';

  @override
  String get review_score5 => '惊喜';

  @override
  String get review_tips => '多种角度评价，以帮助我们进一步了解验货员的工作能力';

  @override
  String get review_picture => '图片';

  @override
  String get review_submit => '提交';

  @override
  String get setting_title => '设置';

  @override
  String get setting_address => '地址管理';

  @override
  String get setting_clear_cache => '清除缓存';

  @override
  String get setting_clear_success => '清理成功';

  @override
  String get setting_about_us => '关于我们';

  @override
  String get setting_receive_msg => '接收消息提醒';

  @override
  String get setting_version => '版本号';

  @override
  String get setting_check_update => '检查更新';

  @override
  String get setting_login_out => '退出登录';

  @override
  String get setting_login_out_tips => '是否确定退出登录？';

  @override
  String get setting_delete_account_tips => '是否确定删除账号？';

  @override
  String get setting_policy_title => '隐私政策授权提示';

  @override
  String get setting_policy_sub_title => '进入下一步前，请先阅读并同意';

  @override
  String get setting_privacy_policy => '隐私协议';

  @override
  String get setting_user_agreement => '用户协议';

  @override
  String get setting_privacy_content => '欢迎使用验货在线App\n我们非常重视您的隐私和个人信息保护，在您使用本App的过程中，我们会对您的部分个人信息进行收集和使用\n1. 在您同意App隐私政策后，我们将进行集成SDK的初始化工作，会收集您的设备MAC地址、IMSI、Android ID、IP地址、硬件型号、操作系统版本号、唯一设备标识符（IMEI等）、网络设备硬件地址（MAC）、软件版本号、网络接入方式、类型、状态、网络质量数据、操作日志、硬件序列号、服务日志信息等以保障App正常数据统计及安全风控。\n2. 未经您的同意，我们不会从第三方获取、共享或对外提供您的信息。\n3. 您可以访问、更正、删除您的个人信息，我们也将提供注销、投诉方式。\n';

  @override
  String get setting_ihavereadandagreed => '我已阅读并同意';

  @override
  String get setting_policy_tips2 => '请认真阅读并理解';

  @override
  String get wallet_title => '钱包';

  @override
  String get wallet_bill => '账单';

  @override
  String get wallet_rmb_account => '人民币账户';

  @override
  String get wallet_usd_account => '美元账户';

  @override
  String get wallet_account_heading => '提现账户及设置';

  @override
  String get wallet_bank => '银行卡';

  @override
  String get wallet_wechat => '微信';

  @override
  String get wallet_alipay => '支付宝';

  @override
  String get wallet_charge => '充值';

  @override
  String get wallet_cash => '提现';

  @override
  String get wallet_balance => '余额';

  @override
  String get wallet_default_account => '默认账户';

  @override
  String get wallet_set_default_account => '设为默认账户';

  @override
  String get bill_title => '账单';

  @override
  String get bill_out => '支出';

  @override
  String get bill_in => '收入';

  @override
  String get bill_month => '月';

  @override
  String get bill_fenxi => '收支分析';

  @override
  String get bill_unfreeze => '解冻';

  @override
  String get bill_all => '全部账单';

  @override
  String get bill_income => '收入';

  @override
  String get bill_outcome => '支出';

  @override
  String get bill_freeze => '冻结';

  @override
  String get bill_withdraw => '提现';

  @override
  String get bank_title => '我的银行卡';

  @override
  String get bank_add => '添加银行卡';

  @override
  String get add_bank_title => '添加银行卡';

  @override
  String get add_bank_name => '银行卡名称';

  @override
  String get add_bank_branch => '开户行';

  @override
  String get add_bank_card => '卡号';

  @override
  String get add_bank_real_name => '姓名';

  @override
  String get add_bank_address => '开户地址';

  @override
  String bind_title(Object bind) {
    return '绑定$bind';
  }

  @override
  String get bind_account => '账户';

  @override
  String get bind_image => '收款码';

  @override
  String get bind_name => '姓名';

  @override
  String get bind_hint => '请输入';

  @override
  String get charge_title => '充值';

  @override
  String get charge_account => '充值账户';

  @override
  String get charge_money => '充值金额';

  @override
  String get charge_deposit_type_title => '充值方式';

  @override
  String get charge_deposit_type_online => '在线充值';

  @override
  String get charge_deposit_type_offline => '线下转账';

  @override
  String get charge_offline_nopic_hint => '请上传充值凭证';

  @override
  String get charge_upload_proof => '请上传转账凭据';

  @override
  String get withdraw_list_title => '提现历史';

  @override
  String get withdraw_rmb => '人民币提现';

  @override
  String get withdraw_usd => '美元提现';

  @override
  String get withdraw_status_checking => '审核中';

  @override
  String get withdraw_status_approved => '审核通过';

  @override
  String get withdraw_status_denied => '审核不通过';

  @override
  String get withdraw_cash_status_unfinished => '未打款';

  @override
  String get withdraw_cash_status_done => '已打款';

  @override
  String get withdraw_cash_status_refused => '驳回';

  @override
  String get charge_hint => '请输入充值金额';

  @override
  String get charge_submit => '确定';

  @override
  String get charge_rmb => '人民币充值';

  @override
  String get charge_usd => '美元充值';

  @override
  String get charge_history_title => '充值历史';

  @override
  String get cash_title => '提现';

  @override
  String get cash_account => '选择提现账户';

  @override
  String get cash_money => '提现金额';

  @override
  String get cash_invoice_money => '发票金额';

  @override
  String get cash_invoice_money_hint => '请输入发票金额';

  @override
  String get cash_invoice_upload => '发票上传';

  @override
  String get cash_account_list_title => '申请通过后，提现款将随机打入以下账户：';

  @override
  String get cash_hint => '请输入提现金额';

  @override
  String get cash_withdraw_tips1 => '您正在提现至';

  @override
  String get cash_withdraw_tips2 => ', 提现金额为';

  @override
  String get cash_amount => '到账金额';

  @override
  String get cash_other => '手续费';

  @override
  String get cash_submit => '确定';

  @override
  String get location_permission => '需要使用定位权限，请开启';

  @override
  String get location_cancel => '取消';

  @override
  String get location_author => '去授权';

  @override
  String get group_title => '组成员';

  @override
  String get unknown_error => '未知错误';

  @override
  String get data_parsing_exception => '数据解析异常';

  @override
  String get edit => '编辑';

  @override
  String get no_data => '暂无数据';

  @override
  String get note => '备注说明';

  @override
  String get msg_locating => '定位中';

  @override
  String get failed_to_download => '下载更新失败';

  @override
  String get pick_address => '点击输入工厂地址';

  @override
  String get update_now => '立即更新';

  @override
  String get message => '消息';

  @override
  String get view_order => '查看订单';

  @override
  String get today => '今天';

  @override
  String get yesterday => '昨天';

  @override
  String get send_file => '发送文件';

  @override
  String get login_expired => '登录已过期,请重新登录';

  @override
  String get exit_group_chat_confirm => '确定退出群聊?';

  @override
  String get exit_group_chat_success => '已退出群聊';

  @override
  String get exit_group_chat_page_title => '聊天信息';

  @override
  String get exit_group_chat_button_title => '退出群聊';

  @override
  String get group_chat_setting_view_more => '查看更多群成员';

  @override
  String get group_chat_setting_name => '群聊名称';

  @override
  String get group_chat_setting_owner_update => '只允许群主修改群名称';

  @override
  String get group_chat_name_page_title => '修改群聊名称';

  @override
  String get group_chat_name_page_required => '请输入群聊名称';

  @override
  String get group_chat_name_save => '保存';

  @override
  String get group_chat_name_saved => '群聊名称已修改';

  @override
  String get conversation_manage_view_please => '请选择需要操作的会话';

  @override
  String get conversation_manage_view_list => '会话列表';

  @override
  String get group_manage_select => '请选着需要操作的群组';

  @override
  String get group_manage_list => '群组列表';

  @override
  String get please_enter => '请输入';

  @override
  String get address_keyword => '请输入地址关键字';

  @override
  String get inspector_min_fee => '请输入最低验货费用';

  @override
  String get inspector_id_card_required => '请输入身份证号';

  @override
  String get inspector_id_card_upload => '请上传身份证照片';

  @override
  String get inspector_id_card_upload_fail => '身份证照片上传出错,请重新上传';

  @override
  String get inspector_revoke => '确定撤销验货员资格？';

  @override
  String get inspector_revoke_completed => '已撤销验货员资格';

  @override
  String get male => '男';

  @override
  String get female => '女';

  @override
  String get elementary => '小学';

  @override
  String get junior => '初中';

  @override
  String get technical => '中专';

  @override
  String get senior => '高中';

  @override
  String get college => '大专';

  @override
  String get bachelor => '本科';

  @override
  String get master => '硕士';

  @override
  String get doctor => '博士';

  @override
  String get yes => '有';

  @override
  String get no => '无';

  @override
  String get upload_image => '上传图片';

  @override
  String get upload_file => '上传文件';

  @override
  String get revoke_inspector => '撤销验货员资格';

  @override
  String get deposit_card => '储蓄卡';

  @override
  String get withdrawal_balance => '提现金额不能超过账户余额';

  @override
  String get failed_get_payment_info => '获取支付信息失败';

  @override
  String get recommended_order => '推荐下单';

  @override
  String get withdrawal_method => '请提供至少一种提现方式';

  @override
  String get withdrawal_bind_alipay => '请先绑定支付宝';

  @override
  String get enabled_camera => '请设置允许使用相机拍照';

  @override
  String get valid_email_mobile => '请输入正确的邮箱地址或手机号';

  @override
  String get apple_map => '苹果地图';

  @override
  String get baidu_map => '百度地图';

  @override
  String get amap => '高德地图';

  @override
  String get google_map => '谷歌地图';

  @override
  String get tencent_map => '腾讯地图';

  @override
  String get image_format => '图片格式需为png,jpg,jpeg';

  @override
  String get enable_location_service => '需开启定位权限';

  @override
  String get enable_location_service_tips => '开启定位权限，可精准查找周边验货订单';

  @override
  String get enable_permission_not_now => '暂不设置';

  @override
  String get enable_permission_goto_setting => '去设置';

  @override
  String get failed_location_service => '获取位置信息出错';

  @override
  String get turn_on_location_service => '请打开手机定位服务';

  @override
  String get no_install_map => '您未安装地图';

  @override
  String get camera => '相机';

  @override
  String get photo_album => '相册';

  @override
  String get new_version => '版本全新上线';

  @override
  String get invalid_mail => '用户邮箱不存在';

  @override
  String get invalid_password => '密码错误';

  @override
  String get invalid_mobile => '手机号不存在';

  @override
  String get invalid_auth_code => '验证码不正确';

  @override
  String get invalid_login => '登录失败，请重试';

  @override
  String get grabbing => '抢单中';

  @override
  String get hour_ago => '小时前发布';

  @override
  String get minute_ago => '分钟前发布';

  @override
  String get report_type => '报告类型';

  @override
  String get fri => '抽检';

  @override
  String get fui => '';

  @override
  String get oli => '';

  @override
  String get fat => '';

  @override
  String get cls => '监柜';

  @override
  String get fri_cls => '';

  @override
  String get order_payment => '订单支付';

  @override
  String get order_refund => '订单退回';

  @override
  String get expend_withdrawal => '支出-提现';

  @override
  String get incoming_refund => '收入-订单退款';

  @override
  String get incoming_recharge => '收入-充值';

  @override
  String get chat_not_member => '你已经不是群组成员，不能发送信息';

  @override
  String get admins => '联系客服';

  @override
  String get theme_title => '主题';

  @override
  String get theme_light => '亮色主题';

  @override
  String get theme_dark => '暗色主题';

  @override
  String get theme_auto => '跟随系统';

  @override
  String get amount_total => '总额';

  @override
  String get amount_available => '可用';

  @override
  String get amount_blocked => '冻结';

  @override
  String get download => '点击下载';

  @override
  String get downloading => '正在下载';

  @override
  String get saved => '已保存';

  @override
  String get order_number => '订单编号';

  @override
  String get order_detail_inspection_cost => '验货费用';

  @override
  String get delete_account => '删除账号';

  @override
  String get delete_account_confirm => '所有信息都将不会保留。\n确定删除吗？';

  @override
  String get delete_account_result => '账号已删除';

  @override
  String get not_exist_account => '账号不存在';

  @override
  String get new_password => '输入新密码';

  @override
  String get supervisor => '跟单员';

  @override
  String get downloadFiles => '下载的文件';

  @override
  String get home_search_hint_inspector => '按城市/产品名搜索订单';

  @override
  String get home_search_hint_admin => '按城市/产品名搜索订单';

  @override
  String get search_recent_history => '最近搜索';

  @override
  String get assign => '指派';

  @override
  String get assigned => '已指派';

  @override
  String get approve => '通过';

  @override
  String get assign_inspector => '指派验货员';

  @override
  String get unassigned => '未指派';

  @override
  String get general_all => '全部';

  @override
  String get general_date => '日期';

  @override
  String get general_desc => '说明';

  @override
  String get general_amount => '金额';

  @override
  String get assign_search_hint => '请输入昵称/姓名/邮箱/手机号';

  @override
  String get assign_cancel_message => '确认取消指派该验货员';

  @override
  String get assign_inspect_times => '验货次数';

  @override
  String get assign_leave_message_batch => '批量留言';

  @override
  String get assign_price_zero_tips => '验货费用不能为0';

  @override
  String get assign_applied => '已申请';

  @override
  String get is_auth_forbidden => '已禁用';

  @override
  String get apply_time => '申请于';

  @override
  String get assign_message => '留言';

  @override
  String get chat_send_message => '发消息';

  @override
  String get chat_send_order => '发送订单';

  @override
  String get chat_panel_album => '相册';

  @override
  String get chat_panel_camera => '拍照';

  @override
  String get chat_panel_file => '文件';

  @override
  String get chat_toolbar_custom_service => '专属客服';

  @override
  String get chat_toolbar_submit_order => '验货下单';

  @override
  String get home_navigation => '点击导航';

  @override
  String get price_input_error_zero => '订单必须在0元与100万元之间';

  @override
  String get filter_all => '全部筛选';

  @override
  String get filter_heading_order_status => '按订单状态';

  @override
  String get filter_heading_insp_date => '按验货日期';

  @override
  String get filter_heading_order_date => '按发布日期';

  @override
  String get filter_heading_area => '按地区';

  @override
  String get filter_date_start => '起始时间';

  @override
  String get filter_date_end => '截止时间';

  @override
  String get filter_date_today => '今日订单';

  @override
  String get filter_date_tomorrow => '明日订单';

  @override
  String get filter_date_2days_later => '2日内订单';

  @override
  String get filter_date_3days_later => '3日内订单';

  @override
  String get sort_by_order_date => '按订单日期排序';

  @override
  String get sort_by_insp_date => '按验货日期排序';

  @override
  String get sort_by_distance => '按距离排序';

  @override
  String get purchase_all_replies => '全部回复';

  @override
  String get purchase_replies_count => '条回复';

  @override
  String get purchase_no_more_replies => '暂无更多回复';

  @override
  String get purchase_save_draft_title => '是否保存草稿';

  @override
  String get purchase_save_draft_choice => '保存为草稿';

  @override
  String get purchase_save_draft_quit => '直接退出';

  @override
  String get purchase_search_hint => '搜索采购订单';

  @override
  String get purchase_reply_hint => '回复帖子内容';

  @override
  String get purchase_reply_reason_hint => '相似度描述或推荐理由';

  @override
  String get purchase_complaint_hint => '提供更多信息有助于举报被快速处理';

  @override
  String get purchase_reply_paid_hint => '输入悬赏内容';

  @override
  String get purchase_edit => '编辑帖子';

  @override
  String get purchase_publish => '悬赏求购';

  @override
  String get purchase_publish_product_label => '产品名称';

  @override
  String get purchase_publish_title_label => '悬赏标题';

  @override
  String get purchase_publish_quantity => '采购数量';

  @override
  String get purchase_publish_content_label => '详细描述';

  @override
  String get purchase_publish_product_hint => '请输入产品名称';

  @override
  String get purchase_publish_title_hint => '可写明名称型号、地区等信息';

  @override
  String get end_date => '截止日期';

  @override
  String get purchase_area => '采购地区';

  @override
  String get purchase_permission_author_only => '只允许楼主查看回复';

  @override
  String get purchase_publish_quantity_hint => '请输入数量';

  @override
  String get purchase_publish_content_hint => '请输入详细描述';

  @override
  String get purchase_publish_price => '悬赏价格';

  @override
  String get purchase_publish_choose_category => '选择分类';

  @override
  String get purchase_publish_choose_category_hint => '请选择分类';

  @override
  String get purchase_paid_publish_switch => '有偿查看';

  @override
  String get purchase_paid_publish_set_price => '设置价格';

  @override
  String get purchase_detail_response_all => '全部回复';

  @override
  String get purchase_detail_response_author_only => '只看楼主';

  @override
  String get purchase_detail_response_asc => '正序';

  @override
  String get purchase_detail_response_desc => '倒序';

  @override
  String get purchase_detail_more_reply => '回复';

  @override
  String get purchase_detail_more_up => '赞';

  @override
  String get purchase_detail_more_cancel_up => '取消赞';

  @override
  String get purchase_my_posts => '主贴';

  @override
  String get purchase_my_replies => '回复';

  @override
  String get purchase_my_appeals => '申诉';

  @override
  String get purchase_appeal_detail => '申诉详情';

  @override
  String get purchase_appeal_submit => '提交申诉';

  @override
  String get purchase_appeal_cancel => '取消申诉';

  @override
  String get purchase_appeal_approve => '申诉已通过';

  @override
  String get purchase_appeal_denied => '申诉已驳回';

  @override
  String get purchase_paid_content_owner_tips => '付费内容';

  @override
  String get purchase_paid_content_tips => '付费内容，支付查看';

  @override
  String get purchase_paid_content_paid_tips => '付费内容已解锁';

  @override
  String get purchase_review_leave => '评价一下';

  @override
  String get purchase_review_my_score => '我的评价';

  @override
  String get purchase_my_replies_original_header => '原帖';

  @override
  String get purchase_publish_bounty_tips => '注：悬赏金额可自由选填，较高的悬赏金能吸引更多的验货员积极为您提供您需要的信息。';

  @override
  String get purchase_reply_to => '回复';

  @override
  String get purchase_modify_bounty => '修改悬赏';

  @override
  String get purchase_bounty_money => '悬赏';

  @override
  String get purchase_evaluated_person => '人已评价';

  @override
  String get purchase_comment_paid_supplier => '供应商';

  @override
  String get purchase_comment_paid_contact => '联系人';

  @override
  String get purchase_comment_paid_phone => '联系电话';

  @override
  String get purchase_comment_paid_email => '邮箱';

  @override
  String get purchase_comment_paid_address => '工厂地址';

  @override
  String get purchase_comment_paid_other => '其它';

  @override
  String get purchase_comment_paid_low_price => '产品底价';

  @override
  String get purchase_appeal_title => '申请退款';

  @override
  String get purchase_appeal_reason => '请输入申诉理由';

  @override
  String get purchase_appeal_request_price => '申诉金额：';

  @override
  String get purchase_appeal_request_reason => '申诉理由：';

  @override
  String get purchase_post_status_draft => '草稿';

  @override
  String get purchase_post_status_reviewing => '审核中';

  @override
  String get purchase_post_status_published => '审核通过';

  @override
  String get purchase_post_status_denied => '未通过';

  @override
  String get purchase_post_publish => '发布帖子';

  @override
  String get purchase_complaint_type_leading => '请选择举报类型';

  @override
  String get purchase_complaint_type_1 => '色情低俗';

  @override
  String get purchase_complaint_type_2 => '垃圾广告';

  @override
  String get purchase_complaint_type_3 => '辱骂攻击';

  @override
  String get purchase_complaint_type_4 => '违法犯罪';

  @override
  String get purchase_complaint_type_5 => '时政不实信息';

  @override
  String get purchase_complaint_type_6 => '侵犯权益';

  @override
  String get purchase_complaint_type_7 => '其他';

  @override
  String get shop_goods_detail_title => '商品详情';

  @override
  String get mall_buy_immediate => '立即购买';

  @override
  String get mall_goods_count => '数量';

  @override
  String get mall_confirm_pay => '确认支付';

  @override
  String get mall_order_confirm => '订单确认';

  @override
  String get mall_submit_order => '提交订单';

  @override
  String get mall_goods_price => '商品金额';

  @override
  String get mall_express_price => '运费';

  @override
  String get mall_price_total => '合计：';

  @override
  String get mall_payment => '收银台';

  @override
  String get mall_payment_methods => '支付方式';

  @override
  String get mall_pay_succeed => '支付成功';

  @override
  String get mall_check_order_detail => '查看订单详情';

  @override
  String get mall_order_remark => '订单备注';

  @override
  String get mall_order_remark_input => '填写备注';

  @override
  String get purchase_detail_more_report => '举报';

  @override
  String get purchase_reply_paid_content_tips => '注：请填写真实信息，有偿回复发表后需等待审核，审核之后才能被他人查看到';

  @override
  String get public_ip_address => 'IP属地:';

  @override
  String get inspection_widget_suit_tips => '验货时需佩戴工牌或穿工作服，若还没有可在首页进入购买';

  @override
  String get purchase_paid_content_appeal => '申诉';

  @override
  String get report_success => '举报成功';

  @override
  String tabbar_tab_names(String names) {
    String _temp0 = intl.Intl.selectLogic(
      names,
      {
        'tab_home': '首页',
        'tab_shortcut': '快捷',
        'tab_message': '消息',
        'tab_mine': '我的',
        'tab_publish': '',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String order_status_desc(String status) {
    String _temp0 = intl.Intl.selectLogic(
      status,
      {
        'order_status_unknown': '未知',
        'order_wait_pay': '待支付',
        'order_cancelled': '已取消',
        'order_cancelled_refund_pending': '已取消,退款待审核',
        'order_refund_pending': '退款待审核',
        'order_refund_partial': '部分退款',
        'order_refund_denied': '拒绝退款',
        'order_wait_dispatch': '待派单',
        'order_doing': '验货中',
        'order_finished': '已完成',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String pay_type(String type) {
    String _temp0 = intl.Intl.selectLogic(
      type,
      {
        'pay_rmb': '人民币账户',
        'pay_usd': '美元账户',
        'pay_zfb': '支付宝',
        'pay_paypal': 'PayPal',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String purchase_complaint_type(String type) {
    String _temp0 = intl.Intl.selectLogic(
      type,
      {
        'type_1': '色情低俗',
        'type_2': '垃圾广告',
        'type_3': '辱骂攻击',
        'type_4': '违法犯罪',
        'type_5': '时政不实信息',
        'type_6': '侵犯权益',
        'type_7': '其他',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String get purchase_reply_paid_my_evaluate => '（我的评价）';

  @override
  String get bind_now => '立即绑定';

  @override
  String get cancel_register => '取消注册';

  @override
  String register_and_bind_email0(Object email) {
    return '注册并绑定$email为主邮箱';
  }

  @override
  String register_and_bind_email1(Object email) {
    return '您的邮箱$email已注册，继续绑定';
  }

  @override
  String get register_and_bind_email2 => '注册并绑定主邮箱';

  @override
  String get new_register_bind_title => '设置主登陆邮箱并注册';

  @override
  String get new_register_bind_field_account_tips => '绑定邮箱/手机号';

  @override
  String get register => '注册';

  @override
  String get switch_account => '切换账号';

  @override
  String get switch_account_confirm_tips => '确定切换账户?';

  @override
  String get password_must_have => '您的密码必须包含：';

  @override
  String get password_must_have_1 => '长度为 8-32 个字符';

  @override
  String get password_must_have_2 => '1 个小写字母 (a-z)';

  @override
  String get password_must_have_3 => '1 个数字';

  @override
  String get password_must_have_4 => '1 个符号 (如!@#\\\$%^&*)';

  @override
  String get password_login => '输入登录密码';

  @override
  String get password_login_again => '再次输入新密码';

  @override
  String get choose_account_to_login => '选择账号快速登录';

  @override
  String get finish => '完成';

  @override
  String get done => '完成';

  @override
  String get account_apple => '苹果登录';

  @override
  String get account_google => '谷歌登录';

  @override
  String get account_wechat => '微信登录';

  @override
  String get account_facebook => 'Facebook登录';

  @override
  String get third_account_unbind => '解除绑定';

  @override
  String get third_account_bind => '绑定';

  @override
  String get confirm_unbind => '确定解绑?';

  @override
  String get inspection_requirement => '验货须知';

  @override
  String get liveroom_entrance => '直播间排单';

  @override
  String get add_account => '新增账户';

  @override
  String get message_order => '订单';

  @override
  String get message_email => '邮件';

  @override
  String get message_wallet => '钱包';

  @override
  String get message_user => '用户信息';

  @override
  String get salesman => '跟单员';

  @override
  String get public_continue => '继续';

  @override
  String get camera_permission_tips => '为方便您使用App时可以正常上传图片、文件或拍照上传，需要您允许使用照相机、存储权限';

  @override
  String get ai_category_inspector => '验货';

  @override
  String get ai_nothing_category => '没有可检测类目';

  @override
  String get ai_category_name => '验货首页';

  @override
  String get ai_quantity => '数量';

  @override
  String get ai_packaging => '包装';

  @override
  String get ai_shipping_mark => '唛头';

  @override
  String get ai_product_style => '产品样式';

  @override
  String get ai_test => '测试';

  @override
  String get ai_craftsmanship => '工艺';

  @override
  String get ai_test_verification => '测试校验';

  @override
  String get ai_category_measure => '测量';

  @override
  String get ai_spare_parts => '备件';

  @override
  String get ai_sampling_number => '抽样编号';

  @override
  String ai_input_range_number(Object range) {
    return '可输入$range内的数字';
  }

  @override
  String ai_enter_range_number(Object range) {
    return '请输入$range内的数字';
  }

  @override
  String get ai_selected => '选中';

  @override
  String get ai_selected_status => '已选中';

  @override
  String get ai_order_quantity => '订单数量';

  @override
  String get ai_packaged_boxes_quantity => '已包装箱数量(成品)';

  @override
  String get ai_unpackaged_boxes_quantity => '未包装箱数量(成品)';

  @override
  String get ai_sample_from_packaged => '从已包装里抽样';

  @override
  String get ai_sample_from_unpackaged => '从未包装里抽样';

  @override
  String get ai_spare_parts_quantity => '备件数量';

  @override
  String get ai_sampling_packaging_number => '抽样包装编号';

  @override
  String get ai_sampling_packaging_number_record => '抽样编号记录';

  @override
  String get ai_sampling_packaging_number_list => '记录抽样外箱的编号';

  @override
  String get ai_judgment => '判定';

  @override
  String get ai_judgment_item => '判定项';

  @override
  String get ai_standard => '标准';

  @override
  String get ai_result => '结果';

  @override
  String get ai_conclusion => '结论';

  @override
  String get ai_overall_conclusion => '整体结论';

  @override
  String get ai_consistency => '是否一致';

  @override
  String get ai_yes => '是';

  @override
  String get ai_no => '否';

  @override
  String get ai_remarks => '备注';

  @override
  String get ai_numerical => '序号';

  @override
  String get ai_recommended_test_items => '推荐检测项';

  @override
  String get ai_test_item => '检测项';

  @override
  String get ai_add_all => '一键添加';

  @override
  String get ai_add_plus => '+添加';

  @override
  String get ai_add => '添加';

  @override
  String ai_confirm_delete(Object name) {
    return '您确定要删除$name吗';
  }

  @override
  String get ai_enter_test_item => '请输入检测项';

  @override
  String get ai_defect_record => '缺陷记录';

  @override
  String get ai_defect_photo => '缺陷拍照';

  @override
  String get ai_defect_description => '缺陷描述';

  @override
  String get ai_defect_level => '缺陷等级';

  @override
  String get ai_found_quantity => '发现数量';

  @override
  String get ai_handling_method => '处理方式';

  @override
  String get ai_edit => '修改';

  @override
  String get ai_delete => '删除';

  @override
  String get ai_pick_out => '挑出';

  @override
  String get ai_replace => '替换';

  @override
  String get ai_rework => '返工';

  @override
  String get ai_edit_description => '修改描述';

  @override
  String get ai_critical => '致命';

  @override
  String get ai_important => '严重';

  @override
  String get ai_minor => '轻微';

  @override
  String get ai_defect_list => '缺陷列表';

  @override
  String get ai_test_level => '测试等级';

  @override
  String get ai_sampling_sample => '抽样样本';

  @override
  String get ai_sampling_level => '抽样等级';

  @override
  String get ai_additional_information => '补充说明';

  @override
  String get ai_inspection_record => '检测记录';

  @override
  String get ai_sample_count => '样本数';

  @override
  String get ai_maximum_allowable_value => '最大允许值';

  @override
  String get ai_test_item_name => '测试项';

  @override
  String get ai_test_result => '测试结果';

  @override
  String get ai_basic_information => '基本信息';

  @override
  String get ai_new_test_item => '新建测试项';

  @override
  String get ai_test_project => '测试项目';

  @override
  String get ai_measurement_project => '测量项目';

  @override
  String get ai_measure_need_num => '个数要求';

  @override
  String get ai_measurement_unit => '测量单位';

  @override
  String get ai_measurement_method => '测量方式';

  @override
  String get ai_measurement_record => '测量记录';

  @override
  String get ai_measured => '已测量';

  @override
  String get ai_unit_of_measurement => '计量单位';

  @override
  String get ai_measured_value => '测量值';

  @override
  String get ai_product_number => '产品编号';

  @override
  String get ai_number => '编号';

  @override
  String get ai_new_measurement_item => '新建测量项';

  @override
  String get ai_length_width_height => '长宽高';

  @override
  String get ai_dimensions_length => '长';

  @override
  String get ai_dimensions_width => '宽';

  @override
  String get ai_dimensions_height => '高';

  @override
  String get ai_length_width => '长宽';

  @override
  String get ai_other => '其它';

  @override
  String get ai_allowable_error => '允许误差';

  @override
  String get ai_report_summary => '报告总结';

  @override
  String get ai_special_note => '特别注意';

  @override
  String get ai_overall_conclusion_2 => '总体结论';

  @override
  String get ai_summary => '概要';

  @override
  String get ai_category_name_table => '检测类目';

  @override
  String get ai_compliance => '是否符合';

  @override
  String get ai_remarks_2 => '备注';

  @override
  String get ai_defect_summary => '缺陷汇总';

  @override
  String get ai_no_guidance_instructions => '暂无指导说明';

  @override
  String get ai_no_standard_instructions => '暂无标准说明';

  @override
  String get ai_please_fill_in => '请填写';

  @override
  String ai_please_supplement_level_or_sample(Object level, Object sample) {
    return '请补充$level或$sample';
  }

  @override
  String get ai_please_add => '请添加';

  @override
  String get ai_please_input => '请输入';

  @override
  String get ai_please_select => '请选择';

  @override
  String ai_name_not_filled(Object name) {
    return '$name未填写';
  }

  @override
  String get ai_addition_successful => '添加成功';

  @override
  String get ai_confirm_action => '确定';

  @override
  String get ai_cancel_action => '取消';

  @override
  String get ai_submit => '提交';

  @override
  String get ai_next_item => '下一项';

  @override
  String get ai_complete => '完成';

  @override
  String get ai_change_description => '修改描述';

  @override
  String get ai_action_guidance_instructions => '操作指导';

  @override
  String get ai_action_standard_instructions => '行业标准';

  @override
  String get ai_add_description => '添加描述';

  @override
  String get ai_change_description_note => '注意：下方为已发现的缺陷，如果修改，历史数据也将采用新描述！';

  @override
  String get ai_packing_completion_rate => '装箱完成率';

  @override
  String get ai_unprocessed_quantity => '未生产完成数量';

  @override
  String get ai_sample_level_type_0 => 'I级';

  @override
  String get ai_sample_level_type_1 => 'II级';

  @override
  String get ai_sample_level_type_2 => 'III级';

  @override
  String get ai_sample_level_type_s1 => 'S-1';

  @override
  String get ai_sample_level_type_s2 => 'S-2';

  @override
  String get ai_sample_level_type_s3 => 'S-3';

  @override
  String get ai_sample_level_type_s4 => 'S-4';

  @override
  String get ai_sample_level_type_nothing => '自定义';

  @override
  String get ai_inspection_image => '图片';

  @override
  String get ai_photo_confirm => '确认';

  @override
  String get ai_add_product_ask_save => '是否需要保存本次编辑？';

  @override
  String get ai_add_product_save => '保存';

  @override
  String get ai_add_product_edit_model => '型号编辑';

  @override
  String get ai_add_product_model_name => '型号名称';

  @override
  String get ai_add_product_input_model => '输入型号名称';

  @override
  String get ai_add_product_num => '数量';

  @override
  String get ai_add_product_input_num => '输入型号数量';

  @override
  String get ai_add_product_unit => '单位';

  @override
  String get ai_add_product_ask_delete => '是否需要删除该型号?';

  @override
  String get ai_add_product_edit_product => '产品编辑';

  @override
  String get ai_add_product_product_name => '产品名称';

  @override
  String get ai_add_product_model => '型号';

  @override
  String get ai_add_product_input_product_name => '输入产品名称';

  @override
  String get ai_add_product_new_model => '新增型号';

  @override
  String get ai_add_product_ask_product => '是否需要删除该产品及所有型号?';

  @override
  String get ai_add_product_picture_lost => '图片缺失';

  @override
  String ai_add_product_lost_des(Object lostStr) {
    return '当前缺失$lostStr，请将信息补充完整后，再进行验货';
  }

  @override
  String get ai_add_product_model_full => '产品型号名称';

  @override
  String get ai_add_product_model_title => '产品型号';

  @override
  String get ai_add_product_new => '添加产品';

  @override
  String get ai_model_unit_piece => '个';

  @override
  String get ai_model_unit_only => '只';

  @override
  String get ai_model_unit_item => '件';

  @override
  String get ai_model_unit_pair => '双';

  @override
  String get ai_model_unit_set => '套';

  @override
  String get ai_model_unit_dozen => '打';

  @override
  String get ai_model_unit_roll => '卷';

  @override
  String get ai_model_unit_vehicle => '辆';

  @override
  String get ai_model_unit_head => '头';

  @override
  String get ai_model_unit_bag => '袋';

  @override
  String get ai_model_unit_box => '箱';

  @override
  String get ai_model_unit_pack => '包';

  @override
  String get ai_model_unit_yard => '码';

  @override
  String get ai_model_unit_meter => '米';

  @override
  String get ai_model_unit_kilogram => '千克';

  @override
  String get ai_model_unit_metric_ton => '公吨';

  @override
  String get ai_model_unit_liter => '公升';

  @override
  String get ai_model_unit_gallon => '加仑';

  @override
  String get ai_model_unit_other => '其它';

  @override
  String get ai_default_config_des => '当前产品暂无检测模板，你可以选择下方模板，或致电（+86）进行模板配置。';

  @override
  String get ai_default_config_category_all => '分类(全部)';

  @override
  String get ai_default_config_select_template => '配置模版';

  @override
  String get ai_default_config_template_selection => '模版选择';

  @override
  String get ai_default_config_search_template => '搜索模版';

  @override
  String get ai_default_config_classify => '分类';

  @override
  String get ai_default_config_preview => '预览';

  @override
  String get ai_default_config_use => '应用';

  @override
  String get ai_default_config_current_use_button => '应用到本产品';

  @override
  String get ai_default_config_more_use_button => '应用到更多产品';

  @override
  String get ai_default_config_product_list => '产品列表';

  @override
  String get ai_default_config_use_warning => '注：【模】产品已经载入模板；【运】运营已经配置模板。如果重新载入新模板将会覆盖之前数据。';

  @override
  String get ai_default_config_tag_default => '运';

  @override
  String get ai_default_config_tag_manual => '模';

  @override
  String get ai_default_config_load_progress => '载入进度';

  @override
  String ai_default_config_template_progress(Object name) {
    return '模版载入完成 $name。';
  }

  @override
  String ai_default_config_template_fail_count(Object name) {
    return '失败$name个，点击重试';
  }

  @override
  String get ai_default_config_load => '载入';

  @override
  String get ai_default_config_success => '成功';

  @override
  String get ai_default_config_fail => '失败';

  @override
  String get ai_default_config_template => '模板';

  @override
  String get ai_add_model_count_warning => '数量必须大于0';

  @override
  String get ai_default_config_product_edit => '编辑产品';

  @override
  String get ai_wait => '待定';

  @override
  String get ai_product_info => '产品信息';

  @override
  String get ai_product_category => '产品分类';

  @override
  String get ai_product_unit => '产品单位';

  @override
  String get ai_package_num_done => '已包装箱数';

  @override
  String get ai_product_full => '补充信息';

  @override
  String get ai_product_full_tip => '订单中的产品信息不完善，请根据验货现场中的实际产品信息进行补充。';

  @override
  String get ai_each_box => '每箱';

  @override
  String get ai_simple_count => '抽样样本数';

  @override
  String get ai_simple_level => '抽样标准';

  @override
  String get ai_simple_num => '抽样数量';

  @override
  String get ai_simple_no => '抽样外箱编号';

  @override
  String get ai_simple_result => '结果判定';

  @override
  String get ai_simple_project => '检查项目';

  @override
  String get ai_simple_project_manage => '管理检查项目';

  @override
  String get ai_simple_project_edit => '编辑检查项目';

  @override
  String get ai_simple_project_recmend => '智能推荐';

  @override
  String get ai_simple_project_input => '填写检查记录';

  @override
  String get ai_simple_help => '帮助';

  @override
  String get ai_simple_project_record => '检查记录';

  @override
  String get ai_simple_require => '客户要求';

  @override
  String get ai_simple_record => '记录';

  @override
  String get ai_simple_dsec => '描述';

  @override
  String get ai_simple_before => '上一项';

  @override
  String get ai_simple_add => '添加一组';

  @override
  String get ai_simple_add_desc => '为照片添加描述信息';

  @override
  String get ai_simple_add_citations => '次引用';

  @override
  String get ai_no_more => '没有更多数据了';

  @override
  String get ai_wrong_tip => '数量不能大于总数';

  @override
  String get ai_defect_records => '缺陷记录';

  @override
  String get ai_check_require => '抽样要求';

  @override
  String get ai_find_defect => '发现缺陷';

  @override
  String get ai_defect_question => '缺陷问题';

  @override
  String get ai_modify_level => '修改抽样等级';

  @override
  String get ai_defect_quick => '快捷添加工艺缺陷';

  @override
  String get ai_defect_self => '自定义缺陷名称';

  @override
  String get ai_defect_record_list => '瑕疵记录列表';

  @override
  String get ai_measure_require => '测量要求';

  @override
  String get ai_measurement_item => '测量项';

  @override
  String get ai_measurement_error => '误差';

  @override
  String get ai_measurement_standard => '测量标准';

  @override
  String get ai_measurement_value_standard => '标准值';

  @override
  String get ai_measurement_camera => '测量拍照';

  @override
  String get ai_measurement_add => '快捷添加测量标准';

  @override
  String get ai_product_first => '产品首图';

  @override
  String get ai_product_report => '保存并生成报告';

  @override
  String get ai_product_report_tip => '请选择产品首图';

  @override
  String get ai_product_report_special => '请输入需要特别注意的内容';

  @override
  String get ai_product_report_sign => '签名';

  @override
  String get ai_product_report_sign_done => '签名完成';

  @override
  String get ai_defect_names => '缺陷名称';

  @override
  String get ai_input_tip => '请输入名称';

  @override
  String get ai_add_measure_tip => '请先添加测量标准';

  @override
  String get ai_wrong_num => '数量错误';

  @override
  String get ai_wrong_name => '请输入产品名称';

  @override
  String get ai_wrong_sample_num => '不能大于抽样样本数';

  @override
  String get ai_per_box => '每箱数量';

  @override
  String get ai_wrong_sample_num_cal => '已包装抽样+未包装抽样需要等于抽样样本数';

  @override
  String get ai_sure_delete => '确定删除吗？';

  @override
  String get ai_choose_tip => '请选择缺陷处理方式和个数';

  @override
  String get ai_weight => '毛重';

  @override
  String get sampling_plan => '抽样计划';

  @override
  String get single => '单个';

  @override
  String get normal => '普通';

  @override
  String get summarize => '总结';

  @override
  String get po_number => 'PO号';

  @override
  String get product_quantity => '产品数量';

  @override
  String get customer_name => '客户名称';

  @override
  String get supplier_name => '供应商名称';

  @override
  String get inspection_date => '验货日期';

  @override
  String get arrival_time => '到达时间';

  @override
  String get completion_time => '完成时间';

  @override
  String get inspection_address => '验货地址';

  @override
  String get inspector => '验货员';

  @override
  String get inspection_report_note => '本验货报告只提供参考，是否确定通过等待客户确定';

  @override
  String get remark_toast => '请先填写备注';

  @override
  String get process_appearance_judgment => '工艺外观判定';

  @override
  String get test_validation_judgment => '测试校验判定';

  @override
  String check_save(Object name) {
    return '您确定要保存$name吗？';
  }

  @override
  String get select_template_config_tip => '若需要配置验货模板，请联系您的跟单员';
}

/// The translations for Chinese, as used in Hong Kong (`zh_HK`).
class SZhHk extends SZh {
  SZhHk(): super('zh_HK');

  @override
  String get app_name => '驗貨在線';

  @override
  String get search => '搜索';

  @override
  String get shortcut_tab_name => '快捷導航';

  @override
  String get loading => '加載中...';

  @override
  String get nomore => '暫無更多內容';

  @override
  String get confirm => '確定';

  @override
  String get more_replies => '更多回覆';

  @override
  String get purchase_paid_publish_information_title => '以下是客戶付費查看的內容';

  @override
  String get purchase_set_fee => '設定查看費用';

  @override
  String get purchase_comment_paid_supplier_hint => '請輸入供應商名稱';

  @override
  String get purchase_comment_paid_contact_hint => '請輸入聯繫人姓名';

  @override
  String get purchase_comment_paid_phone_hint => '請輸入聯繫人電話';

  @override
  String get purchase_comment_paid_email_hint => '請輸入聯繫人郵箱';

  @override
  String get purchase_comment_paid_address_hint => '請輸入工廠地址';

  @override
  String get purchase_comment_paid_other_hint => '其它信息（選填）';

  @override
  String get purchase_comment_paid_low_price_hint => '請輸入產品底價（選填）';

  @override
  String get purchase_reply_paid_title => '付費回覆';

  @override
  String get purchase_reply_paid_desc => '（供應商信息及商品參考價格）';

  @override
  String purchase_reply_paid_evaluated_people(Object people_num) {
    return '（$people_num人已評價）';
  }

  @override
  String get language_setting => '語言設置';

  @override
  String get public_and => '與';

  @override
  String get public_publish => '發布';

  @override
  String get public_distance => '距離';

  @override
  String get public_deny => '拒絕';

  @override
  String get public_see_more => '查看更多';

  @override
  String get general_select => '選擇';

  @override
  String get language_page_title => '多語言';

  @override
  String get language_chinese => '中文';

  @override
  String get language_english => '英文';

  @override
  String get public_seconds_ago => '秒前';

  @override
  String get public_minutes_ago => '分鐘前';

  @override
  String get public_hours_ago => '小時前';

  @override
  String get public_status_applied => '已申請';

  @override
  String get public_status_refused => '已拒絕';

  @override
  String get public_status_approved => '已通過';

  @override
  String get public_status_canceled => '已取消';

  @override
  String get public_app_name => 'Inspector';

  @override
  String get public_send => '發送';

  @override
  String get public_ok => '確定';

  @override
  String get public_price => '價格';

  @override
  String get public_cancel => '取消';

  @override
  String get public_manage => '管理';

  @override
  String get public_finish => '完成';

  @override
  String get public_reset => '重置';

  @override
  String get public_leave_message => '留言';

  @override
  String get public_share => '分享';

  @override
  String get login_submit => '登錄';

  @override
  String get login_mobile_login => '手機登錄';

  @override
  String get login_mobile_tips => '請輸入手機號';

  @override
  String get login_email_login => '郵箱登錄';

  @override
  String get login_email_tips => '請輸入郵箱';

  @override
  String get registry_email_phone_tips => '請輸入郵箱或手機號';

  @override
  String get login_verify_tips => '輸入驗證碼';

  @override
  String get login_password_tips => '輸入密碼';

  @override
  String get login_password_login => '使用密碼登錄';

  @override
  String get login_verify_login => '使用驗證碼登錄';

  @override
  String get login_register => '註冊';

  @override
  String get login_take_code => '獲取驗證碼';

  @override
  String get login_forget_password => '忘記密碼';

  @override
  String get login_agreement => '同意《inspector.Itd協議》';

  @override
  String get login_area_selected => '選擇國區';

  @override
  String get tab_home => '首頁';

  @override
  String get tab_order => '訂單';

  @override
  String get tab_shortcut => '快捷';

  @override
  String get tab_purchase => '採購';

  @override
  String get tab_message => '消息';

  @override
  String get tab_mine => '我的';

  @override
  String get supplement_title => '請先完善個人信息';

  @override
  String get supplement_next => '去完善';

  @override
  String get home_title => '驗貨廣場';

  @override
  String get home_record => '申請記錄';

  @override
  String get home_newest => '最新驗貨';

  @override
  String get home_nearest => '附近驗貨';

  @override
  String home_recommend(Object money) {
    return '推薦獎勵 RMB$money元';
  }

  @override
  String get home_sampling => '抽樣檢驗';

  @override
  String get home_word => 'WORD報告';

  @override
  String home_unit(Object day, Object people) {
    return '$people人/$day天';
  }

  @override
  String get home_product_tip => '產品：';

  @override
  String get home_person_apply => '人申請';

  @override
  String get home_know_tip => '驗貨需知';

  @override
  String get home_inspection_tip => '驗貨費用默認為商定價，可修改，費用低可能優先指派';

  @override
  String get home_reviewed => '我已查看並遵守';

  @override
  String get home_apply => '申請';

  @override
  String get home_apply_price => '請輸入金額￥';

  @override
  String get home_apply_check => '請查看驗貨須知';

  @override
  String get home_apply_tips => '您不是驗貨員，若您有1年以上外貿驗貨工作經驗，請提相關資歷證明';

  @override
  String get home_complete_profile_tips => '完善驗貨員檔案等相關資料，可以提高驗貨申請通過率';

  @override
  String get home_apply_sure => '提交審核';

  @override
  String get home_complete_profile_sure => '去完善';

  @override
  String get home_apply_cancel => '取消申請';

  @override
  String get home_update => '修改';

  @override
  String get home_navi => '導航';

  @override
  String get mine_unauth => '未認證';

  @override
  String get mine_checking => '待審核';

  @override
  String get mine_check_failed => '審核失敗';

  @override
  String get mine_vip_level => 'VIP等級';

  @override
  String get mine_credit_quota => '信用額度';

  @override
  String get mine_authed => '修改認證信息';

  @override
  String get mine_authed_inspector => '修改驗貨員信息';

  @override
  String get mine_amount => '賬戶金額';

  @override
  String get mine_cash => '充值/提現';

  @override
  String get mine_order => '我的訂單';

  @override
  String get mine_purchase => '我的採購';

  @override
  String get mine_check => '我的驗貨';

  @override
  String get mine_address => '地址簿(供應商)';

  @override
  String get mine_recommend => '推薦';

  @override
  String get mine_setting => '設置';

  @override
  String get mine_header_inspect => '驗貨管理';

  @override
  String get mine_header_purchase => '採購管理';

  @override
  String get mine_header_other => '其他';

  @override
  String get mine_inspect_mine => '我的驗貨';

  @override
  String get mine_inspect_order => '訂單管理';

  @override
  String get mine_inspect_history => '申請記錄';

  @override
  String get mine_purchase_mine => '我的採購';

  @override
  String get mine_purchase_reply => '回覆記錄';

  @override
  String get mine_purchase_appeal => '申訴管理';

  @override
  String get mine_other_recommend => '推薦';

  @override
  String get mine_other_address => '地址簿(供應商)';

  @override
  String get mine_other_settings => '設置';

  @override
  String get profile_title => '個人信息';

  @override
  String get profile_avatar => '頭像';

  @override
  String get profile_name => '暱稱';

  @override
  String get profile_mobile => '手機號';

  @override
  String get profile_country => '國家';

  @override
  String get profile_real_name => '真實姓名';

  @override
  String get profile_city => '城市';

  @override
  String get profile_email => '郵箱';

  @override
  String get profile_wechat => '微信';

  @override
  String get profile_bind_manage => '賬號綁定管理';

  @override
  String get profile_info_failed => '信息更新失敗';

  @override
  String get apply_title => '驗貨員資格申請';

  @override
  String get apply_nick => '暱稱';

  @override
  String get apply_sex => '性別';

  @override
  String get apply_birthday => '生日';

  @override
  String get apply_education => '學歷';

  @override
  String get apply_address => '常住地';

  @override
  String get apply_price => '最低驗貨費用';

  @override
  String get apply_shebao => '社保';

  @override
  String get apply_id_card => '身份證號';

  @override
  String get apply_file => '編輯簡歷';

  @override
  String get apply_file_tip => '請輸入自己的基本資料和經歷';

  @override
  String get apply_upload_file => '上傳簡歷';

  @override
  String get apply_upload_file_failed => '上傳簡歷失敗';

  @override
  String get apply_upload_card => '上傳身份證照片';

  @override
  String get apply_card_front => '正面照片(人臉面)';

  @override
  String get apply_card_back => '反面照片(國徽面)';

  @override
  String get apply_submit => '提交';

  @override
  String get apply_enter => '請輸入';

  @override
  String get apply_next_tip => '請信息確認完善後再提交';

  @override
  String get apply_auth_failed => '身份證驗證失敗, 請傳入正確的照片';

  @override
  String get apply_checking => '身份待審核';

  @override
  String get apply_check_success => '身份審核通過';

  @override
  String get apply_check_failed => '審核失敗，請修改內容後重新提交';

  @override
  String get order_title => '我的訂單';

  @override
  String get order_input => '我的驗貨';

  @override
  String get order_output => '發布訂單';

  @override
  String get order_all => '全部';

  @override
  String get order_wait_pay => '待支付';

  @override
  String get order_cancelled => '已取消';

  @override
  String get order_status => '訂單狀態';

  @override
  String get order_status_unknown => '未知';

  @override
  String get order_refund_pending => '退款待審核';

  @override
  String get order_cancelled_refund_pending => '已取消,退款待審核';

  @override
  String get order_refund_partial => '部分退款';

  @override
  String get order_refund_denied => '拒絕退款';

  @override
  String get order_wait_dispatch => '待派單';

  @override
  String get order_ready_inspect => '準備驗貨';

  @override
  String get order_need_pay => '支付';

  @override
  String get order_wait => '準備驗貨';

  @override
  String get order_confirm => '派單確認';

  @override
  String get order_doing => '驗貨中';

  @override
  String get order_comment => '待評價';

  @override
  String get order_finished => '已完成';

  @override
  String get order_goods_info => '產品信息';

  @override
  String get order_goods_name => '產品名稱';

  @override
  String get order_goods_model => '產品型號';

  @override
  String get order_goods_count => '數量';

  @override
  String get order_goods_unit => '單位';

  @override
  String get order_order_time => '訂單時間';

  @override
  String get order_order_amount => '訂單金額';

  @override
  String get order_detail_title => '訂單詳情';

  @override
  String get order_applying => '已申請';

  @override
  String get order_apply_expired => '已過期';

  @override
  String get order_apply_dispatched => '已派單';

  @override
  String get order_create_time => '下單時間';

  @override
  String get order_look => '查看驗貨報告';

  @override
  String get order_report_next => '提交驗貨報告';

  @override
  String get order_detail_inspection_info => '驗貨信息';

  @override
  String get order_inspection_status_unpaid => '未付款';

  @override
  String get order_inspection_status_returned => '已退回';

  @override
  String get order_inspection_status_waiting_start => '等待開始';

  @override
  String get order_detail_related_info => '關聯子訂單';

  @override
  String get order_detail_inspection_product => '產品名稱';

  @override
  String get order_detail_inspection_time => '驗貨時間';

  @override
  String get order_detail_inspection_city => '驗貨城市';

  @override
  String get order_detail_inspection_factory => '驗貨工廠';

  @override
  String get order_detail_inspection_address => '驗貨地址';

  @override
  String get order_detail_inspection_person => '聯繫人';

  @override
  String get order_detail_inspection_phone => '聯繫人電話';

  @override
  String get order_detail_inspection_email => '聯繫人郵箱';

  @override
  String get order_detail_inspection_amount => '價格信息';

  @override
  String get order_detail_inspection_sample => '樣品';

  @override
  String get order_detail_inspection_standard => '抽樣等級';

  @override
  String get order_detail_inspection_critical => 'AQL';

  @override
  String get order_detail_inspection_major => 'Major';

  @override
  String get order_detail_inspection_minor => 'Minor';

  @override
  String get order_detail_inspection_day => '驗貨天數';

  @override
  String get order_detail_inspection_number => '驗貨人數';

  @override
  String get order_detail_inspection_price => '驗貨價格';

  @override
  String get order_detail_inspection_price_apply => '申請價格';

  @override
  String get order_detail_inspection_template => '驗貨模板';

  @override
  String get order_detail_inspection_file => '附件';

  @override
  String get order_detail_inspection_image => '圖片';

  @override
  String get order_detail_inspection_type => '驗貨類型';

  @override
  String get order_tips => '樣品及注意事項';

  @override
  String get order_apply => '申請驗貨';

  @override
  String get order_cancel => '取消';

  @override
  String get order_canceled => '已取消';

  @override
  String get order_dispatch_accept => '同意';

  @override
  String get order_dispatch_refuse => '拒絕';

  @override
  String get order_inspection => '開始驗貨';

  @override
  String get order_sure_cancel => '確定取消訂單';

  @override
  String get order_sure_refuse => '確定拒絕此訂單嗎？';

  @override
  String get order_sure_confirm => '確定接受此訂單嗎？';

  @override
  String get publish_title => '發布訂單';

  @override
  String get publish_edit_title => '編輯訂單';

  @override
  String get publish_welcome => '歡迎下單，報價自由，與驗貨員溝通自由';

  @override
  String get publish_sampling => '抽樣檢驗';

  @override
  String get publish_sampling_content => '適用於產品100%完成，至少80%產品已包裝好，準備出貨前，我們根據國際抽樣方案ANSI/ASQZ1.4(MIL-STD-105E)以及參照您的特殊要求進行隨機抽樣檢驗，在抽樣檢驗報告中，我們將全面反映產品的完成數量，包裝情況以及是否符合AQL（可接受質量水平）要求，讓您在出貨前對整批次的產品質量有全面的了解，避免讓您的訂單遭受任何風險';

  @override
  String get publish_point => '檢驗要點';

  @override
  String get publish_sampling_point => '● 客戶資料/樣品核對\n● 完成數量核對\n● 產品尺寸、款式、顏色核對\n● 外觀工藝檢查\n● 產品功能和安全性檢測\n● 箱唛檢查\n● 包裝完整性\n● 具體包裝細節\n● 客戶特殊要求';

  @override
  String get publish_all => '全數檢驗';

  @override
  String get publish_all_content => '全數檢驗可以在包裝前或者包裝後進行，根據客戶要求，對每一件產品的外觀、尺寸、工藝、功能及安全性等進行檢驗，區分良品與不良品，並將檢品結果及時彙報給客戶';

  @override
  String get publish_online => '在線檢驗';

  @override
  String get publish_online_content => '在線檢驗是在生產過程中檢驗或者全部生產完成包裝之前檢驗，可以及時幫您確認產品的質量，功能，外觀及其它要素是否在整個生產過程都同您的規格要求保持一致，同時也有利於儘早發現任何不符點，從而降低工廠延遲交貨的風險';

  @override
  String get publish_online_point => '● 生產情況跟進\n● 生產線評估及生產進度確認\n● 抽檢半成品和成品\n● 檢查包裝信息及包裝材料\n● 使有缺陷的產品改進\n● 評估交付時間';

  @override
  String get publish_factory => '工廠審核';

  @override
  String get publish_factory_content => '工廠審核主要採用客觀判斷法，依據事先制定好的標準或準則對工廠進行量化考核和審定，根據現場打分評比以及對工廠的綜合審核結果等形成評估報告，以供客戶判定該工廠是否作為其合格供應商的依據';

  @override
  String get publish_factory_point_title => '審計內容';

  @override
  String get publish_factory_point => '● 工廠概況（基本信息）\n● 組織架構\n● 生產流程\n● 生產能力\n● 研發技術能力\n● 機械設備和設施';

  @override
  String get publish_factory_review => '綜合評價';

  @override
  String get publish_factory_review_content => '● 針對每個審核項目，權衡彼此的重要性，分別給予不同的分數，再根據審核調查表及實地調查的資料，出具資格評分表';

  @override
  String get publish_watch => '監裝';

  @override
  String get publish_watch_content => '貨櫃監裝主要包括評估貨櫃狀況、核對產品信息、清點裝櫃的箱數，檢查包裝信息及監督整個裝櫃過程。為了降低貨物在裝櫃後被替換的高風險，檢驗員在裝箱現場進行監督，以確保您支付的產品安全裝箱';

  @override
  String get publish_watch_point => '● 記錄貨櫃號碼和拖車號碼\n● 檢查貨櫃是否存在破損、潮濕和特殊氣味，並對空櫃進行拍照\n● 檢查待裝箱數以及外包裝狀況，隨機抽查幾箱以確認實際裝箱的產品\n● 監督裝櫃過程，以確保破損最小化和空間利用最大化\n● 對貨櫃封門情況，貨櫃鉛封號，裝箱單進行拍照留底，記錄貨櫃離開時間';

  @override
  String get publish_watch_inspection => '驗貨+監裝';

  @override
  String get publish_watch_inspection_content => '為了確保產品的最終質量及完成情況，在準備出貨前，我們根據國際抽樣方案隨機從成品中抽取樣品進行抽樣檢查，並核對客戶提供的資料，對整個裝櫃流程進行全程監控';

  @override
  String get publish_watch_inspection_point => '● 在集裝箱未到達前核對客戶資料/樣品比對，抽樣檢驗產品的外觀工藝、功能和安全性以及產品包裝、箱唛等\n● 發現不良品及時與工廠溝通，進行替換或者返工\n● 檢查貨櫃是否存在破損、潮濕和特殊氣味，並對空櫃進行拍照\n● 監督裝櫃過程，以確保破損最小化和空間利用最大化\n● 對貨櫃封門情況，貨櫃鉛封號，裝箱單進行拍照留底，記錄貨櫃離開時間';

  @override
  String get publish_next => '下一步';

  @override
  String get publish_inspection_time => '驗貨時間';

  @override
  String get publish_inspection_time_selected => '選擇驗貨時間';

  @override
  String get publish_inspection_time_tip => '請選擇';

  @override
  String get publish_inspection_people => '驗貨人數';

  @override
  String get publish_people => '人';

  @override
  String get publish_day => '天';

  @override
  String get publish_inspection_factory => '驗貨工廠';

  @override
  String get publish_factory_tips => '輸入驗貨工廠';

  @override
  String get publish_address_book => '地址簿';

  @override
  String get publish_goods_name => '產品名稱';

  @override
  String get publish_name_tips => '多種產品時輸入一兩個代表名稱';

  @override
  String get publish_po_tips => '請輸入P.O號';

  @override
  String get publish_file_tips => '上傳附件';

  @override
  String get publish_camera => '拍照上傳';

  @override
  String get publish_file => '文件上傳';

  @override
  String get publish_purchase => '發布採購訂單';

  @override
  String get publish_inspection => '發布驗貨訂單';

  @override
  String get publish_factory_tip => '請先選擇驗貨地址等信息';

  @override
  String get publish_attention => '注意事項';

  @override
  String get publish_attention_tips => '請輸入哪些問題需要驗貨員重點關注';

  @override
  String get publish_stand_price => '一口價';

  @override
  String get publish_click_price => '切換';

  @override
  String get publish_vip_price => 'VIP價';

  @override
  String get publish_vip_tips => '提供人工全程跟單等服務';

  @override
  String get publish_total => '合計';

  @override
  String get publish_submit => '提交';

  @override
  String get publish_only_price_failed => '無一口價權限';

  @override
  String get publish_price_tip => '請選擇價格';

  @override
  String get publish_date_tips => '請選擇日期';

  @override
  String get date_title => '驗貨日期';

  @override
  String get date_save => '保存';

  @override
  String get address_title => '編輯工廠信息';

  @override
  String get address_auto_tips => '請粘貼或輸入文本，點擊“識別”自動工廠名稱、姓名電話、地址等';

  @override
  String get address_paste => '粘貼';

  @override
  String get address_ocr => '識別';

  @override
  String get address_name => '工廠名稱';

  @override
  String get address_name_tip => '請輸入驗貨工廠信息';

  @override
  String get address_person => '聯繫人';

  @override
  String get address_person_tip => '請輸入聯繫人';

  @override
  String get address_mobile => '手機號';

  @override
  String get address_mobile_tip => '請輸入手機號';

  @override
  String get address_email => '郵箱';

  @override
  String get address_email_tip => '請輸入郵箱號';

  @override
  String get address_area => '省市區';

  @override
  String get address_area_tip => '請選擇省-市-區  〉';

  @override
  String get address_detail => '詳細地址';

  @override
  String get address_detail_tip => '輸入街道、門牌號等信息';

  @override
  String get address_location => '定位';

  @override
  String get address_save_tip => '保存到地址簿';

  @override
  String get address_clear => '清除';

  @override
  String get address_submit => '提交';

  @override
  String get address_recent => '最近使用地址';

  @override
  String get address_more => '更多地址';

  @override
  String get address_list_title => '地址管理';

  @override
  String get address_insert => '添加地址';

  @override
  String get address_delete => '刪除';

  @override
  String get address_delete_result => '刪除失敗';

  @override
  String get address_edit => '編輯';

  @override
  String get address_delete_tips => '確認刪除地址?';

  @override
  String get address_detected_paste => '檢測到地址信息，是否使用該地址';

  @override
  String get pay_title => '支付訂單';

  @override
  String get pay_time => '支付剩餘時間';

  @override
  String get pay_paypal => 'PayPal';

  @override
  String get pay_zfb => '支付寶';

  @override
  String get pay_usd => '美元賬戶';

  @override
  String get pay_rmb => '人民幣賬戶';

  @override
  String get pay_pay => '支付';

  @override
  String get pay_result_success => '支付成功';

  @override
  String get pay_result_success_wait => '支付成功，等待訂單處理中。';

  @override
  String get pay_result_failed => '支付失敗';

  @override
  String get pay_keep => '可記賬';

  @override
  String get check_title => '驗貨報告';

  @override
  String get check_picture => '驗貨圖片';

  @override
  String get check_file => '廉政承諾函';

  @override
  String get check_report => '廉政承諾書+手寫報告';

  @override
  String get check_draft => '草稿報告';

  @override
  String get check_template => '報告模板';

  @override
  String get check_submit => '提交';

  @override
  String get check_hint => '請輸入圖片描述';

  @override
  String get check_checking => '報告審核中';

  @override
  String get check_check_success => '報告審核通過';

  @override
  String get check_check_failed => '報告審核失敗，請修改內容後重新提交';

  @override
  String get review_title => '服務評價';

  @override
  String get review_next => '去評價';

  @override
  String get contact_bnt => '馬上溝通';

  @override
  String get review_score => '服務水平';

  @override
  String get review_score1 => '失望';

  @override
  String get review_score2 => '不滿';

  @override
  String get review_score3 => '一般';

  @override
  String get review_score4 => '滿意';

  @override
  String get review_score5 => '驚喜';

  @override
  String get review_tips => '多種角度評價，以幫助我們進一步了解驗貨員的工作能力';

  @override
  String get review_picture => '圖片';

  @override
  String get review_submit => '提交';

  @override
  String get setting_title => '設置';

  @override
  String get setting_address => '地址管理';

  @override
  String get setting_clear_cache => '清除緩存';

  @override
  String get setting_clear_success => '清理成功';

  @override
  String get setting_about_us => '關於我們';

  @override
  String get setting_receive_msg => '接收消息提醒';

  @override
  String get setting_version => '版本號';

  @override
  String get setting_check_update => '檢查更新';

  @override
  String get setting_login_out => '退出登錄';

  @override
  String get setting_login_out_tips => '是否確定退出登錄？';

  @override
  String get setting_delete_account_tips => '是否確定刪除賬號？';

  @override
  String get setting_policy_title => '隱私政策授權提示';

  @override
  String get setting_policy_sub_title => '進入下一步前，請先閱讀並同意';

  @override
  String get setting_privacy_policy => '隱私協議';

  @override
  String get setting_user_agreement => '用戶協議';

  @override
  String get setting_privacy_content => '歡迎使用驗貨在線App\n我們非常重視您的隱私和個人信息保護，在您使用本App的過程中，我們會對您的部分個人信息進行收集和使用\n1. 在您同意App隱私政策後，我們將進行集成SDK的初始化工作，會收集您的設備MAC地址、IMSI、Android ID、IP地址、硬件型號、操作系統版本號、唯一設備標識符（IMEI等）、網絡設備硬件地址（MAC）、軟件版本號、網絡接入方式、類型、狀態、網絡質量數據、操作日誌、硬件序列號、服務日誌信息等以保障App正常數據統計及安全風控。\n2. 未經您的同意，我們不會從第三方獲取、共享或對外提供您的信息。\n3. 您可以訪問、更正、刪除您的個人信息，我們也將提供註銷、投訴方式。\n';

  @override
  String get setting_ihavereadandagreed => '我已閱讀並同意';

  @override
  String get setting_policy_tips2 => '請認真閱讀並理解';

  @override
  String get wallet_title => '錢包';

  @override
  String get wallet_bill => '賬單';

  @override
  String get wallet_rmb_account => '人民幣賬戶';

  @override
  String get wallet_usd_account => '美元賬戶';

  @override
  String get wallet_account_heading => '提現賬戶及設置';

  @override
  String get wallet_bank => '銀行卡';

  @override
  String get wallet_wechat => '微信';

  @override
  String get wallet_alipay => '支付寶';

  @override
  String get wallet_charge => '充值';

  @override
  String get wallet_cash => '提現';

  @override
  String get wallet_balance => '餘額';

  @override
  String get wallet_default_account => '默認賬戶';

  @override
  String get wallet_set_default_account => '設為默認賬戶';

  @override
  String get bill_title => '賬單';

  @override
  String get bill_out => '支出';

  @override
  String get bill_in => '收入';

  @override
  String get bill_month => '月';

  @override
  String get bill_fenxi => '收支分析';

  @override
  String get bill_unfreeze => '解凍';

  @override
  String get bill_all => '全部賬單';

  @override
  String get bill_income => '收入';

  @override
  String get bill_outcome => '支出';

  @override
  String get bill_freeze => '凍結';

  @override
  String get bill_withdraw => '提現';

  @override
  String get bank_title => '我的銀行卡';

  @override
  String get bank_add => '添加銀行卡';

  @override
  String get add_bank_title => '添加銀行卡';

  @override
  String get add_bank_name => '銀行卡名稱';

  @override
  String get add_bank_branch => '開戶行';

  @override
  String get add_bank_card => '卡號';

  @override
  String get add_bank_real_name => '姓名';

  @override
  String get add_bank_address => '開戶地址';

  @override
  String bind_title(Object bind) {
    return '綁定$bind';
  }

  @override
  String get bind_account => '賬戶';

  @override
  String get bind_image => '收款碼';

  @override
  String get bind_name => '姓名';

  @override
  String get bind_hint => '請輸入';

  @override
  String get charge_title => '充值';

  @override
  String get charge_account => '充值賬戶';

  @override
  String get charge_money => '充值金額';

  @override
  String get charge_deposit_type_title => '充值方式';

  @override
  String get charge_deposit_type_online => '在線充值';

  @override
  String get charge_deposit_type_offline => '線下轉賬';

  @override
  String get charge_offline_nopic_hint => '請上傳充值憑證';

  @override
  String get charge_upload_proof => '請上傳轉賬憑據';

  @override
  String get withdraw_list_title => '提現歷史';

  @override
  String get withdraw_rmb => '人民幣提現';

  @override
  String get withdraw_usd => '美元提現';

  @override
  String get withdraw_status_checking => '審核中';

  @override
  String get withdraw_status_approved => '審核通過';

  @override
  String get withdraw_status_denied => '審核不通過';

  @override
  String get withdraw_cash_status_unfinished => '未打款';

  @override
  String get withdraw_cash_status_done => '已打款';

  @override
  String get withdraw_cash_status_refused => '駁回';

  @override
  String get charge_hint => '請輸入充值金額';

  @override
  String get charge_submit => '確定';

  @override
  String get charge_rmb => '人民幣充值';

  @override
  String get charge_usd => '美元充值';

  @override
  String get charge_history_title => '充值歷史';

  @override
  String get cash_title => '提現';

  @override
  String get cash_account => '選擇提現賬戶';

  @override
  String get cash_money => '提現金額';

  @override
  String get cash_invoice_money => '發票金額';

  @override
  String get cash_invoice_money_hint => '請輸入發票金額';

  @override
  String get cash_invoice_upload => '發票上傳';

  @override
  String get cash_account_list_title => '申請通過後，提現款將隨機打入以下賬戶：';

  @override
  String get cash_hint => '請輸入提現金額';

  @override
  String get cash_withdraw_tips1 => '您正在提現至';

  @override
  String get cash_withdraw_tips2 => ', 提現金額為';

  @override
  String get cash_amount => '到賬金額';

  @override
  String get cash_other => '手續費';

  @override
  String get cash_submit => '確定';

  @override
  String get location_permission => '需要使用定位權限，請開啟';

  @override
  String get location_cancel => '取消';

  @override
  String get location_author => '去授權';

  @override
  String get group_title => '群組成員';

  @override
  String get unknown_error => '未知錯誤';

  @override
  String get data_parsing_exception => '數據解析異常';

  @override
  String get edit => '編輯';

  @override
  String get no_data => '暫無數據';

  @override
  String get note => '備註說明';

  @override
  String get msg_locating => '定位中';

  @override
  String get failed_to_download => '下載更新失敗';

  @override
  String get pick_address => '點擊輸入工廠地址';

  @override
  String get update_now => '立即更新';

  @override
  String get message => '消息';

  @override
  String get view_order => '查看訂單';

  @override
  String get today => '今天';

  @override
  String get yesterday => '昨天';

  @override
  String get send_file => '發送文件';

  @override
  String get login_expired => '登錄已過期,請重新登錄';

  @override
  String get exit_group_chat_confirm => '確定退出群聊?';

  @override
  String get exit_group_chat_success => '已退出群聊';

  @override
  String get exit_group_chat_page_title => '聊天信息';

  @override
  String get exit_group_chat_button_title => '退出群聊';

  @override
  String get group_chat_setting_view_more => '查看更多群成員';

  @override
  String get group_chat_setting_name => '群聊名稱';

  @override
  String get group_chat_setting_owner_update => '只允許群主修改群名稱';

  @override
  String get group_chat_name_page_title => '修改群聊名稱';

  @override
  String get group_chat_name_page_required => '請輸入群聊名稱';

  @override
  String get group_chat_name_save => '保存';

  @override
  String get group_chat_name_saved => '群聊名稱已修改';

  @override
  String get conversation_manage_view_please => '請選擇需要操作的會話';

  @override
  String get conversation_manage_view_list => '會話列表';

  @override
  String get group_manage_select => '請選擇需要操作的群組';

  @override
  String get group_manage_list => '群組列表';

  @override
  String get please_enter => '請輸入';

  @override
  String get address_keyword => '請輸入地址關鍵字';

  @override
  String get inspector_min_fee => '請輸入最低驗貨費用';

  @override
  String get inspector_id_card_required => '請輸入身份證號';

  @override
  String get inspector_id_card_upload => '請上傳身份證照片';

  @override
  String get inspector_id_card_upload_fail => '身份證照片上傳出錯,請重新上傳';

  @override
  String get inspector_revoke => '確定撤銷驗貨員資格？';

  @override
  String get inspector_revoke_completed => '已撤銷驗貨員資格';

  @override
  String get male => '男';

  @override
  String get female => '女';

  @override
  String get elementary => '小學';

  @override
  String get junior => '初中';

  @override
  String get technical => '中專';

  @override
  String get senior => '高中';

  @override
  String get college => '大專';

  @override
  String get bachelor => '本科';

  @override
  String get master => '碩士';

  @override
  String get doctor => '博士';

  @override
  String get yes => '有';

  @override
  String get no => '無';

  @override
  String get upload_image => '上傳圖片';

  @override
  String get upload_file => '上傳文件';

  @override
  String get revoke_inspector => '撤銷驗貨員資格';

  @override
  String get deposit_card => '儲蓄卡';

  @override
  String get withdrawal_balance => '提現金額不能超過賬戶餘額';

  @override
  String get failed_get_payment_info => '獲取支付信息失敗';

  @override
  String get recommended_order => '推薦下單';

  @override
  String get withdrawal_method => '請提供至少一種提現方式';

  @override
  String get withdrawal_bind_alipay => '請先綁定支付寶';

  @override
  String get enabled_camera => '請設置允許使用相機拍照';

  @override
  String get valid_email_mobile => '請輸入正確的郵箱地址或手機號';

  @override
  String get apple_map => '蘋果地圖';

  @override
  String get baidu_map => '百度地圖';

  @override
  String get amap => '高德地圖';

  @override
  String get google_map => '谷歌地圖';

  @override
  String get tencent_map => '騰訊地圖';

  @override
  String get image_format => '圖片格式需為png,jpg,jpeg';

  @override
  String get enable_location_service => '需開啟定位權限';

  @override
  String get enable_location_service_tips => '開啟定位權限，可精準查找周邊驗貨訂單';

  @override
  String get enable_permission_not_now => '暫不設置';

  @override
  String get enable_permission_goto_setting => '去設置';

  @override
  String get failed_location_service => '獲取位置信息出錯';

  @override
  String get turn_on_location_service => '請打開手機定位服務';

  @override
  String get no_install_map => '您未安裝地圖';

  @override
  String get camera => '相機';

  @override
  String get photo_album => '相冊';

  @override
  String get new_version => '版本全新上線';

  @override
  String get invalid_mail => '用戶郵箱不存在';

  @override
  String get invalid_password => '密碼錯誤';

  @override
  String get invalid_mobile => '手機號不存在';

  @override
  String get invalid_auth_code => '驗證碼不正確';

  @override
  String get invalid_login => '登錄失敗，請重試';

  @override
  String get grabbing => '搶單中';

  @override
  String get hour_ago => '小時前發布';

  @override
  String get minute_ago => '分鐘前發布';

  @override
  String get report_type => '報告類型';

  @override
  String get fri => '抽檢';

  @override
  String get fui => '';

  @override
  String get oli => '';

  @override
  String get fat => '';

  @override
  String get cls => '監櫃';

  @override
  String get fri_cls => '';

  @override
  String get order_payment => '訂單支付';

  @override
  String get order_refund => '訂單退回';

  @override
  String get expend_withdrawal => '支出-提現';

  @override
  String get incoming_refund => '收入-訂單退款';

  @override
  String get incoming_recharge => '收入-充值';

  @override
  String get chat_not_member => '你已經不是群組成員，不能發送信息';

  @override
  String get admins => '聯繫客服';

  @override
  String get theme_title => '主題';

  @override
  String get theme_light => '亮色主題';

  @override
  String get theme_dark => '暗色主題';

  @override
  String get theme_auto => '跟隨系統';

  @override
  String get amount_total => '總額';

  @override
  String get amount_available => '可用';

  @override
  String get amount_blocked => '凍結';

  @override
  String get download => '點擊下載';

  @override
  String get downloading => '正在下載';

  @override
  String get saved => '已保存';

  @override
  String get order_number => '訂單編號';

  @override
  String get order_detail_inspection_cost => '驗貨費用';

  @override
  String get delete_account => '刪除賬號';

  @override
  String get delete_account_confirm => '所有信息都將不會保留。\n確定刪除嗎？';

  @override
  String get delete_account_result => '賬號已刪除';

  @override
  String get not_exist_account => '賬號不存在';

  @override
  String get new_password => '輸入新密碼';

  @override
  String get supervisor => '跟單員';

  @override
  String get downloadFiles => '下載的文件';

  @override
  String get home_search_hint_inspector => '按城市/產品名搜索訂單';

  @override
  String get home_search_hint_admin => '按城市/產品名搜索訂單';

  @override
  String get search_recent_history => '最近搜索';

  @override
  String get assign => '指派';

  @override
  String get assigned => '已指派';

  @override
  String get approve => '通過';

  @override
  String get assign_inspector => '指派驗貨員';

  @override
  String get unassigned => '未指派';

  @override
  String get general_all => '全部';

  @override
  String get general_date => '日期';

  @override
  String get general_desc => '說明';

  @override
  String get general_amount => '金額';

  @override
  String get assign_search_hint => '請輸入暱稱/姓名/郵箱/手機號';

  @override
  String get assign_cancel_message => '確認取消指派該驗貨員';

  @override
  String get assign_inspect_times => '驗貨次數';

  @override
  String get assign_leave_message_batch => '批量留言';

  @override
  String get assign_price_zero_tips => '驗貨費用不能為0';

  @override
  String get assign_applied => '已申請';

  @override
  String get apply_time => '申請於';

  @override
  String get assign_message => '留言';

  @override
  String get chat_send_message => '發消息';

  @override
  String get chat_send_order => '發送訂單';

  @override
  String get chat_panel_album => '相冊';

  @override
  String get chat_panel_camera => '拍照';

  @override
  String get chat_panel_file => '文件';

  @override
  String get chat_toolbar_custom_service => '專屬客服';

  @override
  String get chat_toolbar_submit_order => '驗貨下單';

  @override
  String get home_navigation => '點擊導航';

  @override
  String get price_input_error_zero => '訂單必須在0元與100萬元之間';

  @override
  String get filter_all => '全部篩選';

  @override
  String get filter_heading_order_status => '按訂單狀態';

  @override
  String get filter_heading_insp_date => '按驗貨日期';

  @override
  String get filter_heading_order_date => '按發布日期';

  @override
  String get filter_heading_area => '按地區';

  @override
  String get filter_date_start => '起始時間';

  @override
  String get filter_date_end => '截止時間';

  @override
  String get filter_date_today => '今日訂單';

  @override
  String get filter_date_tomorrow => '明日訂單';

  @override
  String get filter_date_2days_later => '2日內訂單';

  @override
  String get filter_date_3days_later => '3日內訂單';

  @override
  String get sort_by_order_date => '按訂單日期排序';

  @override
  String get sort_by_insp_date => '按驗貨日期排序';

  @override
  String get sort_by_distance => '按距離排序';

  @override
  String get purchase_all_replies => '全部回覆';

  @override
  String get purchase_replies_count => '條回覆';

  @override
  String get purchase_no_more_replies => '暫無更多回覆';

  @override
  String get purchase_save_draft_title => '是否保存草稿';

  @override
  String get purchase_save_draft_choice => '保存為草稿';

  @override
  String get purchase_save_draft_quit => '直接退出';

  @override
  String get purchase_search_hint => '搜索採購訂單';

  @override
  String get purchase_reply_hint => '回覆帖子內容';

  @override
  String get purchase_complaint_hint => '提供更多信息有助於舉報被快速處理';

  @override
  String get purchase_reply_paid_hint => '輸入懸賞內容';

  @override
  String get purchase_edit => '編輯帖子';

  @override
  String get purchase_publish => '懸賞求購';

  @override
  String get purchase_publish_product_label => '產品名稱';

  @override
  String get purchase_publish_title_label => '懸賞標題';

  @override
  String get purchase_publish_content_label => '詳細描述';

  @override
  String get purchase_publish_product_hint => '請輸入產品名稱';

  @override
  String get purchase_publish_title_hint => '可寫明名稱型號、地區、數量等信息';

  @override
  String get purchase_publish_content_hint => '請輸入詳細描述';

  @override
  String get purchase_publish_price => '懸賞價格';

  @override
  String get purchase_publish_choose_category => '選擇分類';

  @override
  String get purchase_publish_choose_category_hint => '請選擇分類';

  @override
  String get purchase_paid_publish_switch => '有償查看';

  @override
  String get purchase_paid_publish_set_price => '設置價格';

  @override
  String get purchase_detail_response_all => '全部回覆';

  @override
  String get purchase_detail_response_author_only => '只看樓主';

  @override
  String get purchase_detail_response_asc => '正序';

  @override
  String get purchase_detail_response_desc => '倒序';

  @override
  String get purchase_detail_more_reply => '回覆';

  @override
  String get purchase_detail_more_up => '讚';

  @override
  String get purchase_detail_more_cancel_up => '取消讚';

  @override
  String get purchase_my_posts => '主貼';

  @override
  String get purchase_my_replies => '回覆';

  @override
  String get purchase_my_appeals => '申訴';

  @override
  String get purchase_appeal_detail => '申訴詳情';

  @override
  String get purchase_appeal_submit => '提交申訴';

  @override
  String get purchase_appeal_cancel => '取消申訴';

  @override
  String get purchase_appeal_approve => '申訴已通過';

  @override
  String get purchase_appeal_denied => '申訴已駁回';

  @override
  String get purchase_paid_content_owner_tips => '付費內容';

  @override
  String get purchase_paid_content_tips => '付費內容，支付查看';

  @override
  String get purchase_paid_content_paid_tips => '付費內容已解鎖';

  @override
  String get purchase_review_leave => '評價一下';

  @override
  String get purchase_review_my_score => '我的評價';

  @override
  String get purchase_my_replies_original_header => '原帖';

  @override
  String get purchase_publish_bounty_tips => '注：懸賞金額可自由選填，較高的懸賞金能吸引更多的驗貨員積極為您提供您需要的信息。';

  @override
  String get purchase_reply_to => '回覆';

  @override
  String get purchase_modify_bounty => '修改懸賞';

  @override
  String get purchase_bounty_money => '懸賞';

  @override
  String get purchase_evaluated_person => '人已評價';

  @override
  String get purchase_comment_paid_supplier => '供應商';

  @override
  String get purchase_comment_paid_contact => '聯繫人';

  @override
  String get purchase_comment_paid_phone => '聯繫電話';

  @override
  String get purchase_comment_paid_email => '郵箱';

  @override
  String get purchase_comment_paid_address => '工廠地址';

  @override
  String get purchase_comment_paid_other => '其它';

  @override
  String get purchase_comment_paid_low_price => '產品底價';

  @override
  String get purchase_appeal_title => '申請退款';

  @override
  String get purchase_appeal_reason => '請輸入申訴理由';

  @override
  String get purchase_appeal_request_price => '申訴金額：';

  @override
  String get purchase_appeal_request_reason => '申訴理由：';

  @override
  String get purchase_post_status_draft => '草稿';

  @override
  String get purchase_post_status_reviewing => '審核中';

  @override
  String get purchase_post_status_published => '審核通過';

  @override
  String get purchase_post_status_denied => '未通過';

  @override
  String get purchase_post_publish => '發布帖子';

  @override
  String get purchase_complaint_type_leading => '請選擇舉報類型';

  @override
  String get purchase_complaint_type_1 => '色情低俗';

  @override
  String get purchase_complaint_type_2 => '垃圾廣告';

  @override
  String get purchase_complaint_type_3 => '辱罵攻擊';

  @override
  String get purchase_complaint_type_4 => '違法犯罪';

  @override
  String get purchase_complaint_type_5 => '時政不實信息';

  @override
  String get purchase_complaint_type_6 => '侵犯權益';

  @override
  String get purchase_complaint_type_7 => '其他';

  @override
  String get shop_goods_detail_title => '商品詳情';

  @override
  String get mall_buy_immediate => '立即購買';

  @override
  String get mall_goods_count => '數量';

  @override
  String get mall_confirm_pay => '確認支付';

  @override
  String get mall_order_confirm => '訂單確認';

  @override
  String get mall_submit_order => '提交訂單';

  @override
  String get mall_goods_price => '商品金額';

  @override
  String get mall_express_price => '運費';

  @override
  String get mall_price_total => '合計：';

  @override
  String get mall_payment => '收銀台';

  @override
  String get mall_payment_methods => '支付方式';

  @override
  String get mall_pay_succeed => '支付成功';

  @override
  String get mall_check_order_detail => '查看訂單詳情';

  @override
  String get mall_order_remark => '訂單備註';

  @override
  String get mall_order_remark_input => '填寫備註';

  @override
  String get purchase_detail_more_report => '舉報';

  @override
  String get purchase_reply_paid_content_tips => '注：請填寫真實信息，有償回覆發表後需等待審核，審核之後才能被他人查看到';

  @override
  String get public_ip_address => 'IP屬地:';

  @override
  String get inspection_widget_suit_tips => '驗貨時需佩戴工牌或穿工作服，若還沒有可在首頁進入購買';

  @override
  String get purchase_paid_content_appeal => '申訴';

  @override
  String get report_success => '舉報成功';

  @override
  String tabbar_tab_names(String names) {
    String _temp0 = intl.Intl.selectLogic(
      names,
      {
        'tab_home': '首頁',
        'tab_shortcut': '快捷',
        'tab_message': '消息',
        'tab_mine': '我的',
        'tab_publish': '',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String order_status_desc(String status) {
    String _temp0 = intl.Intl.selectLogic(
      status,
      {
        'order_status_unknown': '未知',
        'order_wait_pay': '待支付',
        'order_cancelled': '已取消',
        'order_cancelled_refund_pending': '已取消,退款待審核',
        'order_refund_pending': '退款待審核',
        'order_refund_partial': '部分退款',
        'order_refund_denied': '拒絕退款',
        'order_wait_dispatch': '待派單',
        'order_doing': '驗貨中',
        'order_finished': '已完成',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String pay_type(String type) {
    String _temp0 = intl.Intl.selectLogic(
      type,
      {
        'pay_rmb': '人民幣賬戶',
        'pay_usd': '美元賬戶',
        'pay_zfb': '支付寶',
        'pay_paypal': 'PayPal',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String purchase_complaint_type(String type) {
    String _temp0 = intl.Intl.selectLogic(
      type,
      {
        'type_1': '色情低俗',
        'type_2': '垃圾廣告',
        'type_3': '辱罵攻擊',
        'type_4': '違法犯罪',
        'type_5': '時政不實信息',
        'type_6': '侵犯權益',
        'type_7': '其他',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String get purchase_reply_paid_my_evaluate => '（我的評價）';

  @override
  String get bind_now => '立即綁定';

  @override
  String get cancel_register => '取消註冊';

  @override
  String register_and_bind_email0(Object email) {
    return '註冊並綁定$email為主郵箱';
  }

  @override
  String register_and_bind_email1(Object email) {
    return '您的郵箱$email已註冊，繼續綁定';
  }

  @override
  String get register_and_bind_email2 => '註冊並綁定主郵箱';

  @override
  String get new_register_bind_title => '設置主登錄郵箱並註冊';

  @override
  String get new_register_bind_field_account_tips => '綁定郵箱/手機號';

  @override
  String get register => '註冊';

  @override
  String get switch_account => '切換賬號';

  @override
  String get switch_account_confirm_tips => '確定切換賬戶?';

  @override
  String get password_must_have => '您的密碼必須包含：';

  @override
  String get password_must_have_1 => '長度為 8-32 個字符';

  @override
  String get password_must_have_2 => '1 個小寫字母 (a-z)';

  @override
  String get password_must_have_3 => '1 個數字';

  @override
  String get password_must_have_4 => '1 個符號 (如!@#\$%^&*)';

  @override
  String get password_login => '輸入登錄密碼';

  @override
  String get password_login_again => '再次輸入新密碼';

  @override
  String get choose_account_to_login => '選擇賬號快速登錄';

  @override
  String get finish => '完成';

  @override
  String get done => '完成';

  @override
  String get account_apple => '蘋果登錄';

  @override
  String get account_google => '谷歌登錄';

  @override
  String get account_wechat => '微信登錄';

  @override
  String get account_facebook => 'Facebook登錄';

  @override
  String get third_account_unbind => '解除綁定';

  @override
  String get third_account_bind => '綁定';

  @override
  String get confirm_unbind => '確定解綁?';

  @override
  String get inspection_requirement => '驗貨須知';

  @override
  String get liveroom_entrance => '直播間排單';

  @override
  String get add_account => '新增賬戶';

  @override
  String get ai_category_inspector => '驗貨';

  @override
  String get ai_nothing_category => '無可檢測類別';

  @override
  String get ai_category_name => '類別名稱';

  @override
  String get ai_quantity => '數量';

  @override
  String get ai_packaging => '包裝';

  @override
  String get ai_shipping_mark => '運輸標誌';

  @override
  String get ai_product_style => '產品風格';

  @override
  String get ai_test => '測試';

  @override
  String get ai_craftsmanship => '工藝';

  @override
  String get ai_test_verification => '測試驗證';

  @override
  String get ai_category_measure => '類別測量';

  @override
  String get ai_spare_parts => '備品';

  @override
  String get ai_sampling_number => '取樣編號';

  @override
  String ai_input_range_number(Object range) {
    return '請輸入$range內的數字';
  }

  @override
  String ai_enter_range_number(Object range) {
    return '請輸入$range內的數字';
  }

  @override
  String get ai_selected => '已選擇';

  @override
  String get ai_selected_status => '選擇狀態';

  @override
  String get ai_order_quantity => '訂單數量';

  @override
  String get ai_packaged_boxes_quantity => '包裝完成箱數（成品）';

  @override
  String get ai_unpackaged_boxes_quantity => '未包裝箱數（成品）';

  @override
  String get ai_sample_from_packaged => '從包裝完成取樣';

  @override
  String get ai_sample_from_unpackaged => '從未包裝取樣';

  @override
  String get ai_spare_parts_quantity => '備品數量';

  @override
  String get ai_sampling_packaging_number => '取樣包裝編號';

  @override
  String get ai_sampling_packaging_number_record => '取樣包裝編號記錄';

  @override
  String get ai_sampling_packaging_number_list => '取樣外包裝編號列表';

  @override
  String get ai_judgment => '判斷';

  @override
  String get ai_judgment_item => '判斷項目';

  @override
  String get ai_standard => '標準';

  @override
  String get ai_result => '結果';

  @override
  String get ai_conclusion => '結論';

  @override
  String get ai_overall_conclusion => '總結論';

  @override
  String get ai_consistency => '一致性';

  @override
  String get ai_yes => '是';

  @override
  String get ai_no => '否';

  @override
  String get ai_remarks => '備註';

  @override
  String get ai_numerical => '數字';

  @override
  String get ai_recommended_test_items => '推薦測試項目';

  @override
  String get ai_test_item => '測試項目';

  @override
  String get ai_add_all => '全部新增';

  @override
  String get ai_add_plus => '+ 新增';

  @override
  String get ai_add => '新增';

  @override
  String ai_confirm_delete(Object name) {
    return '確定要刪除$name嗎？';
  }

  @override
  String get ai_enter_test_item => '請輸入測試項目';

  @override
  String get ai_defect_record => '缺陷記錄';

  @override
  String get ai_defect_photo => '缺陷照片';

  @override
  String get ai_defect_description => '缺陷描述';

  @override
  String get ai_defect_level => '缺陷等級';

  @override
  String get ai_found_quantity => '發現數量';

  @override
  String get ai_handling_method => '處理方式';

  @override
  String get ai_edit => '編輯';

  @override
  String get ai_delete => '刪除';

  @override
  String get ai_pick_out => '挑出';

  @override
  String get ai_replace => '替換';

  @override
  String get ai_rework => '重工';

  @override
  String get ai_edit_description => '編輯描述';

  @override
  String get ai_critical => '關鍵';

  @override
  String get ai_important => '重要';

  @override
  String get ai_minor => '次要';

  @override
  String get ai_defect_list => '缺陷列表';

  @override
  String get ai_test_level => '測試等級';

  @override
  String get ai_sampling_sample => '取樣樣本';

  @override
  String get ai_sampling_level => '取樣等級';

  @override
  String get ai_additional_information => '額外資訊';

  @override
  String get ai_inspection_record => '檢驗記錄';

  @override
  String get ai_sample_count => '樣本數量';

  @override
  String get ai_maximum_allowable_value => '允許的最大值';

  @override
  String get ai_test_item_name => '測試項目名稱';

  @override
  String get ai_test_result => '測試結果';

  @override
  String get ai_basic_information => '基本資訊';

  @override
  String get ai_new_test_item => '新的測試項目';

  @override
  String get ai_test_project => '測試專案';

  @override
  String get ai_measurement_project => '測量專案';

  @override
  String get ai_measure_need_num => '需要數量';

  @override
  String get ai_measurement_unit => '測量單位';

  @override
  String get ai_measurement_method => '測量方法';

  @override
  String get ai_measurement_record => '測量記錄';

  @override
  String get ai_measured => '已測量';

  @override
  String get ai_unit_of_measurement => '測量單位';

  @override
  String get ai_measured_value => '測量值';

  @override
  String get ai_product_number => '產品編號';

  @override
  String get ai_number => '編號';

  @override
  String get ai_new_measurement_item => '新的測量項目';

  @override
  String get ai_length_width_height => '長寬高';

  @override
  String get ai_dimensions_length => '長度';

  @override
  String get ai_dimensions_width => '寬度';

  @override
  String get ai_dimensions_height => '高度';

  @override
  String get ai_length_width => '長寬';

  @override
  String get ai_other => '其他';

  @override
  String get ai_allowable_error => '允許誤差';

  @override
  String get ai_report_summary => '報告摘要';

  @override
  String get ai_special_note => '特別說明';

  @override
  String get ai_overall_conclusion_2 => '總結論';

  @override
  String get ai_summary => '摘要';

  @override
  String get ai_category_name_table => '類別名稱表';

  @override
  String get ai_compliance => '符合性';

  @override
  String get ai_remarks_2 => '備註';

  @override
  String get ai_defect_summary => '缺陷摘要';

  @override
  String get ai_no_guidance_instructions => '目前無指導說明';

  @override
  String get ai_no_standard_instructions => '目前無標準說明';

  @override
  String get ai_please_fill_in => '請填寫';

  @override
  String ai_please_supplement_level_or_sample(Object level, Object sample) {
    return '請補充$level或$sample';
  }

  @override
  String get ai_please_add => '請新增';

  @override
  String get ai_please_input => '請輸入';

  @override
  String get ai_please_select => '請選擇';

  @override
  String ai_name_not_filled(Object name) {
    return '$name未填寫';
  }

  @override
  String get ai_addition_successful => '新增成功';

  @override
  String get ai_confirm_action => '確認';

  @override
  String get ai_cancel_action => '取消';

  @override
  String get ai_submit => '提交';

  @override
  String get ai_next_item => '下一項';

  @override
  String get ai_complete => '完成';

  @override
  String get ai_change_description => '更改描述';

  @override
  String get ai_action_guidance_instructions => '動作指導說明';

  @override
  String get ai_action_standard_instructions => '動作標準說明';

  @override
  String get ai_add_description => '新增描述';

  @override
  String get ai_change_description_note => '注意：下方列出的已經發現的缺陷，若修改此處，歷史資料也會採用新的描述！';

  @override
  String get ai_packing_completion_rate => '包裝完成率';

  @override
  String get ai_unprocessed_quantity => '未處理數量';

  @override
  String get ai_sample_level_type_0 => 'I級';

  @override
  String get ai_sample_level_type_1 => 'II級';

  @override
  String get ai_sample_level_type_2 => 'III級';

  @override
  String get ai_sample_level_type_s1 => 'S-1';

  @override
  String get ai_sample_level_type_s2 => 'S-2';

  @override
  String get ai_sample_level_type_s3 => 'S-3';

  @override
  String get ai_sample_level_type_s4 => 'S-4';

  @override
  String get ai_sample_level_type_nothing => '無';

  @override
  String get ai_inspection_image => '檢驗圖像';

  @override
  String get ai_photo_confirm => '確認照片';

  @override
  String get ai_add_product_ask_save => '是否要儲存此編輯？';

  @override
  String get ai_add_product_save => '儲存';

  @override
  String get ai_add_product_edit_model => '編輯型號';

  @override
  String get ai_add_product_model_name => '型號名稱';

  @override
  String get ai_add_product_input_model => '請輸入型號名稱';

  @override
  String get ai_add_product_num => '數量';

  @override
  String get ai_add_product_input_num => '請輸入型號數量';

  @override
  String get ai_add_product_unit => '單位';

  @override
  String get ai_add_product_ask_delete => '是否要刪除此型號？';

  @override
  String get ai_add_product_edit_product => '編輯產品';

  @override
  String get ai_add_product_product_name => '產品名稱';

  @override
  String get ai_add_product_model => '型號';

  @override
  String get ai_add_product_input_product_name => '請輸入產品名稱';

  @override
  String get ai_add_product_new_model => '新型號';

  @override
  String get ai_add_product_ask_product => '是否要刪除此產品及其所有型號？';

  @override
  String get ai_add_product_picture_lost => '圖片遺失';

  @override
  String ai_add_product_lost_des(Object lostStr) {
    return '目前$lostStr遺失，請補全所有資訊後再進行檢驗';
  }

  @override
  String get ai_add_product_model_full => '產品型號全名';

  @override
  String get ai_add_product_model_title => '產品型號';

  @override
  String get ai_add_product_new => '添加產品';

  @override
  String get ai_default_config_des => '當前產品暫無檢測模板，您可以選擇下方模板，或致電（+86）進行模板配置。';

  @override
  String get ai_default_config_category_all => '分類(全部)';

  @override
  String get ai_default_config_select_template => '請選擇模版';

  @override
  String get ai_default_config_template_selection => '模版選擇';

  @override
  String get ai_default_config_search_template => '搜尋模版';

  @override
  String get ai_default_config_classify => '分類';

  @override
  String get ai_default_config_preview => '預覽';

  @override
  String get ai_default_config_use => '應用';

  @override
  String get ai_default_config_current_use_button => '應用到本產品';

  @override
  String get ai_default_config_more_use_button => '應用到更多產品';

  @override
  String get ai_default_config_product_list => '產品列表';

  @override
  String get ai_default_config_tag_default => '運';

  @override
  String get ai_default_config_tag_manual => '模';

  @override
  String get ai_default_config_load_progress => '載入進度';

  @override
  String ai_default_config_template_progress(Object name) {
    return '模版載入完成 $name。';
  }

  @override
  String ai_default_config_template_fail_count(Object name) {
    return '失敗$name個，點擊重試';
  }

  @override
  String get ai_default_config_load => '載入';

  @override
  String get ai_default_config_success => '成功';

  @override
  String get ai_default_config_fail => '失敗';

  @override
  String get ai_default_config_product_edit => '編輯產品';

  @override
  String get ai_wait => '等待';

  @override
  String get ai_product_info => '產品信息';

  @override
  String get ai_product_category => '產品分類';

  @override
  String get ai_product_unit => '產品單位';

  @override
  String get ai_package_num_done => '已包裝箱數';

  @override
  String get ai_product_full => '補充信息';

  @override
  String get ai_product_full_tip => '訂單中的產品信息不完善，請根據驗貨現場中的實際產品信息進行補充。';

  @override
  String get ai_each_box => '每箱';

  @override
  String get ai_simple_count => '抽樣樣本數';

  @override
  String get ai_simple_level => '抽樣標準';

  @override
  String get ai_simple_num => '抽樣數量';

  @override
  String get ai_simple_no => '抽樣外箱編號';

  @override
  String get ai_simple_result => '結果判定';

  @override
  String get ai_simple_project => '檢查項目';

  @override
  String get ai_simple_project_manage => '管理檢查項目';

  @override
  String get ai_simple_project_edit => '編輯檢查項目';

  @override
  String get ai_simple_project_recmend => '智能推薦';

  @override
  String get ai_simple_project_input => '填寫檢查記錄';

  @override
  String get ai_simple_help => '幫助';

  @override
  String get ai_simple_project_record => '檢查記錄';

  @override
  String get ai_simple_require => '客戶要求';

  @override
  String get ai_simple_record => '記錄';

  @override
  String get ai_simple_dsec => '描述';

  @override
  String get ai_simple_before => '上一項';

  @override
  String get ai_simple_add => '添加一組';

  @override
  String get ai_simple_add_desc => '為照片添加描述信息';

  @override
  String get ai_simple_add_citations => '次引用';

  @override
  String get ai_no_more => '沒有更多數據了';

  @override
  String get ai_wrong_tip => '數量不能大於總數';

  @override
  String get ai_defect_records => '缺陷記錄';

  @override
  String get ai_check_require => '抽樣要求';

  @override
  String get ai_find_defect => '發現缺陷';

  @override
  String get ai_defect_question => '缺陷問題';

  @override
  String get ai_modify_level => '修改抽樣等級';

  @override
  String get ai_defect_quick => '快捷添加工藝缺陷';

  @override
  String get ai_defect_self => '自定義缺陷名稱';

  @override
  String get ai_defect_record_list => '瑕疵記錄列表';

  @override
  String get ai_measure_require => '測量要求';

  @override
  String get ai_measurement_item => '測量項';

  @override
  String get ai_measurement_error => '誤差';

  @override
  String get ai_measurement_standard => '測量標準';

  @override
  String get ai_measurement_value_standard => '標準值';

  @override
  String get ai_measurement_camera => '測量拍照';

  @override
  String get ai_measurement_add => '快捷添加測量標準';

  @override
  String get ai_product_first => '產品首圖';

  @override
  String get ai_product_report => '生成報告';

  @override
  String get ai_product_report_tip => '請選擇產品首圖';

  @override
  String get ai_product_report_special => '請輸入需要特別注意的內容';

  @override
  String get ai_product_report_sign => '簽名';

  @override
  String get ai_product_report_sign_done => '簽名完成';

  @override
  String get ai_defect_names => '缺陷名稱';

  @override
  String get ai_input_tip => '請輸入名稱';

  @override
  String get ai_add_measure_tip => '請先添加測量標準';

  @override
  String get ai_wrong_num => '數量錯誤';

  @override
  String get ai_wrong_name => '請輸入產品名稱';

  @override
  String get ai_wrong_sample_num => '不能大於抽樣樣本數';

  @override
  String get ai_per_box => '每箱數量';

  @override
  String get ai_wrong_sample_num_cal => '已包裝抽樣+未包裝抽樣需要等於抽樣樣本數';

  @override
  String get ai_sure_delete => '確定刪除嗎？';

  @override
  String get ai_choose_tip => '請選擇缺陷處理方式和個數';

  @override
  String get ai_weight => '毛重';

  @override
  String get sampling_plan => '抽樣計劃';

  @override
  String get single => '單個';

  @override
  String get normal => '普通';

  @override
  String get po_number => 'PO號';

  @override
  String get product_quantity => '產品數量';

  @override
  String get customer_name => '客戶名稱';

  @override
  String get supplier_name => '供應商名稱';

  @override
  String get inspection_date => '驗貨日期';

  @override
  String get arrival_time => '到達時間';

  @override
  String get completion_time => '完成時間';

  @override
  String get inspection_address => '驗貨地址';

  @override
  String get inspector => '驗貨員';

  @override
  String get inspection_report_note => '本驗貨報告只提供參考，是否確定通過等待客戶確定';
}
