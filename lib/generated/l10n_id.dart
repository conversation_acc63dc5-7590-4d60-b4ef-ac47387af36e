import 'package:intl/intl.dart' as intl;

import 'l10n.dart';

// ignore_for_file: type=lint

/// The translations for Indonesian (`id`).
class SId extends S {
  SId([String locale = 'id']) : super(locale);

  @override
  String get app_name => 'Inspeksi Online';

  @override
  String get search => 'Cari';

  @override
  String get shortcut_tab_name => 'Navigasi Cepat';

  @override
  String get loading => 'Memuat...';

  @override
  String get nomore => 'Tidak ada konten lagi';

  @override
  String get confirm => 'Konfirmasi';

  @override
  String get more_replies => 'Lebih banyak balasan';

  @override
  String get purchase_paid_publish_information_title => 'Inilah yang pelanggan bayar untuk melihatnya';

  @override
  String get purchase_set_fee => 'Tetapkan biaya menonton';

  @override
  String get purchase_comment_paid_supplier_hint => 'Silakan masukkan nama pemasok';

  @override
  String get purchase_comment_paid_contact_hint => 'Silakan masukkan nama kontak';

  @override
  String get purchase_comment_paid_phone_hint => 'Silakan masukkan nomor telepon kontak';

  @override
  String get purchase_comment_paid_email_hint => 'Silakan masukkan email kontak';

  @override
  String get purchase_comment_paid_address_hint => 'Silakan masukkan alamat pabrik';

  @override
  String get purchase_comment_paid_other_hint => 'Informasi lain (opsional)';

  @override
  String get purchase_comment_paid_low_price_hint => 'Silakan masukkan harga dasar produk (opsional)';

  @override
  String get purchase_reply_paid_title => 'Balasan Berbayar';

  @override
  String get purchase_reply_paid_desc => '(Informasi pemasok dan harga referensi produk)';

  @override
  String purchase_reply_paid_evaluated_people(Object people_num) {
    return '($people_num orang telah mengevaluasi)';
  }

  @override
  String get language_setting => 'Pengaturan Bahasa';

  @override
  String get public_and => 'dan';

  @override
  String get public_publish => 'Publikasi';

  @override
  String get public_distance => 'Jarak';

  @override
  String get public_deny => 'Tolak';

  @override
  String get public_see_more => 'Lihat lebih banyak';

  @override
  String get general_select => 'Pilih';

  @override
  String get language_page_title => 'Multi Bahasa';

  @override
  String get language_chinese => 'Bahasa Mandarin';

  @override
  String get language_english => 'Bahasa Inggris';

  @override
  String get public_seconds_ago => 'detik yang lalu';

  @override
  String get public_minutes_ago => 'menit yang lalu';

  @override
  String get public_hours_ago => 'jam yang lalu';

  @override
  String get public_status_applied => 'Telah Diajukan';

  @override
  String get public_status_refused => 'Ditolak';

  @override
  String get public_status_approved => 'Disetujui';

  @override
  String get public_status_canceled => 'Dibatalkan';

  @override
  String get public_app_name => 'Inspector';

  @override
  String get public_send => 'Kirim';

  @override
  String get public_ok => 'OK';

  @override
  String get public_price => 'Harga';

  @override
  String get public_cancel => 'Batal';

  @override
  String get public_manage => 'Kelola';

  @override
  String get public_finish => 'Selesai';

  @override
  String get public_reset => 'Reset';

  @override
  String get public_leave_message => 'Tinggalkan pesan';

  @override
  String get public_share => 'Bagikan';

  @override
  String get login_submit => 'Masuk';

  @override
  String get login_mobile_login => 'Login dengan Ponsel';

  @override
  String get login_mobile_tips => 'Silakan masukkan nomor ponsel';

  @override
  String get login_email_login => 'Login dengan Email';

  @override
  String get login_email_tips => 'Silakan masukkan email';

  @override
  String get registry_email_phone_tips => 'Silakan masukkan email atau nomor ponsel';

  @override
  String get login_verify_tips => 'Masukkan kode verifikasi';

  @override
  String get login_password_tips => 'Masukkan kata sandi';

  @override
  String get login_password_login => 'Login dengan Kata Sandi';

  @override
  String get login_verify_login => 'Login dengan Kode Verifikasi';

  @override
  String get login_register => 'Daftar';

  @override
  String get login_take_code => 'Dapatkan Kode Verifikasi';

  @override
  String get login_forget_password => 'Lupa Kata Sandi';

  @override
  String get login_agreement => 'Setuju dengan \'Perjanjian inspector.Itd\'';

  @override
  String get login_area_selected => 'Pilih Wilayah Negara';

  @override
  String get tab_home => 'Beranda';

  @override
  String get tab_order => 'Pesanan';

  @override
  String get tab_shortcut => 'Pintasan';

  @override
  String get tab_purchase => 'Pembelian';

  @override
  String get tab_message => 'Pesan';

  @override
  String get tab_mine => 'Saya';

  @override
  String get supplement_title => 'Silakan lengkapi informasi pribadi Anda terlebih dahulu';

  @override
  String get supplement_next => 'Lengkapi';

  @override
  String get home_title => 'Plaza Inspeksi';

  @override
  String get home_record => 'Catatan Aplikasi';

  @override
  String get home_newest => 'Inspeksi Terbaru';

  @override
  String get home_nearest => 'Inspeksi Terdekat';

  @override
  String home_recommend(Object money) {
    return 'Bonus Rekomendasi RMB $money';
  }

  @override
  String get home_sampling => 'Inspeksi Sampel';

  @override
  String get home_word => 'Laporan WORD';

  @override
  String home_unit(Object day, Object people) {
    return '$people orang/$day hari';
  }

  @override
  String get home_product_tip => 'Produk:';

  @override
  String get home_person_apply => 'orang mengajukan';

  @override
  String get home_know_tip => 'Panduan Inspeksi';

  @override
  String get home_inspection_tip => 'Biaya inspeksi default adalah harga yang disepakati, dapat diubah, biaya rendah mungkin diprioritaskan untuk penugasan';

  @override
  String get home_reviewed => 'Saya telah membaca dan akan mematuhi';

  @override
  String get home_apply => 'Ajukan';

  @override
  String get home_apply_price => 'Silakan masukkan jumlah ¥';

  @override
  String get home_apply_check => 'Silakan periksa panduan inspeksi';

  @override
  String get home_apply_tips => 'Anda bukan inspektor, jika Anda memiliki pengalaman kerja inspeksi perdagangan luar negeri lebih dari 1 tahun, silakan berikan bukti kualifikasi terkait';

  @override
  String get home_complete_profile_tips => 'Melengkapi profil inspektor dan informasi terkait lainnya dapat meningkatkan tingkat persetujuan aplikasi inspeksi';

  @override
  String get home_apply_sure => 'Kirim untuk Ditinjau';

  @override
  String get home_complete_profile_sure => 'Lengkapi';

  @override
  String get home_apply_cancel => 'Batalkan Aplikasi';

  @override
  String get home_update => 'Ubah';

  @override
  String get home_navi => 'Navigasi';

  @override
  String get mine_unauth => 'Belum Diverifikasi';

  @override
  String get mine_checking => 'Menunggu Peninjauan';

  @override
  String get mine_check_failed => 'Peninjauan Gagal';

  @override
  String get mine_vip_level => 'Level VIP';

  @override
  String get mine_credit_quota => 'Batas Kredit';

  @override
  String get mine_authed => 'Ubah Informasi Verifikasi';

  @override
  String get mine_authed_inspector => 'Ubah Informasi Inspektor';

  @override
  String get mine_amount => 'Jumlah Akun';

  @override
  String get mine_cash => 'Isi Ulang/Tarik';

  @override
  String get mine_order => 'Pesanan Saya';

  @override
  String get mine_purchase => 'Pembelian Saya';

  @override
  String get mine_check => 'Inspeksi Saya';

  @override
  String get mine_address => 'Buku Alamat (Pemasok)';

  @override
  String get mine_recommend => 'Rekomendasi';

  @override
  String get mine_setting => 'Pengaturan';

  @override
  String get mine_header_inspect => 'Manajemen Inspeksi';

  @override
  String get mine_header_purchase => 'Manajemen Pembelian';

  @override
  String get mine_header_other => 'Lainnya';

  @override
  String get mine_inspect_mine => 'Inspeksi Saya';

  @override
  String get mine_inspect_order => 'Manajemen Pesanan';

  @override
  String get mine_inspect_history => 'Riwayat Aplikasi';

  @override
  String get mine_purchase_mine => 'Pembelian Saya';

  @override
  String get mine_purchase_reply => 'Riwayat Balasan';

  @override
  String get mine_purchase_appeal => 'Manajemen Banding';

  @override
  String get mine_other_recommend => 'Rekomendasi';

  @override
  String get mine_other_address => 'Buku Alamat (Pemasok)';

  @override
  String get mine_other_settings => 'Pengaturan';

  @override
  String get profile_title => 'Informasi Pribadi';

  @override
  String get profile_avatar => 'Avatar';

  @override
  String get profile_name => 'Nama Panggilan';

  @override
  String get profile_mobile => 'Nomor Ponsel';

  @override
  String get profile_country => 'Negara';

  @override
  String get profile_real_name => 'Nama Asli';

  @override
  String get profile_city => 'Kota';

  @override
  String get profile_email => 'Email';

  @override
  String get profile_wechat => 'WeChat';

  @override
  String get profile_bind_manage => 'Manajemen Pengikatan Akun';

  @override
  String get profile_info_failed => 'Pembaruan informasi gagal';

  @override
  String get apply_title => 'Aplikasi Kualifikasi Inspektor';

  @override
  String get apply_nick => 'Nama Panggilan';

  @override
  String get apply_sex => 'Jenis Kelamin';

  @override
  String get apply_birthday => 'Tanggal Lahir';

  @override
  String get apply_education => 'Pendidikan';

  @override
  String get apply_address => 'Alamat Tempat Tinggal';

  @override
  String get apply_price => 'Biaya Inspeksi Minimum';

  @override
  String get apply_shebao => 'Asuransi Sosial';

  @override
  String get apply_id_card => 'Nomor KTP';

  @override
  String get apply_file => 'Edit Resume';

  @override
  String get apply_file_tip => 'Silakan masukkan informasi dasar dan pengalaman Anda';

  @override
  String get apply_upload_file => 'Unggah Resume';

  @override
  String get apply_upload_file_failed => 'Gagal mengunggah resume';

  @override
  String get apply_upload_card => 'Unggah Foto KTP';

  @override
  String get apply_card_front => 'Foto Depan (Sisi Wajah)';

  @override
  String get apply_card_back => 'Foto Belakang (Sisi Lambang Negara)';

  @override
  String get apply_submit => 'Kirim';

  @override
  String get apply_enter => 'Silakan masukkan';

  @override
  String get apply_next_tip => 'Silakan konfirmasi bahwa informasi telah lengkap sebelum mengirim';

  @override
  String get apply_auth_failed => 'Verifikasi KTP gagal, silakan unggah foto yang benar';

  @override
  String get apply_checking => 'Menunggu peninjauan identitas';

  @override
  String get apply_check_success => 'Peninjauan identitas berhasil';

  @override
  String get apply_check_failed => 'Peninjauan gagal, silakan ubah konten dan kirim ulang';

  @override
  String get order_title => 'Pesanan Saya';

  @override
  String get order_input => 'Inspeksi Saya';

  @override
  String get order_output => 'Publikasikan Pesanan';

  @override
  String get order_all => 'Semua';

  @override
  String get order_wait_pay => 'Menunggu Pembayaran';

  @override
  String get order_cancelled => 'Dibatalkan';

  @override
  String get order_status => 'Status Pesanan';

  @override
  String get order_status_unknown => 'Tidak Diketahui';

  @override
  String get order_refund_pending => 'Pengembalian Dana Menunggu Peninjauan';

  @override
  String get order_cancelled_refund_pending => 'Dibatalkan, Pengembalian Dana Menunggu Peninjauan';

  @override
  String get order_refund_partial => 'Pengembalian Dana Sebagian';

  @override
  String get order_refund_denied => 'Pengembalian Dana Ditolak';

  @override
  String get order_wait_dispatch => 'Menunggu Penugasan';

  @override
  String get order_ready_inspect => 'Siap untuk Inspeksi';

  @override
  String get order_need_pay => 'Bayar';

  @override
  String get order_wait => 'Siap untuk Inspeksi';

  @override
  String get order_confirm => 'Konfirmasi Penugasan';

  @override
  String get order_doing => 'Dalam Inspeksi';

  @override
  String get order_comment => 'Menunggu Evaluasi';

  @override
  String get order_finished => 'Selesai';

  @override
  String get order_goods_info => 'Informasi Produk';

  @override
  String get order_goods_name => 'Nama Produk';

  @override
  String get order_goods_model => 'Model Produk';

  @override
  String get order_goods_count => 'Jumlah';

  @override
  String get order_goods_unit => 'Unit';

  @override
  String get order_order_time => 'Waktu Pesanan';

  @override
  String get order_order_amount => 'Jumlah Pesanan';

  @override
  String get order_detail_title => 'Detail Pesanan';

  @override
  String get order_applying => 'Telah Diajukan';

  @override
  String get order_apply_expired => 'Kedaluwarsa';

  @override
  String get order_apply_dispatched => 'Telah Ditugaskan';

  @override
  String get order_create_time => 'Waktu Pemesanan';

  @override
  String get order_look => 'Lihat Laporan Inspeksi';

  @override
  String get order_report_next => 'Kirim Laporan Inspeksi';

  @override
  String get order_detail_inspection_info => 'Informasi Inspeksi';

  @override
  String get order_inspection_status_unpaid => 'Belum Dibayar';

  @override
  String get order_inspection_status_returned => 'Dikembalikan';

  @override
  String get order_inspection_status_waiting_start => 'Menunggu Dimulai';

  @override
  String get order_detail_related_info => 'Informasi Sub-pesanan Terkait';

  @override
  String get order_detail_inspection_product => 'Nama Produk';

  @override
  String get order_detail_inspection_time => 'Waktu Inspeksi';

  @override
  String get order_detail_inspection_city => 'Kota Inspeksi';

  @override
  String get order_detail_inspection_factory => 'Pabrik Inspeksi';

  @override
  String get order_detail_inspection_address => 'Alamat Inspeksi';

  @override
  String get order_detail_inspection_person => 'Kontak';

  @override
  String get order_detail_inspection_phone => 'Nomor Telepon Kontak';

  @override
  String get order_detail_inspection_email => 'Email Kontak';

  @override
  String get order_detail_inspection_amount => 'Informasi Harga';

  @override
  String get order_detail_inspection_sample => 'Sampel';

  @override
  String get order_detail_inspection_standard => 'Tingkat Sampling';

  @override
  String get order_detail_inspection_critical => 'AQL';

  @override
  String get order_detail_inspection_major => 'Major';

  @override
  String get order_detail_inspection_minor => 'Minor';

  @override
  String get order_detail_inspection_day => 'Jumlah Hari Inspeksi';

  @override
  String get order_detail_inspection_number => 'Jumlah Inspektor';

  @override
  String get order_detail_inspection_price => 'Harga Inspeksi';

  @override
  String get order_detail_inspection_price_apply => 'Harga Aplikasi';

  @override
  String get order_detail_inspection_template => 'Template Inspeksi';

  @override
  String get order_detail_inspection_file => 'Lampiran';

  @override
  String get order_detail_inspection_image => 'Gambar';

  @override
  String get order_detail_inspection_type => 'Jenis Inspeksi';

  @override
  String get order_tips => 'Sampel dan Catatan';

  @override
  String get order_apply => 'Ajukan Inspeksi';

  @override
  String get order_cancel => 'Batal';

  @override
  String get order_canceled => 'Telah Dibatalkan';

  @override
  String get order_dispatch_accept => 'Setuju';

  @override
  String get order_dispatch_refuse => 'Tolak';

  @override
  String get order_inspection => 'Mulai Inspeksi';

  @override
  String get order_sure_cancel => 'Yakin membatalkan pesanan';

  @override
  String get order_sure_refuse => 'Yakin menolak pesanan ini?';

  @override
  String get order_sure_confirm => 'Yakin menerima pesanan ini?';

  @override
  String get publish_title => 'Publikasikan Pesanan';

  @override
  String get publish_edit_title => 'Edit Pesanan';

  @override
  String get publish_welcome => 'Selamat datang untuk memesan, harga bebas, komunikasi bebas dengan inspektor';

  @override
  String get publish_sampling => 'Inspeksi Sampel';

  @override
  String get publish_sampling_content => 'Berlaku untuk produk yang 100% selesai, setidaknya 80% produk telah dikemas, siap untuk pengiriman. Kami melakukan inspeksi sampel acak berdasarkan rencana sampling internasional ANSI/ASQZ1.4(MIL-STD-105E) serta mengacu pada persyaratan khusus Anda. Dalam laporan inspeksi sampel, kami akan sepenuhnya mencerminkan jumlah produk yang selesai, kondisi kemasan, dan apakah memenuhi persyaratan AQL (Acceptable Quality Level), memberi Anda pemahaman menyeluruh tentang kualitas seluruh batch produk sebelum pengiriman, menghindari risiko apa pun pada pesanan Anda';

  @override
  String get publish_point => 'Poin Inspeksi';

  @override
  String get publish_sampling_point => '● Verifikasi data/sampel pelanggan\n● Verifikasi jumlah yang selesai\n● Verifikasi ukuran, gaya, warna produk\n● Pemeriksaan tampilan dan pengerjaan\n● Pengujian fungsi dan keamanan produk\n● Pemeriksaan label pengiriman\n● Integritas kemasan\n● Detail kemasan spesifik\n● Persyaratan khusus pelanggan';

  @override
  String get publish_all => 'Inspeksi Penuh';

  @override
  String get publish_all_content => 'Inspeksi penuh dapat dilakukan sebelum atau sesudah pengemasan. Sesuai permintaan pelanggan, setiap produk diperiksa untuk tampilan, ukuran, pengerjaan, fungsi dan keamanan, memisahkan produk yang baik dan cacat, dan melaporkan hasil inspeksi kepada pelanggan secara tepat waktu';

  @override
  String get publish_online => 'Inspeksi Online';

  @override
  String get publish_online_content => 'Inspeksi online dilakukan selama proses produksi atau sebelum semua produksi selesai dan dikemas. Ini dapat membantu Anda memastikan kualitas, fungsi, tampilan, dan elemen lain dari produk tetap konsisten dengan spesifikasi Anda selama seluruh proses produksi, juga membantu menemukan ketidaksesuaian lebih awal, sehingga mengurangi risiko keterlambatan pengiriman dari pabrik';

  @override
  String get publish_online_point => '● Tindak lanjut situasi produksi\n● Evaluasi lini produksi dan konfirmasi kemajuan produksi\n● Inspeksi sampel produk setengah jadi dan jadi\n● Periksa informasi kemasan dan bahan kemasan\n● Perbaikan produk cacat\n● Evaluasi waktu pengiriman';

  @override
  String get publish_factory => 'Audit Pabrik';

  @override
  String get publish_factory_content => 'Audit pabrik terutama menggunakan metode penilaian objektif, mengevaluasi dan mengaudit pabrik secara kuantitatif berdasarkan standar atau kriteria yang telah ditetapkan sebelumnya. Laporan evaluasi dibuat berdasarkan penilaian di tempat dan hasil audit komprehensif pabrik, sebagai dasar bagi pelanggan untuk menentukan apakah pabrik tersebut dapat menjadi pemasok yang memenuhi syarat';

  @override
  String get publish_factory_point_title => 'Konten Audit';

  @override
  String get publish_factory_point => '● Gambaran umum pabrik (informasi dasar)\n● Struktur organisasi\n● Proses produksi\n● Kapasitas produksi\n● Kemampuan penelitian dan pengembangan\n● Peralatan mesin dan fasilitas';

  @override
  String get publish_factory_review => 'Evaluasi Komprehensif';

  @override
  String get publish_factory_review_content => '● Untuk setiap item audit, pertimbangkan kepentingan relatifnya, berikan skor yang berbeda, kemudian buat tabel penilaian kualifikasi berdasarkan formulir survei audit dan data survei lapangan';

  @override
  String get publish_watch => 'Pengawasan Pemuatan';

  @override
  String get publish_watch_content => 'Pengawasan pemuatan kontainer terutama mencakup evaluasi kondisi kontainer, verifikasi informasi produk, penghitungan jumlah kotak yang dimuat, pemeriksaan informasi kemasan, dan pengawasan seluruh proses pemuatan. Untuk mengurangi risiko tinggi penggantian barang setelah pemuatan, inspektor mengawasi di lokasi pemuatan untuk memastikan produk yang Anda bayar dimuat dengan aman';

  @override
  String get publish_watch_point => '● Catat nomor kontainer dan nomor truk\n● Periksa kontainer untuk kerusakan, kelembaban, dan bau aneh, ambil foto kontainer kosong\n● Periksa jumlah kotak yang akan dimuat dan kondisi kemasan luar, periksa secara acak beberapa kotak untuk memastikan produk yang sebenarnya dimuat\n● Awasi proses pemuatan untuk memastikan kerusakan minimal dan pemanfaatan ruang maksimal\n● Ambil foto kondisi penutupan kontainer, nomor segel kontainer, dan daftar kemasan, catat waktu keberangkatan kontainer';

  @override
  String get publish_watch_inspection => 'Inspeksi + Pengawasan Pemuatan';

  @override
  String get publish_watch_inspection_content => 'Untuk memastikan kualitas akhir dan kelengkapan produk, sebelum siap untuk pengiriman, kami mengambil sampel secara acak dari produk jadi untuk inspeksi sampel berdasarkan rencana sampling internasional, dan memverifikasi data yang diberikan pelanggan, serta mengawasi seluruh proses pemuatan kontainer';

  @override
  String get publish_watch_inspection_point => '● Sebelum kontainer tiba, verifikasi data/sampel pelanggan, lakukan inspeksi sampel pada tampilan, pengerjaan, fungsi dan keamanan produk, serta kemasan, label pengiriman, dll.\n● Segera komunikasikan dengan pabrik jika ditemukan produk cacat, untuk penggantian atau pengerjaan ulang\n● Periksa kontainer untuk kerusakan, kelembaban, dan bau aneh, ambil foto kontainer kosong\n● Awasi proses pemuatan untuk memastikan kerusakan minimal dan pemanfaatan ruang maksimal\n● Ambil foto kondisi penutupan kontainer, nomor segel kontainer, dan daftar kemasan, catat waktu keberangkatan kontainer';

  @override
  String get publish_next => 'Selanjutnya';

  @override
  String get publish_inspection_time => 'Waktu Inspeksi';

  @override
  String get publish_inspection_time_selected => 'Pilih Waktu Inspeksi';

  @override
  String get publish_inspection_time_tip => 'Silakan pilih';

  @override
  String get publish_inspection_people => 'Jumlah Inspektor';

  @override
  String get publish_people => 'orang';

  @override
  String get publish_day => 'hari';

  @override
  String get publish_inspection_factory => 'Pabrik Inspeksi';

  @override
  String get publish_factory_tips => 'Masukkan pabrik inspeksi';

  @override
  String get publish_address_book => 'Buku Alamat';

  @override
  String get publish_goods_name => 'Nama Produk';

  @override
  String get publish_name_tips => 'Masukkan satu atau dua nama representatif jika ada beberapa produk';

  @override
  String get publish_po_tips => 'Silakan masukkan nomor P.O';

  @override
  String get publish_file_tips => 'Unggah lampiran';

  @override
  String get publish_camera => 'Unggah foto';

  @override
  String get publish_file => 'Unggah file';

  @override
  String get publish_purchase => 'Publikasikan Pesanan Pembelian';

  @override
  String get publish_inspection => 'Publikasikan Pesanan Inspeksi';

  @override
  String get publish_factory_tip => 'Silakan pilih informasi alamat inspeksi terlebih dahulu';

  @override
  String get publish_attention => 'Catatan';

  @override
  String get publish_attention_tips => 'Silakan masukkan masalah apa yang perlu diperhatikan oleh inspektor';

  @override
  String get publish_stand_price => 'Harga Tetap';

  @override
  String get publish_click_price => 'Ubah';

  @override
  String get publish_vip_price => 'Harga VIP';

  @override
  String get publish_vip_tips => 'Menyediakan layanan pemantauan manual selama proses';

  @override
  String get publish_total => 'Total';

  @override
  String get publish_submit => 'Kirim';

  @override
  String get publish_only_price_failed => 'Tidak ada izin harga tetap';

  @override
  String get publish_price_tip => 'Silakan pilih harga';

  @override
  String get publish_date_tips => 'Silakan pilih tanggal';

  @override
  String get date_title => 'Tanggal Inspeksi';

  @override
  String get date_save => 'Simpan';

  @override
  String get address_title => 'Edit Informasi Pabrik';

  @override
  String get address_auto_tips => 'Silakan tempel atau masukkan teks, klik \"Identifikasi\" untuk secara otomatis mengidentifikasi nama pabrik, nama dan nomor telepon, alamat, dll';

  @override
  String get address_paste => 'Tempel';

  @override
  String get address_ocr => 'Identifikasi';

  @override
  String get address_name => 'Nama Pabrik';

  @override
  String get address_name_tip => 'Silakan masukkan informasi pabrik inspeksi';

  @override
  String get address_person => 'Kontak';

  @override
  String get address_person_tip => 'Silakan masukkan kontak';

  @override
  String get address_mobile => 'Nomor Ponsel';

  @override
  String get address_mobile_tip => 'Silakan masukkan nomor ponsel';

  @override
  String get address_email => 'Email';

  @override
  String get address_email_tip => 'Silakan masukkan alamat email';

  @override
  String get address_area => 'Provinsi-Kota-Kabupaten';

  @override
  String get address_area_tip => 'Silakan pilih Provinsi-Kota-Kabupaten  〉';

  @override
  String get address_detail => 'Alamat Rinci';

  @override
  String get address_detail_tip => 'Masukkan informasi jalan, nomor, dll';

  @override
  String get address_location => 'Lokasi';

  @override
  String get address_save_tip => 'Simpan ke Buku Alamat';

  @override
  String get address_clear => 'Hapus';

  @override
  String get address_submit => 'Kirim';

  @override
  String get address_recent => 'Alamat yang Baru Digunakan';

  @override
  String get address_more => 'Lebih Banyak Alamat';

  @override
  String get address_list_title => 'Manajemen Alamat';

  @override
  String get address_insert => 'Tambah Alamat';

  @override
  String get address_delete => 'Hapus';

  @override
  String get address_delete_result => 'Penghapusan Gagal';

  @override
  String get address_edit => 'Edit';

  @override
  String get address_delete_tips => 'Konfirmasi hapus alamat?';

  @override
  String get address_detected_paste => 'Informasi alamat terdeteksi, gunakan alamat ini?';

  @override
  String get pay_title => 'Bayar Pesanan';

  @override
  String get pay_time => 'Waktu Tersisa untuk Pembayaran';

  @override
  String get pay_paypal => 'PayPal';

  @override
  String get pay_zfb => 'Alipay';

  @override
  String get pay_usd => 'Akun USD';

  @override
  String get pay_rmb => 'Akun RMB';

  @override
  String get pay_pay => 'Bayar';

  @override
  String get pay_result_success => 'Pembayaran Berhasil';

  @override
  String get pay_result_success_wait => 'Pembayaran berhasil, menunggu pemrosesan pesanan.';

  @override
  String get pay_result_failed => 'Pembayaran Gagal';

  @override
  String get pay_keep => 'Dapat dicatat';

  @override
  String get check_title => 'Laporan Inspeksi';

  @override
  String get check_picture => 'Foto Inspeksi';

  @override
  String get check_file => 'Surat Komitmen Integritas';

  @override
  String get check_report => 'Surat Komitmen Integritas + Laporan Tulisan Tangan';

  @override
  String get check_draft => 'Laporan Draft';

  @override
  String get check_template => 'Template Laporan';

  @override
  String get check_submit => 'Kirim';

  @override
  String get check_hint => 'Silakan masukkan deskripsi gambar';

  @override
  String get check_checking => 'Laporan sedang ditinjau';

  @override
  String get check_check_success => 'Peninjauan laporan berhasil';

  @override
  String get check_check_failed => 'Peninjauan laporan gagal, silakan ubah konten dan kirim ulang';

  @override
  String get review_title => 'Evaluasi Layanan';

  @override
  String get review_next => 'Evaluasi';

  @override
  String get contact_bnt => 'Hubungi Sekarang';

  @override
  String get review_score => 'Tingkat Layanan';

  @override
  String get review_score1 => 'Kecewa';

  @override
  String get review_score2 => 'Tidak Puas';

  @override
  String get review_score3 => 'Biasa';

  @override
  String get review_score4 => 'Puas';

  @override
  String get review_score5 => 'Luar Biasa';

  @override
  String get review_tips => 'Evaluasi dari berbagai sudut untuk membantu kami lebih memahami kemampuan kerja inspektor';

  @override
  String get review_picture => 'Gambar';

  @override
  String get review_submit => 'Kirim';

  @override
  String get setting_title => 'Pengaturan';

  @override
  String get setting_address => 'Manajemen Alamat';

  @override
  String get setting_clear_cache => 'Bersihkan Cache';

  @override
  String get setting_clear_success => 'Pembersihan Berhasil';

  @override
  String get setting_about_us => 'Tentang Kami';

  @override
  String get setting_receive_msg => 'Terima Pemberitahuan Pesan';

  @override
  String get setting_version => 'Nomor Versi';

  @override
  String get setting_check_update => 'Periksa Pembaruan';

  @override
  String get setting_login_out => 'Keluar';

  @override
  String get setting_login_out_tips => 'Yakin ingin keluar?';

  @override
  String get setting_delete_account_tips => 'Yakin ingin menghapus akun?';

  @override
  String get setting_policy_title => 'Pemberitahuan Otorisasi Kebijakan Privasi';

  @override
  String get setting_policy_sub_title => 'Sebelum melanjutkan, harap baca dan setujui';

  @override
  String get setting_privacy_policy => 'Kebijakan Privasi';

  @override
  String get setting_user_agreement => 'Perjanjian Pengguna';

  @override
  String get setting_privacy_content => 'Selamat datang di Aplikasi Inspeksi Online\nKami sangat menghargai privasi dan perlindungan informasi pribadi Anda. Selama Anda menggunakan Aplikasi ini, kami akan mengumpulkan dan menggunakan sebagian informasi pribadi Anda\n1. Setelah Anda menyetujui kebijakan privasi Aplikasi, kami akan melakukan inisialisasi SDK terintegrasi, yang akan mengumpulkan alamat MAC perangkat Anda, IMSI, Android ID, alamat IP, model perangkat keras, nomor versi sistem operasi, pengidentifikasi perangkat unik (seperti IMEI), alamat perangkat keras jaringan (MAC), nomor versi perangkat lunak, metode akses jaringan, jenis, status, data kualitas jaringan, log operasi, nomor seri perangkat keras, informasi log layanan, dll. untuk memastikan statistik data normal dan kontrol keamanan Aplikasi.\n2. Tanpa persetujuan Anda, kami tidak akan memperoleh, berbagi, atau memberikan informasi Anda kepada pihak ketiga.\n3. Anda dapat mengakses, memperbaiki, menghapus informasi pribadi Anda, dan kami juga akan menyediakan cara untuk membatalkan dan mengajukan keluhan.';

  @override
  String get setting_ihavereadandagreed => 'Saya telah membaca dan menyetujui';

  @override
  String get setting_policy_tips2 => 'Harap baca dan pahami dengan seksama';

  @override
  String get wallet_title => 'Dompet';

  @override
  String get wallet_bill => 'Tagihan';

  @override
  String get wallet_rmb_account => 'Akun RMB';

  @override
  String get wallet_usd_account => 'Akun USD';

  @override
  String get wallet_account_heading => 'Akun Penarikan dan Pengaturan';

  @override
  String get wallet_bank => 'Kartu Bank';

  @override
  String get wallet_wechat => 'WeChat';

  @override
  String get wallet_alipay => 'Alipay';

  @override
  String get wallet_charge => 'Isi Ulang';

  @override
  String get wallet_cash => 'Tarik';

  @override
  String get wallet_balance => 'Saldo';

  @override
  String get wallet_default_account => 'Akun Default';

  @override
  String get wallet_set_default_account => 'Atur sebagai Akun Default';

  @override
  String get bill_title => 'Tagihan';

  @override
  String get bill_out => 'Pengeluaran';

  @override
  String get bill_in => 'Pemasukan';

  @override
  String get bill_month => 'Bulan';

  @override
  String get bill_fenxi => 'Analisis Pemasukan dan Pengeluaran';

  @override
  String get bill_unfreeze => 'Pencairan';

  @override
  String get bill_all => 'Semua Tagihan';

  @override
  String get bill_income => 'Pemasukan';

  @override
  String get bill_outcome => 'Pengeluaran';

  @override
  String get bill_freeze => 'Pembekuan';

  @override
  String get bill_withdraw => 'Penarikan';

  @override
  String get bank_title => 'Kartu Bank Saya';

  @override
  String get bank_add => 'Tambah Kartu Bank';

  @override
  String get add_bank_title => 'Tambah Kartu Bank';

  @override
  String get add_bank_name => 'Nama Kartu Bank';

  @override
  String get add_bank_branch => 'Cabang Bank';

  @override
  String get add_bank_card => 'Nomor Kartu';

  @override
  String get add_bank_real_name => 'Nama';

  @override
  String get add_bank_address => 'Alamat Pembukaan Rekening';

  @override
  String bind_title(Object bind) {
    return 'Ikat $bind';
  }

  @override
  String get bind_account => 'Akun';

  @override
  String get bind_image => 'Kode Pembayaran';

  @override
  String get bind_name => 'Nama';

  @override
  String get bind_hint => 'Silakan masukkan';

  @override
  String get charge_title => 'Isi Ulang';

  @override
  String get charge_account => 'Akun Isi Ulang';

  @override
  String get charge_money => 'Jumlah Isi Ulang';

  @override
  String get charge_deposit_type_title => 'Metode Isi Ulang';

  @override
  String get charge_deposit_type_online => 'Isi Ulang Online';

  @override
  String get charge_deposit_type_offline => 'Transfer Offline';

  @override
  String get charge_offline_nopic_hint => 'Silakan unggah bukti isi ulang';

  @override
  String get charge_upload_proof => 'Silakan unggah bukti transfer';

  @override
  String get withdraw_list_title => 'Riwayat Penarikan';

  @override
  String get withdraw_rmb => 'Penarikan RMB';

  @override
  String get withdraw_usd => 'Penarikan USD';

  @override
  String get withdraw_status_checking => 'Sedang Ditinjau';

  @override
  String get withdraw_status_approved => 'Peninjauan Disetujui';

  @override
  String get withdraw_status_denied => 'Peninjauan Ditolak';

  @override
  String get withdraw_cash_status_unfinished => 'Belum Ditransfer';

  @override
  String get withdraw_cash_status_done => 'Telah Ditransfer';

  @override
  String get withdraw_cash_status_refused => 'Ditolak';

  @override
  String get charge_hint => 'Silakan masukkan jumlah isi ulang';

  @override
  String get charge_submit => 'Konfirmasi';

  @override
  String get charge_rmb => 'Isi Ulang RMB';

  @override
  String get charge_usd => 'Isi Ulang USD';

  @override
  String get charge_history_title => 'Riwayat Isi Ulang';

  @override
  String get cash_title => 'Penarikan';

  @override
  String get cash_account => 'Pilih Akun Penarikan';

  @override
  String get cash_money => 'Jumlah Penarikan';

  @override
  String get cash_invoice_money => 'Jumlah Faktur';

  @override
  String get cash_invoice_money_hint => 'Silakan masukkan jumlah faktur';

  @override
  String get cash_invoice_upload => 'Unggah Faktur';

  @override
  String get cash_account_list_title => 'Setelah aplikasi disetujui, dana penarikan akan ditransfer ke salah satu akun berikut secara acak:';

  @override
  String get cash_hint => 'Silakan masukkan jumlah penarikan';

  @override
  String get cash_withdraw_tips1 => 'Anda sedang menarik ke';

  @override
  String get cash_withdraw_tips2 => ', jumlah penarikan adalah';

  @override
  String get cash_amount => 'Jumlah yang Diterima';

  @override
  String get cash_other => 'Biaya Layanan';

  @override
  String get cash_submit => 'Konfirmasi';

  @override
  String get location_permission => 'Perlu menggunakan izin lokasi, silakan aktifkan';

  @override
  String get location_cancel => 'Batal';

  @override
  String get location_author => 'Beri Izin';

  @override
  String get group_title => 'Anggota Grup';

  @override
  String get unknown_error => 'Kesalahan Tidak Diketahui';

  @override
  String get data_parsing_exception => 'Pengecualian Parsing Data';

  @override
  String get edit => 'Edit';

  @override
  String get no_data => 'Tidak Ada Data';

  @override
  String get note => 'Catatan Penjelasan';

  @override
  String get msg_locating => 'Mencari Lokasi';

  @override
  String get failed_to_download => 'Gagal mengunduh pembaruan';

  @override
  String get pick_address => 'Klik untuk memasukkan alamat pabrik';

  @override
  String get update_now => 'Perbarui Sekarang';

  @override
  String get message => 'Pesan';

  @override
  String get view_order => 'Lihat Pesanan';

  @override
  String get today => 'Hari Ini';

  @override
  String get yesterday => 'Kemarin';

  @override
  String get send_file => 'Kirim File';

  @override
  String get login_expired => 'Login telah kedaluwarsa, silakan login kembali';

  @override
  String get exit_group_chat_confirm => 'Yakin ingin keluar dari obrolan grup?';

  @override
  String get exit_group_chat_success => 'Telah keluar dari obrolan grup';

  @override
  String get exit_group_chat_page_title => 'Informasi Obrolan';

  @override
  String get exit_group_chat_button_title => 'Keluar dari Obrolan Grup';

  @override
  String get group_chat_setting_view_more => 'Lihat Lebih Banyak Anggota Grup';

  @override
  String get group_chat_setting_name => 'Nama Obrolan Grup';

  @override
  String get group_chat_setting_owner_update => 'Hanya pemilik grup yang diizinkan mengubah nama grup';

  @override
  String get group_chat_name_page_title => 'Ubah Nama Obrolan Grup';

  @override
  String get group_chat_name_page_required => 'Silakan masukkan nama obrolan grup';

  @override
  String get group_chat_name_save => 'Simpan';

  @override
  String get group_chat_name_saved => 'Nama obrolan grup telah diubah';

  @override
  String get conversation_manage_view_please => 'Silakan pilih percakapan yang ingin dioperasikan';

  @override
  String get conversation_manage_view_list => 'Daftar Percakapan';

  @override
  String get group_manage_select => 'Silakan pilih grup yang ingin dioperasikan';

  @override
  String get group_manage_list => 'Daftar Grup';

  @override
  String get please_enter => 'Silakan masukkan';

  @override
  String get address_keyword => 'Silakan masukkan kata kunci alamat';

  @override
  String get inspector_min_fee => 'Silakan masukkan biaya inspeksi minimum';

  @override
  String get inspector_id_card_required => 'Silakan masukkan nomor KTP';

  @override
  String get inspector_id_card_upload => 'Silakan unggah foto KTP';

  @override
  String get inspector_id_card_upload_fail => 'Gagal mengunggah foto KTP, silakan unggah ulang';

  @override
  String get inspector_revoke => 'Yakin ingin mencabut kualifikasi inspektor?';

  @override
  String get inspector_revoke_completed => 'Kualifikasi inspektor telah dicabut';

  @override
  String get male => 'Laki-laki';

  @override
  String get female => 'Perempuan';

  @override
  String get elementary => 'SD';

  @override
  String get junior => 'SMP';

  @override
  String get technical => 'SMK';

  @override
  String get senior => 'SMA';

  @override
  String get college => 'Diploma';

  @override
  String get bachelor => 'S1';

  @override
  String get master => 'S2';

  @override
  String get doctor => 'S3';

  @override
  String get yes => 'Ya';

  @override
  String get no => 'Tidak';

  @override
  String get upload_image => 'Unggah Gambar';

  @override
  String get upload_file => 'Unggah File';

  @override
  String get revoke_inspector => 'Cabut Kualifikasi Inspektor';

  @override
  String get deposit_card => 'Kartu Tabungan';

  @override
  String get withdrawal_balance => 'Jumlah penarikan tidak boleh melebihi saldo akun';

  @override
  String get failed_get_payment_info => 'Gagal mendapatkan informasi pembayaran';

  @override
  String get recommended_order => 'Pesanan Rekomendasi';

  @override
  String get withdrawal_method => 'Harap sediakan setidaknya satu metode penarikan';

  @override
  String get withdrawal_bind_alipay => 'Silakan ikat Alipay terlebih dahulu';

  @override
  String get enabled_camera => 'Silakan atur untuk mengizinkan penggunaan kamera untuk mengambil foto';

  @override
  String get valid_email_mobile => 'Silakan masukkan alamat email atau nomor ponsel yang valid';

  @override
  String get apple_map => 'Peta Apple';

  @override
  String get baidu_map => 'Peta Baidu';

  @override
  String get amap => 'Peta Amap';

  @override
  String get google_map => 'Peta Google';

  @override
  String get tencent_map => 'Peta Tencent';

  @override
  String get image_format => 'Format gambar harus png, jpg, jpeg';

  @override
  String get enable_location_service => 'Perlu mengaktifkan izin lokasi';

  @override
  String get enable_location_service_tips => 'Aktifkan izin lokasi untuk mencari pesanan inspeksi di sekitar dengan tepat';

  @override
  String get enable_permission_not_now => 'Nanti saja';

  @override
  String get enable_permission_goto_setting => 'Buka Pengaturan';

  @override
  String get failed_location_service => 'Gagal mendapatkan informasi lokasi';

  @override
  String get turn_on_location_service => 'Silakan aktifkan layanan lokasi ponsel';

  @override
  String get no_install_map => 'Anda belum menginstal peta';

  @override
  String get camera => 'Kamera';

  @override
  String get photo_album => 'Album Foto';

  @override
  String get new_version => 'Versi baru telah diluncurkan';

  @override
  String get invalid_mail => 'Email pengguna tidak ada';

  @override
  String get invalid_password => 'Kata sandi salah';

  @override
  String get invalid_mobile => 'Nomor ponsel tidak ada';

  @override
  String get invalid_auth_code => 'Kode verifikasi salah';

  @override
  String get invalid_login => 'Login gagal, silakan coba lagi';

  @override
  String get grabbing => 'Sedang Mengambil Pesanan';

  @override
  String get hour_ago => 'jam yang lalu dipublikasikan';

  @override
  String get minute_ago => 'menit yang lalu dipublikasikan';

  @override
  String get report_type => 'Jenis Laporan';

  @override
  String get fri => 'Inspeksi Sampel';

  @override
  String get fui => '';

  @override
  String get oli => '';

  @override
  String get fat => '';

  @override
  String get cls => 'Pengawasan Kontainer';

  @override
  String get fri_cls => '';

  @override
  String get order_payment => 'Pembayaran Pesanan';

  @override
  String get order_refund => 'Pengembalian Dana Pesanan';

  @override
  String get expend_withdrawal => 'Pengeluaran - Penarikan';

  @override
  String get incoming_refund => 'Pemasukan - Pengembalian Dana Pesanan';

  @override
  String get incoming_recharge => 'Pemasukan - Isi Ulang';

  @override
  String get chat_not_member => 'Anda bukan lagi anggota grup, tidak dapat mengirim pesan';

  @override
  String get admins => 'Hubungi Layanan Pelanggan';

  @override
  String get theme_title => 'Tema';

  @override
  String get theme_light => 'Tema Terang';

  @override
  String get theme_dark => 'Tema Gelap';

  @override
  String get theme_auto => 'Ikuti Sistem';

  @override
  String get amount_total => 'Total';

  @override
  String get amount_available => 'Tersedia';

  @override
  String get amount_blocked => 'Dibekukan';

  @override
  String get download => 'Klik untuk Unduh';

  @override
  String get downloading => 'Sedang Mengunduh';

  @override
  String get saved => 'Tersimpan';

  @override
  String get order_number => 'Nomor Pesanan';

  @override
  String get order_detail_inspection_cost => 'Biaya Inspeksi';

  @override
  String get delete_account => 'Hapus Akun';

  @override
  String get delete_account_confirm => 'Semua informasi tidak akan disimpan.\nYakin ingin menghapus?';

  @override
  String get delete_account_result => 'Akun telah dihapus';

  @override
  String get not_exist_account => 'Akun tidak ada';

  @override
  String get new_password => 'Masukkan kata sandi baru';

  @override
  String get supervisor => 'Pengawas';

  @override
  String get downloadFiles => 'File yang Diunduh';

  @override
  String get home_search_hint_inspector => 'Cari pesanan berdasarkan kota/nama produk';

  @override
  String get home_search_hint_admin => 'Cari pesanan berdasarkan kota/nama produk';

  @override
  String get search_recent_history => 'Pencarian Terbaru';

  @override
  String get assign => 'Tugaskan';

  @override
  String get assigned => 'Telah Ditugaskan';

  @override
  String get approve => 'Setujui';

  @override
  String get assign_inspector => 'Tugaskan Inspektor';

  @override
  String get unassigned => 'Belum Ditugaskan';

  @override
  String get general_all => 'Semua';

  @override
  String get general_date => 'Tanggal';

  @override
  String get general_desc => 'Deskripsi';

  @override
  String get general_amount => 'Jumlah';

  @override
  String get assign_search_hint => 'Silakan masukkan nama panggilan/nama/email/nomor ponsel';

  @override
  String get assign_cancel_message => 'Konfirmasi membatalkan penugasan inspektor ini';

  @override
  String get assign_inspect_times => 'Jumlah Inspeksi';

  @override
  String get assign_leave_message_batch => 'Tinggalkan Pesan Massal';

  @override
  String get assign_price_zero_tips => 'Biaya inspeksi tidak boleh 0';

  @override
  String get assign_applied => 'Telah Diajukan';

  @override
  String get is_auth_forbidden => 'Forbidden';

  @override
  String get apply_time => 'Diajukan pada';

  @override
  String get assign_message => 'Pesan';

  @override
  String get chat_send_message => 'Kirim Pesan';

  @override
  String get chat_send_order => 'Kirim Pesanan';

  @override
  String get chat_panel_album => 'Album';

  @override
  String get chat_panel_camera => 'Kamera';

  @override
  String get chat_panel_file => 'File';

  @override
  String get chat_toolbar_custom_service => 'Layanan Pelanggan Khusus';

  @override
  String get chat_toolbar_submit_order => 'Pesan Inspeksi';

  @override
  String get home_navigation => 'Klik untuk Navigasi';

  @override
  String get price_input_error_zero => 'Pesanan harus antara 0 dan 1 juta';

  @override
  String get filter_all => 'Semua Filter';

  @override
  String get filter_heading_order_status => 'Berdasarkan Status Pesanan';

  @override
  String get filter_heading_insp_date => 'Berdasarkan Tanggal Inspeksi';

  @override
  String get filter_heading_order_date => 'Berdasarkan Tanggal Publikasi';

  @override
  String get filter_heading_area => 'Berdasarkan Wilayah';

  @override
  String get filter_date_start => 'Tanggal Mulai';

  @override
  String get filter_date_end => 'Tanggal Selesai';

  @override
  String get filter_date_today => 'Pesanan Hari Ini';

  @override
  String get filter_date_tomorrow => 'Pesanan Besok';

  @override
  String get filter_date_2days_later => 'Pesanan dalam 2 Hari';

  @override
  String get filter_date_3days_later => 'Pesanan dalam 3 Hari';

  @override
  String get sort_by_order_date => 'Urutkan berdasarkan Tanggal Pesanan';

  @override
  String get sort_by_insp_date => 'Urutkan berdasarkan Tanggal Inspeksi';

  @override
  String get sort_by_distance => 'Urutkan berdasarkan Jarak';

  @override
  String get purchase_all_replies => 'Semua Balasan';

  @override
  String get purchase_replies_count => 'balasan';

  @override
  String get purchase_no_more_replies => 'Tidak ada balasan lagi';

  @override
  String get purchase_save_draft_title => 'Simpan sebagai draf?';

  @override
  String get purchase_save_draft_choice => 'Simpan sebagai Draf';

  @override
  String get purchase_save_draft_quit => 'Keluar Langsung';

  @override
  String get purchase_search_hint => 'Cari pesanan pembelian';

  @override
  String get purchase_reply_hint => 'Balas konten postingan';

  @override
  String get purchase_reply_reason_hint => 'Reason why recommend';

  @override
  String get purchase_complaint_hint => 'Memberikan informasi lebih lanjut akan membantu laporan diproses dengan cepat';

  @override
  String get purchase_reply_paid_hint => 'Masukkan konten hadiah';

  @override
  String get purchase_edit => 'Edit Postingan';

  @override
  String get purchase_publish => 'Publikasi Permintaan Hadiah';

  @override
  String get purchase_publish_product_label => 'Nama Produk';

  @override
  String get purchase_publish_title_label => 'Judul Hadiah';

  @override
  String get purchase_publish_quantity => 'Quantity';

  @override
  String get purchase_publish_content_label => 'Deskripsi Rinci';

  @override
  String get purchase_publish_product_hint => 'Silakan masukkan nama produk';

  @override
  String get purchase_publish_title_hint => 'Dapat mencantumkan nama model, wilayah, kuantitas, dll.';

  @override
  String get end_date => 'End date';

  @override
  String get purchase_area => 'Area';

  @override
  String get purchase_permission_author_only => 'Only author of post can see replies';

  @override
  String get purchase_publish_quantity_hint => 'Please input quantity';

  @override
  String get purchase_publish_content_hint => 'Silakan masukkan deskripsi rinci';

  @override
  String get purchase_publish_price => 'Harga Hadiah';

  @override
  String get purchase_publish_choose_category => 'Pilih Kategori';

  @override
  String get purchase_publish_choose_category_hint => 'Silakan pilih kategori';

  @override
  String get purchase_paid_publish_switch => 'Lihat Berbayar';

  @override
  String get purchase_paid_publish_set_price => 'Atur Harga';

  @override
  String get purchase_detail_response_all => 'Semua Balasan';

  @override
  String get purchase_detail_response_author_only => 'Hanya Lihat Penulis';

  @override
  String get purchase_detail_response_asc => 'Urutan Menaik';

  @override
  String get purchase_detail_response_desc => 'Urutan Menurun';

  @override
  String get purchase_detail_more_reply => 'Balas';

  @override
  String get purchase_detail_more_up => 'Suka';

  @override
  String get purchase_detail_more_cancel_up => 'Batal Suka';

  @override
  String get purchase_my_posts => 'Postingan Utama';

  @override
  String get purchase_my_replies => 'Balasan';

  @override
  String get purchase_my_appeals => 'Banding';

  @override
  String get purchase_appeal_detail => 'Detail Banding';

  @override
  String get purchase_appeal_submit => 'Ajukan Banding';

  @override
  String get purchase_appeal_cancel => 'Batalkan Banding';

  @override
  String get purchase_appeal_approve => 'Banding Disetujui';

  @override
  String get purchase_appeal_denied => 'Banding Ditolak';

  @override
  String get purchase_paid_content_owner_tips => 'Konten Berbayar';

  @override
  String get purchase_paid_content_tips => 'Konten berbayar, bayar untuk melihat';

  @override
  String get purchase_paid_content_paid_tips => 'Konten berbayar telah dibuka';

  @override
  String get purchase_review_leave => 'Beri Ulasan';

  @override
  String get purchase_review_my_score => 'Ulasan Saya';

  @override
  String get purchase_my_replies_original_header => 'Postingan Asli';

  @override
  String get purchase_publish_bounty_tips => 'Catatan: Jumlah hadiah dapat diisi secara bebas, hadiah yang lebih tinggi dapat menarik lebih banyak inspektor untuk secara aktif memberikan informasi yang Anda butuhkan.';

  @override
  String get purchase_reply_to => 'Balas ke';

  @override
  String get purchase_modify_bounty => 'Ubah Hadiah';

  @override
  String get purchase_bounty_money => 'Hadiah';

  @override
  String get purchase_evaluated_person => 'orang telah mengevaluasi';

  @override
  String get purchase_comment_paid_supplier => 'Pemasok';

  @override
  String get purchase_comment_paid_contact => 'Kontak';

  @override
  String get purchase_comment_paid_phone => 'Nomor Telepon';

  @override
  String get purchase_comment_paid_email => 'Email';

  @override
  String get purchase_comment_paid_address => 'Alamat Pabrik';

  @override
  String get purchase_comment_paid_other => 'Lainnya';

  @override
  String get purchase_comment_paid_low_price => 'Harga Dasar Produk';

  @override
  String get purchase_appeal_title => 'Ajukan Pengembalian Dana';

  @override
  String get purchase_appeal_reason => 'Silakan masukkan alasan banding';

  @override
  String get purchase_appeal_request_price => 'Jumlah Banding:';

  @override
  String get purchase_appeal_request_reason => 'Alasan Banding:';

  @override
  String get purchase_post_status_draft => 'Draf';

  @override
  String get purchase_post_status_reviewing => 'Sedang Ditinjau';

  @override
  String get purchase_post_status_published => 'Ditinjau dan Disetujui';

  @override
  String get purchase_post_status_denied => 'Tidak Disetujui';

  @override
  String get purchase_post_publish => 'Publikasikan Postingan';

  @override
  String get purchase_complaint_type_leading => 'Silakan pilih jenis laporan';

  @override
  String get purchase_complaint_type_1 => 'Konten Pornografi';

  @override
  String get purchase_complaint_type_2 => 'Iklan Spam';

  @override
  String get purchase_complaint_type_3 => 'Pelecehan/Serangan';

  @override
  String get purchase_complaint_type_4 => 'Kegiatan Ilegal';

  @override
  String get purchase_complaint_type_5 => 'Informasi Politik Tidak Akurat';

  @override
  String get purchase_complaint_type_6 => 'Pelanggaran Hak';

  @override
  String get purchase_complaint_type_7 => 'Lainnya';

  @override
  String get shop_goods_detail_title => 'Detail Produk';

  @override
  String get mall_buy_immediate => 'Beli Sekarang';

  @override
  String get mall_goods_count => 'Jumlah';

  @override
  String get mall_confirm_pay => 'Konfirmasi Pembayaran';

  @override
  String get mall_order_confirm => 'Konfirmasi Pesanan';

  @override
  String get mall_submit_order => 'Kirim Pesanan';

  @override
  String get mall_goods_price => 'Harga Produk';

  @override
  String get mall_express_price => 'Biaya Pengiriman';

  @override
  String get mall_price_total => 'Total:';

  @override
  String get mall_payment => 'Kasir';

  @override
  String get mall_payment_methods => 'Metode Pembayaran';

  @override
  String get mall_pay_succeed => 'Pembayaran Berhasil';

  @override
  String get mall_check_order_detail => 'Lihat Detail Pesanan';

  @override
  String get mall_order_remark => 'Catatan Pesanan';

  @override
  String get mall_order_remark_input => 'Masukkan catatan';

  @override
  String get purchase_detail_more_report => 'Laporkan';

  @override
  String get purchase_reply_paid_content_tips => 'Catatan: Harap isi informasi yang benar, balasan berbayar perlu menunggu peninjauan setelah dipublikasikan, dan hanya dapat dilihat oleh orang lain setelah ditinjau';

  @override
  String get public_ip_address => 'Lokasi IP:';

  @override
  String get inspection_widget_suit_tips => 'Saat inspeksi, harap kenakan kartu identifikasi atau seragam kerja. Jika belum punya, Anda dapat membelinya di halaman utama';

  @override
  String get purchase_paid_content_appeal => 'Banding';

  @override
  String get report_success => 'Laporan Berhasil';

  @override
  String tabbar_tab_names(String names) {
    String _temp0 = intl.Intl.selectLogic(
      names,
      {
        'tab_home': 'Beranda',
        'tab_shortcut': 'Pintasan',
        'tab_message': 'Pesan',
        'tab_mine': 'Saya',
        'tab_publish': '',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String order_status_desc(String status) {
    String _temp0 = intl.Intl.selectLogic(
      status,
      {
        'order_status_unknown': 'Tidak Diketahui',
        'order_wait_pay': 'Menunggu Pembayaran',
        'order_cancelled': 'Dibatalkan',
        'order_cancelled_refund_pending': 'Dibatalkan, Pengembalian Dana Menunggu Peninjauan',
        'order_refund_pending': 'Pengembalian Dana Menunggu Peninjauan',
        'order_refund_partial': 'Pengembalian Dana Sebagian',
        'order_refund_denied': 'Pengembalian Dana Ditolak',
        'order_wait_dispatch': 'Menunggu Penugasan',
        'order_doing': 'Dalam Inspeksi',
        'order_finished': 'Selesai',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String pay_type(String type) {
    String _temp0 = intl.Intl.selectLogic(
      type,
      {
        'pay_rmb': 'Akun RMB',
        'pay_usd': 'Akun USD',
        'pay_zfb': 'Alipay',
        'pay_paypal': 'PayPal',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String purchase_complaint_type(String type) {
    String _temp0 = intl.Intl.selectLogic(
      type,
      {
        'type_1': 'Konten Pornografi',
        'type_2': 'Iklan Spam',
        'type_3': 'Pelecehan/Serangan',
        'type_4': 'Kegiatan Ilegal',
        'type_5': 'Informasi Politik Tidak Akurat',
        'type_6': 'Pelanggaran Hak',
        'type_7': 'Lainnya',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String get purchase_reply_paid_my_evaluate => '(Evaluasi Saya)';

  @override
  String get bind_now => 'Ikat Sekarang';

  @override
  String get cancel_register => 'Batalkan Pendaftaran';

  @override
  String register_and_bind_email0(Object email) {
    return 'Daftar dan ikat $email sebagai email utama';
  }

  @override
  String register_and_bind_email1(Object email) {
    return 'Email Anda $email sudah terdaftar, lanjutkan pengikatan';
  }

  @override
  String get register_and_bind_email2 => 'Daftar dan ikat email utama';

  @override
  String get new_register_bind_title => 'Atur email login utama dan daftar';

  @override
  String get new_register_bind_field_account_tips => 'Ikat email/nomor ponsel';

  @override
  String get register => 'Daftar';

  @override
  String get switch_account => 'Ganti Akun';

  @override
  String get switch_account_confirm_tips => 'Yakin ingin mengganti akun?';

  @override
  String get password_must_have => 'Kata sandi Anda harus memiliki:';

  @override
  String get password_must_have_1 => 'Panjang 8-32 karakter';

  @override
  String get password_must_have_2 => '1 huruf kecil (a-z)';

  @override
  String get password_must_have_3 => '1 angka';

  @override
  String get password_must_have_4 => '1 simbol (seperti !@#\\\$%^&*)';

  @override
  String get password_login => 'Masukkan kata sandi login';

  @override
  String get password_login_again => 'Masukkan kata sandi baru lagi';

  @override
  String get choose_account_to_login => 'Pilih akun untuk login cepat';

  @override
  String get finish => 'Selesai';

  @override
  String get done => 'Selesai';

  @override
  String get account_apple => 'Login Apple';

  @override
  String get account_google => 'Login Google';

  @override
  String get account_wechat => 'Login WeChat';

  @override
  String get account_facebook => 'Login Facebook';

  @override
  String get third_account_unbind => 'Lepaskan Pengikatan';

  @override
  String get third_account_bind => 'Ikat';

  @override
  String get confirm_unbind => 'Yakin ingin melepaskan pengikatan?';

  @override
  String get inspection_requirement => 'Persyaratan Inspeksi';

  @override
  String get liveroom_entrance => 'Antrian Ruang Siaran Langsung';

  @override
  String get add_account => 'Tambah Akun Baru';

  @override
  String get message_order => 'Orders';

  @override
  String get message_email => 'Email';

  @override
  String get message_wallet => 'Wallet';

  @override
  String get message_user => 'User info';

  @override
  String get salesman => 'Salesman';

  @override
  String get public_continue => 'Continue';

  @override
  String get camera_permission_tips => 'In order to facilitate you to upload pictures, files or photos when using the App, you need to allow the use of camera and storage permissions.';

  @override
  String get ai_category_inspector => 'Pemeriksaan';

  @override
  String get ai_nothing_category => 'Tidak ada kategori yang dapat dideteksi';

  @override
  String get ai_category_name => 'Kategori Pemeriksaan';

  @override
  String get ai_quantity => 'Kuantitas';

  @override
  String get ai_packaging => 'Kemasan';

  @override
  String get ai_shipping_mark => 'Marka Pengiriman';

  @override
  String get ai_product_style => 'Gaya Produk';

  @override
  String get ai_test => 'Pengujian';

  @override
  String get ai_craftsmanship => 'Keahlian';

  @override
  String get ai_test_verification => 'Verifikasi Pengujian';

  @override
  String get ai_category_measure => 'Pengukuran';

  @override
  String get ai_spare_parts => 'Suku Cadang';

  @override
  String get ai_sampling_number => 'Nomor Sampling';

  @override
  String ai_input_range_number(Object range) {
    return 'Masukkan angka dalam $range';
  }

  @override
  String ai_enter_range_number(Object range) {
    return 'Masukkan angka dalam $range';
  }

  @override
  String get ai_selected => 'Terpilih';

  @override
  String get ai_selected_status => 'Sudah dipilih';

  @override
  String get ai_order_quantity => 'Kuantitas Pesanan';

  @override
  String get ai_packaged_boxes_quantity => 'Kuantitas Kotak Kemasan (Hasil Akhir)';

  @override
  String get ai_unpackaged_boxes_quantity => 'Kuantitas Kotak Tidak Kemasan (Hasil Akhir)';

  @override
  String get ai_sample_from_packaged => 'Sampling dari Kemasan';

  @override
  String get ai_sample_from_unpackaged => 'Sampling dari Tidak Kemasan';

  @override
  String get ai_spare_parts_quantity => 'Kuantitas Suku Cadang';

  @override
  String get ai_sampling_packaging_number => 'Nomor Kemasan Sampling';

  @override
  String get ai_sampling_packaging_number_record => 'Rekam Nomor Sampling';

  @override
  String get ai_sampling_packaging_number_list => 'Rekam Nomor Kotak Kemasan Sampling';

  @override
  String get ai_judgment => 'Penilaian';

  @override
  String get ai_judgment_item => 'Item Penilaian';

  @override
  String get ai_standard => 'Standar';

  @override
  String get ai_result => 'Hasil';

  @override
  String get ai_conclusion => 'Kesimpulan';

  @override
  String get ai_overall_conclusion => 'Kesimpulan Secara Keseluruhan';

  @override
  String get ai_consistency => 'Apakah konsisten';

  @override
  String get ai_yes => 'Ya';

  @override
  String get ai_no => 'Tidak';

  @override
  String get ai_remarks => 'Catatan';

  @override
  String get ai_numerical => 'Nomor Urut';

  @override
  String get ai_recommended_test_items => 'Item Pemeriksaan yang Direkomendasikan';

  @override
  String get ai_test_item => 'Item Pemeriksaan';

  @override
  String get ai_add_all => 'Tambah Semua';

  @override
  String get ai_add_plus => '+ Tambah';

  @override
  String get ai_add => 'Tambah';

  @override
  String ai_confirm_delete(Object name) {
    return 'Apakah Anda yakin akan menghapus $name?';
  }

  @override
  String get ai_enter_test_item => 'Masukkan item pemeriksaan';

  @override
  String get ai_defect_record => 'Rekam Cacat';

  @override
  String get ai_defect_photo => 'Foto Cacat';

  @override
  String get ai_defect_description => 'Deskripsi Cacat';

  @override
  String get ai_defect_level => 'Tingkat Cacat';

  @override
  String get ai_found_quantity => 'Kuantitas yang ditemukan';

  @override
  String get ai_handling_method => 'Metode Penanganan';

  @override
  String get ai_edit => 'Edit';

  @override
  String get ai_delete => 'Hapus';

  @override
  String get ai_pick_out => 'Pilih Keluar';

  @override
  String get ai_replace => 'Ganti';

  @override
  String get ai_rework => 'Kerja Ulang';

  @override
  String get ai_edit_description => 'Edit Deskripsi';

  @override
  String get ai_critical => 'Kritis';

  @override
  String get ai_important => 'Penting';

  @override
  String get ai_minor => 'Kecil';

  @override
  String get ai_defect_list => 'Daftar Cacat';

  @override
  String get ai_test_level => 'Tingkat Pengujian';

  @override
  String get ai_sampling_sample => 'Sampel Sampling';

  @override
  String get ai_sampling_level => 'Tingkat Sampling';

  @override
  String get ai_additional_information => 'Informasi Tambahan';

  @override
  String get ai_inspection_record => 'Rekam Pemeriksaan';

  @override
  String get ai_sample_count => 'Jumlah Sampel';

  @override
  String get ai_maximum_allowable_value => 'Nilai Maksimum yang Diperbolehkan';

  @override
  String get ai_test_item_name => 'Nama Item Pengujian';

  @override
  String get ai_test_result => 'Hasil Pengujian';

  @override
  String get ai_basic_information => 'Informasi Dasar';

  @override
  String get ai_new_test_item => 'Item Pengujian Baru';

  @override
  String get ai_test_project => 'Proyek Pengujian';

  @override
  String get ai_measurement_project => 'Proyek Pengukuran';

  @override
  String get ai_measure_need_num => 'Persyaratan Jumlah';

  @override
  String get ai_measurement_unit => 'Unit Pengukuran';

  @override
  String get ai_measurement_method => 'Metode Pengukuran';

  @override
  String get ai_measurement_record => 'Catatan Pengukuran';

  @override
  String get ai_measured => 'Sudah Diukur';

  @override
  String get ai_unit_of_measurement => 'Unit Pengukuran';

  @override
  String get ai_measured_value => 'Nilai Pengukuran';

  @override
  String get ai_product_number => 'Nomor Produk';

  @override
  String get ai_number => 'Nomor';

  @override
  String get ai_new_measurement_item => 'Item Pengukuran Baru';

  @override
  String get ai_length_width_height => 'Panjang, Lebar, Tinggi';

  @override
  String get ai_dimensions_length => 'Panjang';

  @override
  String get ai_dimensions_width => 'Lebar';

  @override
  String get ai_dimensions_height => 'Tinggi';

  @override
  String get ai_length_width => 'Panjang & Lebar';

  @override
  String get ai_other => 'Lainnya';

  @override
  String get ai_allowable_error => 'Toleransi Kesalahan';

  @override
  String get ai_report_summary => 'Ringkasan Laporan';

  @override
  String get ai_special_note => 'Perhatian Khusus';

  @override
  String get ai_overall_conclusion_2 => 'Kesimpulan Umum';

  @override
  String get ai_summary => 'Ringkasan';

  @override
  String get ai_category_name_table => 'Kategori Pemeriksaan';

  @override
  String get ai_compliance => 'Memenuhi Persyaratan';

  @override
  String get ai_remarks_2 => 'Keterangan';

  @override
  String get ai_defect_summary => 'Ringkasan Defek';

  @override
  String get ai_no_guidance_instructions => 'Tidak Ada Petunjuk Panduan';

  @override
  String get ai_no_standard_instructions => 'Tidak Ada Petunjuk Standar';

  @override
  String get ai_please_fill_in => 'Silakan Isi';

  @override
  String ai_please_supplement_level_or_sample(Object level, Object sample) {
    return 'Silakan Tambahkan $level atau $sample';
  }

  @override
  String get ai_please_add => 'Silakan Tambahkan';

  @override
  String get ai_please_input => 'Silakan Masukkan';

  @override
  String get ai_please_select => 'Silakan Pilih';

  @override
  String ai_name_not_filled(Object name) {
    return '$name Belum Diisi';
  }

  @override
  String get ai_addition_successful => 'Berhasil Ditambahkan';

  @override
  String get ai_confirm_action => 'Konfirmasi';

  @override
  String get ai_cancel_action => 'Batal';

  @override
  String get ai_submit => 'Kirim';

  @override
  String get ai_next_item => 'Item Selanjutnya';

  @override
  String get ai_complete => 'Selesai';

  @override
  String get ai_change_description => 'Ubah Deskripsi';

  @override
  String get ai_action_guidance_instructions => 'Petunjuk Operasi';

  @override
  String get ai_action_standard_instructions => 'Standar Industri';

  @override
  String get ai_add_description => 'Tambahkan Deskripsi';

  @override
  String get ai_change_description_note => 'Perhatian: Berikut adalah cacat yang telah ditemukan, jika Anda ubahnya, data sejarah juga akan menggunakan deskripsi baru!';

  @override
  String get ai_packing_completion_rate => 'Rasio Penyelesaian Kemasan';

  @override
  String get ai_unprocessed_quantity => 'Jumlah Belum Diproses';

  @override
  String get ai_sample_level_type_0 => 'Level I';

  @override
  String get ai_sample_level_type_1 => 'Level II';

  @override
  String get ai_sample_level_type_2 => 'Level III';

  @override
  String get ai_sample_level_type_s1 => 'S-1';

  @override
  String get ai_sample_level_type_s2 => 'S-2';

  @override
  String get ai_sample_level_type_s3 => 'S-3';

  @override
  String get ai_sample_level_type_s4 => 'S-4';

  @override
  String get ai_sample_level_type_nothing => 'Tidak Ada';

  @override
  String get ai_inspection_image => 'Gambar';

  @override
  String get ai_photo_confirm => 'Konfirmasi';

  @override
  String get ai_add_product_ask_save => 'Apakah Anda ingin menyimpan perubahan ini?';

  @override
  String get ai_add_product_save => 'Simpan';

  @override
  String get ai_add_product_edit_model => 'Edit Model';

  @override
  String get ai_add_product_model_name => 'Nama Model';

  @override
  String get ai_add_product_input_model => 'Masukkan Nama Model';

  @override
  String get ai_add_product_num => 'Jumlah';

  @override
  String get ai_add_product_input_num => 'Masukkan jumlah model';

  @override
  String get ai_add_product_unit => 'Satuan';

  @override
  String get ai_add_product_ask_delete => 'Apakah perlu hapus model ini?';

  @override
  String get ai_add_product_edit_product => 'Edit produk';

  @override
  String get ai_add_product_product_name => 'Nama produk';

  @override
  String get ai_add_product_model => 'Model';

  @override
  String get ai_add_product_input_product_name => 'Masukkan nama produk';

  @override
  String get ai_add_product_new_model => 'Tambah model baru';

  @override
  String get ai_add_product_ask_product => 'Apakah perlu hapus produk ini dan semua modelnya?';

  @override
  String get ai_add_product_picture_lost => 'Gambar hilang';

  @override
  String ai_add_product_lost_des(Object lostStr) {
    return 'Saat ini $lostStr hilang, silakan lengkapi informasi sebelum dilakukan pemeriksaan kembali';
  }

  @override
  String get ai_add_product_model_full => 'Nama model produk';

  @override
  String get ai_add_product_model_title => 'Model produk';

  @override
  String get ai_add_product_new => 'Tambah produk';

  @override
  String get ai_model_unit_piece => 'piece';

  @override
  String get ai_model_unit_only => 'piece';

  @override
  String get ai_model_unit_item => 'piece';

  @override
  String get ai_model_unit_pair => 'pair';

  @override
  String get ai_model_unit_set => 'set';

  @override
  String get ai_model_unit_dozen => 'dozen';

  @override
  String get ai_model_unit_roll => 'roll';

  @override
  String get ai_model_unit_vehicle => 'vehicle';

  @override
  String get ai_model_unit_head => 'head';

  @override
  String get ai_model_unit_bag => 'bag';

  @override
  String get ai_model_unit_box => 'box';

  @override
  String get ai_model_unit_pack => 'pack';

  @override
  String get ai_model_unit_yard => 'yard';

  @override
  String get ai_model_unit_meter => 'meter';

  @override
  String get ai_model_unit_kilogram => 'kilogram';

  @override
  String get ai_model_unit_metric_ton => 'metric ton';

  @override
  String get ai_model_unit_liter => 'liter';

  @override
  String get ai_model_unit_gallon => 'gallon';

  @override
  String get ai_model_unit_other => 'other';

  @override
  String get ai_default_config_des => 'Produk saat ini tidak memiliki template pemeriksaan, Anda dapat memilih template di bawah atau menelepon (+86) untuk konfigurasi template.';

  @override
  String get ai_default_config_category_all => 'Kategori (Semua)';

  @override
  String get ai_default_config_select_template => 'Pilih Template';

  @override
  String get ai_default_config_template_selection => 'Pemilihan Template';

  @override
  String get ai_default_config_search_template => 'Cari Template';

  @override
  String get ai_default_config_classify => 'Kategori';

  @override
  String get ai_default_config_preview => 'Pratinjau';

  @override
  String get ai_default_config_use => 'Terapkan';

  @override
  String get ai_default_config_current_use_button => 'Terapkan ke Produk Ini';

  @override
  String get ai_default_config_more_use_button => 'Terapkan ke Lebih Banyak Produk';

  @override
  String get ai_default_config_product_list => 'Daftar Produk';

  @override
  String get ai_default_config_use_warning => 'Note: 【Mod】Product template already loaded; 【Ops】Template already configured by operations. Loading a new template will overwrite previous data.';

  @override
  String get ai_default_config_tag_default => 'Ops';

  @override
  String get ai_default_config_tag_manual => 'Mod';

  @override
  String get ai_default_config_load_progress => 'Proses Pemuatan';

  @override
  String ai_default_config_template_progress(Object name) {
    return 'Template dimuat selesai $name.';
  }

  @override
  String ai_default_config_template_fail_count(Object name) {
    return '$name gagal, klik untuk mencoba lagi';
  }

  @override
  String get ai_default_config_load => 'Muat';

  @override
  String get ai_default_config_success => 'Sukses';

  @override
  String get ai_default_config_fail => 'Gagal';

  @override
  String get ai_default_config_template => 'template';

  @override
  String get ai_add_model_count_warning => 'The quantity must be greater than 0';

  @override
  String get ai_default_config_product_edit => '编辑产品';

  @override
  String get ai_wait => 'menunggu';

  @override
  String get ai_product_info => 'Informasi Produk';

  @override
  String get ai_product_category => 'Kategori Produk';

  @override
  String get ai_product_unit => 'Satuan Produk';

  @override
  String get ai_package_num_done => 'Kotak yang Sudah Dikemas';

  @override
  String get ai_product_full => 'Informasi Tambahan';

  @override
  String get ai_product_full_tip => 'Informasi produk dalam pesanan tidak lengkap. Harap lengkapi sesuai informasi aktual produk di lokasi inspeksi.';

  @override
  String get ai_each_box => 'Per Kotak';

  @override
  String get ai_simple_count => 'Jumlah Sampel';

  @override
  String get ai_simple_level => 'Standar Sampling';

  @override
  String get ai_simple_num => 'Jumlah Sampling';

  @override
  String get ai_simple_no => 'Nomor Kotak Sampling';

  @override
  String get ai_simple_result => 'Hasil Penilaian';

  @override
  String get ai_simple_project => 'Item Inspeksi';

  @override
  String get ai_simple_project_manage => 'Kelola Item Inspeksi';

  @override
  String get ai_simple_project_edit => 'Edit Item Inspeksi';

  @override
  String get ai_simple_project_recmend => 'Rekomendasi Cerdas';

  @override
  String get ai_simple_project_input => 'Catatan Inspeksi';

  @override
  String get ai_simple_help => 'Bantuan';

  @override
  String get ai_simple_project_record => 'Rekaman Inspeksi';

  @override
  String get ai_simple_require => 'Persyaratan Pelanggan';

  @override
  String get ai_simple_record => 'Rekam';

  @override
  String get ai_simple_dsec => 'Deskripsi';

  @override
  String get ai_simple_before => 'Item Sebelumnya';

  @override
  String get ai_simple_add => 'Tambahkan Grup';

  @override
  String get ai_simple_add_desc => 'Tambahkan deskripsi untuk foto';

  @override
  String get ai_simple_add_citations => 'Kutipan';

  @override
  String get ai_no_more => 'Tidak ada data lagi';

  @override
  String get ai_wrong_tip => 'Jumlah tidak boleh melebihi total';

  @override
  String get ai_defect_records => 'Catatan Cacat';

  @override
  String get ai_check_require => 'Persyaratan Sampling';

  @override
  String get ai_find_defect => 'Temukan Cacat';

  @override
  String get ai_defect_question => 'Masalah Cacat';

  @override
  String get ai_modify_level => 'Ubah Tingkat Sampling';

  @override
  String get ai_defect_quick => 'Tambahkan Cacat Proses dengan Cepat';

  @override
  String get ai_defect_self => 'Nama Cacat Kustom';

  @override
  String get ai_defect_record_list => 'Daftar Catatan Cacat';

  @override
  String get ai_measure_require => 'Persyaratan Pengukuran';

  @override
  String get ai_measurement_item => 'Item Pengukuran';

  @override
  String get ai_measurement_error => 'Kesalahan';

  @override
  String get ai_measurement_standard => 'Standar Pengukuran';

  @override
  String get ai_measurement_value_standard => 'Nilai Standar';

  @override
  String get ai_measurement_camera => 'Foto Pengukuran';

  @override
  String get ai_measurement_add => 'Tambahkan Standar Pengukuran dengan Cepat';

  @override
  String get ai_product_first => 'Gambar Utama Produk';

  @override
  String get ai_product_report => 'Buat Laporan';

  @override
  String get ai_product_report_tip => 'Silakan pilih gambar utama produk';

  @override
  String get ai_product_report_special => 'Masukkan konten yang perlu perhatian khusus';

  @override
  String get ai_product_report_sign => 'Tanda Tangan';

  @override
  String get ai_product_report_sign_done => 'Tanda Tangan Selesai';

  @override
  String get ai_defect_names => 'Nama Cacat';

  @override
  String get ai_input_tip => 'Masukkan nama';

  @override
  String get ai_add_measure_tip => 'Harap tambahkan standar pengukuran terlebih dahulu';

  @override
  String get ai_wrong_num => 'Jumlah Salah';

  @override
  String get ai_wrong_name => 'Masukkan nama produk';

  @override
  String get ai_wrong_sample_num => 'Tidak boleh melebihi jumlah sampel';

  @override
  String get ai_per_box => 'Jumlah per Kotak';

  @override
  String get ai_wrong_sample_num_cal => 'Sampel yang dikemas + sampel yang belum dikemas harus sama dengan jumlah sampel';

  @override
  String get ai_sure_delete => 'Yakin ingin menghapus?';

  @override
  String get ai_choose_tip => 'Pilih metode dan jumlah penanganan cacat';

  @override
  String get ai_weight => 'Berat Kotor';

  @override
  String get sampling_plan => 'Rencana Sampling';

  @override
  String get single => 'Tunggal';

  @override
  String get normal => 'Normal';

  @override
  String get summarize => 'Summarize';

  @override
  String get po_number => 'Nomor PO';

  @override
  String get product_quantity => 'Jumlah Produk';

  @override
  String get customer_name => 'Nama Pelanggan';

  @override
  String get supplier_name => 'Nama Pemasok';

  @override
  String get inspection_date => 'Tanggal Inspeksi';

  @override
  String get arrival_time => 'Waktu Kedatangan';

  @override
  String get completion_time => 'Waktu Penyelesaian';

  @override
  String get inspection_address => 'Alamat Inspeksi';

  @override
  String get inspector => 'Inspektor';

  @override
  String get inspection_report_note => 'This inspection report is for reference only. Final approval is subject to customer confirmation.';

  @override
  String get remark_toast => 'Please fill in the remarks first';

  @override
  String get process_appearance_judgment => 'Process appearance judgment';

  @override
  String get test_validation_judgment => 'Test validation judgment';

  @override
  String check_save(Object name) {
    return 'Are you sure you want to save $name?';
  }

  @override
  String get select_template_config_tip => 'If you need to configure the inspection template, please contact your order follower';
}
