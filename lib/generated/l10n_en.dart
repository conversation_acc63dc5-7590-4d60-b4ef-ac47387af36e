import 'package:intl/intl.dart' as intl;

import 'l10n.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class SEn extends S {
  SEn([String locale = 'en']) : super(locale);

  @override
  String get app_name => 'Inspector Online';

  @override
  String get search => 'Search';

  @override
  String get shortcut_tab_name => 'Shortcuts';

  @override
  String get loading => 'Loading...';

  @override
  String get nomore => 'No more items';

  @override
  String get confirm => 'Confirm';

  @override
  String get more_replies => 'More Replies';

  @override
  String get purchase_paid_publish_information_title => 'Set viewing fee';

  @override
  String get purchase_set_fee => '设置查看费用';

  @override
  String get purchase_comment_paid_supplier_hint => 'Please input supplier name';

  @override
  String get purchase_comment_paid_contact_hint => 'Please input contact name';

  @override
  String get purchase_comment_paid_phone_hint => 'Please input phone number';

  @override
  String get purchase_comment_paid_email_hint => 'Please input email address';

  @override
  String get purchase_comment_paid_address_hint => 'Please input factory address';

  @override
  String get purchase_comment_paid_other_hint => 'Other information (Optional)';

  @override
  String get purchase_comment_paid_low_price_hint => 'Please input low price (Optional)';

  @override
  String get purchase_reply_paid_title => 'Paid Content';

  @override
  String get purchase_reply_paid_desc => ' (Supplier and price informations)';

  @override
  String purchase_reply_paid_evaluated_people(Object people_num) {
    return '($people_num evaluated)';
  }

  @override
  String get language_setting => 'Language Setting';

  @override
  String get public_and => 'and';

  @override
  String get public_publish => 'publish';

  @override
  String get public_distance => 'distance';

  @override
  String get public_deny => 'deny';

  @override
  String get public_see_more => 'See more';

  @override
  String get general_select => 'select';

  @override
  String get language_page_title => 'Languages';

  @override
  String get language_chinese => 'Chinese';

  @override
  String get language_english => 'English';

  @override
  String get public_seconds_ago => 'seconds ago';

  @override
  String get public_minutes_ago => 'minutes ago';

  @override
  String get public_hours_ago => 'hours ago';

  @override
  String get public_status_applied => 'Applied';

  @override
  String get public_status_refused => 'Denied';

  @override
  String get public_status_approved => 'Approved';

  @override
  String get public_status_canceled => 'Canceled';

  @override
  String get public_app_name => 'Inspector';

  @override
  String get public_send => 'Send';

  @override
  String get public_ok => 'Ok';

  @override
  String get public_price => 'Price';

  @override
  String get public_cancel => 'Cancel';

  @override
  String get public_manage => 'Manage';

  @override
  String get public_finish => 'Finish';

  @override
  String get public_reset => 'Reset';

  @override
  String get public_leave_message => 'Message';

  @override
  String get public_share => 'Share';

  @override
  String get login_submit => 'Login';

  @override
  String get login_mobile_login => 'Mobile';

  @override
  String get login_mobile_tips => 'Please enter mobile';

  @override
  String get login_email_login => 'Email';

  @override
  String get login_email_tips => 'Please enter email';

  @override
  String get registry_email_phone_tips => 'Please enter email or phone number';

  @override
  String get login_verify_tips => 'Please enter verification code';

  @override
  String get login_password_tips => 'Please enter password';

  @override
  String get login_password_login => 'Login with password';

  @override
  String get login_verify_login => 'Login with verification code';

  @override
  String get login_register => 'Create account';

  @override
  String get login_take_code => 'Send';

  @override
  String get login_forget_password => 'Forgot password';

  @override
  String get login_agreement => 'I agree to Inspector.Itd Agreement';

  @override
  String get login_area_selected => 'Select Country';

  @override
  String get tab_home => 'Home';

  @override
  String get tab_order => 'Order';

  @override
  String get tab_shortcut => 'Shortcut';

  @override
  String get tab_purchase => 'Purchase';

  @override
  String get tab_message => 'Message';

  @override
  String get tab_mine => 'Me';

  @override
  String get supplement_title => 'Please complete your personal information first';

  @override
  String get supplement_next => 'Go';

  @override
  String get home_title => 'Inspection Square';

  @override
  String get home_record => 'Records';

  @override
  String get home_newest => 'Latest';

  @override
  String get home_nearest => 'Nearest';

  @override
  String home_recommend(Object money) {
    return 'Referral Reward RMB$money\\\$';
  }

  @override
  String get home_sampling => 'Hypothesis Testing';

  @override
  String get home_word => 'WORD REPORT';

  @override
  String home_unit(Object day, Object people) {
    return '$people person/$day day';
  }

  @override
  String get home_product_tip => 'Product:';

  @override
  String get home_person_apply => ' Applied';

  @override
  String get home_know_tip => 'Terms & Privacy Policy.';

  @override
  String get home_inspection_tip => 'The inspection fee defaults to the merchant\"s pricing,can be modified,low cost may be given priority for assignment.';

  @override
  String get home_reviewed => 'I agree to our ';

  @override
  String get home_apply => 'Apply';

  @override
  String get home_apply_price => 'Please enter the amount';

  @override
  String get home_apply_check => 'Please refer to the inspection notice';

  @override
  String get home_apply_tips => 'you are not a certified inspector, but you have more than 1 year of experience in foreign trade inspection, please provide relevant qualification proofs.';

  @override
  String get home_complete_profile_tips => 'Completing relevant information for the inspector can increase the application success rate';

  @override
  String get home_apply_sure => 'Submit';

  @override
  String get home_complete_profile_sure => 'Go';

  @override
  String get home_apply_cancel => 'Cancel';

  @override
  String get home_update => 'Edit';

  @override
  String get home_navi => '';

  @override
  String get mine_unauth => 'Not certified';

  @override
  String get mine_checking => 'Pending Approval';

  @override
  String get mine_check_failed => 'Approval Rejected';

  @override
  String get mine_vip_level => 'VIP Level';

  @override
  String get mine_credit_quota => 'Credit quota';

  @override
  String get mine_authed => 'Certified Inspector';

  @override
  String get mine_authed_inspector => 'Modify Certified Inspector';

  @override
  String get mine_amount => 'Balance';

  @override
  String get mine_cash => 'Recharge/Withdrawal';

  @override
  String get mine_order => 'My Orders';

  @override
  String get mine_purchase => 'My Purchase';

  @override
  String get mine_check => 'My inspections';

  @override
  String get mine_address => 'Address Book(supplier)';

  @override
  String get mine_recommend => 'Referral';

  @override
  String get mine_setting => 'Settings';

  @override
  String get mine_header_inspect => 'Inspect management';

  @override
  String get mine_header_purchase => 'Purchase management';

  @override
  String get mine_header_other => 'Others';

  @override
  String get mine_inspect_mine => 'My Inspect';

  @override
  String get mine_inspect_order => 'Order';

  @override
  String get mine_inspect_history => 'Apply history';

  @override
  String get mine_purchase_mine => 'My purchase';

  @override
  String get mine_purchase_reply => 'Replies';

  @override
  String get mine_purchase_appeal => 'Appeals';

  @override
  String get mine_other_recommend => 'Referral';

  @override
  String get mine_other_address => 'Address Book(supplier)';

  @override
  String get mine_other_settings => 'Settings';

  @override
  String get profile_title => 'Personal Information';

  @override
  String get profile_avatar => 'Avatar';

  @override
  String get profile_name => 'Nickname';

  @override
  String get profile_mobile => 'Mobile';

  @override
  String get profile_country => 'Country';

  @override
  String get profile_real_name => 'Real name';

  @override
  String get profile_city => 'City';

  @override
  String get profile_email => 'Email';

  @override
  String get profile_wechat => 'WeChat';

  @override
  String get profile_bind_manage => 'Third login management';

  @override
  String get profile_info_failed => 'Information update failed';

  @override
  String get apply_title => 'Inspector Qualification Application';

  @override
  String get apply_nick => 'Nickname';

  @override
  String get apply_sex => 'Gender';

  @override
  String get apply_birthday => 'Birthday';

  @override
  String get apply_education => 'Education Background';

  @override
  String get apply_address => 'Permanent Address';

  @override
  String get apply_price => 'Min Fee';

  @override
  String get apply_shebao => 'Social Security';

  @override
  String get apply_id_card => 'ID number';

  @override
  String get apply_file => 'Edit Resume';

  @override
  String get apply_file_tip => 'Please enter your basic information\n and experiences';

  @override
  String get apply_upload_file => 'Upload Resume';

  @override
  String get apply_upload_file_failed => 'Upload failed';

  @override
  String get apply_upload_card => 'Upload ID Card Photos';

  @override
  String get apply_card_front => 'Front-facing Photo';

  @override
  String get apply_card_back => 'Back-facing Photo';

  @override
  String get apply_submit => 'Submit';

  @override
  String get apply_enter => 'Please enter';

  @override
  String get apply_next_tip => 'Please complete the information before submitting';

  @override
  String get apply_auth_failed => 'ID verification failed, please upload a correct photo';

  @override
  String get apply_checking => 'Pending';

  @override
  String get apply_check_success => 'Approved';

  @override
  String get apply_check_failed => 'Verification failed, please modify the content and resubmit';

  @override
  String get order_title => 'My Orders';

  @override
  String get order_input => 'Inspections';

  @override
  String get order_output => 'Orders';

  @override
  String get order_all => 'All';

  @override
  String get order_wait_pay => 'Pending payment';

  @override
  String get order_cancelled => 'Cancelled';

  @override
  String get order_status => 'Order status';

  @override
  String get order_status_unknown => 'Unknown';

  @override
  String get order_refund_pending => 'Refund pending';

  @override
  String get order_cancelled_refund_pending => 'Cancelled, refund pending';

  @override
  String get order_refund_partial => 'Partial refund';

  @override
  String get order_refund_denied => 'Refund denied';

  @override
  String get order_wait_dispatch => 'Awaiting';

  @override
  String get order_ready_inspect => 'Preparing';

  @override
  String get order_need_pay => 'Unpaid';

  @override
  String get order_wait => 'Preparing';

  @override
  String get order_confirm => 'Confirmed';

  @override
  String get order_doing => 'Inspecting';

  @override
  String get order_comment => 'Pending Evaluation';

  @override
  String get order_finished => 'Completed';

  @override
  String get order_goods_info => 'Products';

  @override
  String get order_goods_name => 'Product';

  @override
  String get order_goods_model => 'Model';

  @override
  String get order_goods_count => 'Count';

  @override
  String get order_goods_unit => 'Unit';

  @override
  String get order_order_time => 'Date';

  @override
  String get order_order_amount => 'Amount';

  @override
  String get order_detail_title => 'Detail';

  @override
  String get order_applying => 'Applied';

  @override
  String get order_apply_expired => 'Expired';

  @override
  String get order_apply_dispatched => 'Dispatched';

  @override
  String get order_create_time => 'Created At';

  @override
  String get order_look => 'View inspection report';

  @override
  String get order_report_next => 'Submit inspection report';

  @override
  String get order_detail_inspection_info => 'Inspection Information';

  @override
  String get order_inspection_status_unpaid => 'Unpaid';

  @override
  String get order_inspection_status_returned => 'Returned';

  @override
  String get order_inspection_status_waiting_start => 'Waiting start';

  @override
  String get order_detail_related_info => 'Associated sub-order';

  @override
  String get order_detail_inspection_product => 'Product';

  @override
  String get order_detail_inspection_time => 'Inspection Date';

  @override
  String get order_detail_inspection_city => 'City';

  @override
  String get order_detail_inspection_factory => 'Factory';

  @override
  String get order_detail_inspection_address => 'Address';

  @override
  String get order_detail_inspection_person => 'Contact';

  @override
  String get order_detail_inspection_phone => 'Phone';

  @override
  String get order_detail_inspection_email => 'Email';

  @override
  String get order_detail_inspection_amount => 'Price Information';

  @override
  String get order_detail_inspection_sample => 'Samples';

  @override
  String get order_detail_inspection_standard => 'Sample standard';

  @override
  String get order_detail_inspection_critical => 'AQL';

  @override
  String get order_detail_inspection_major => 'Major';

  @override
  String get order_detail_inspection_minor => 'Minor';

  @override
  String get order_detail_inspection_day => 'Days';

  @override
  String get order_detail_inspection_number => 'Number of People';

  @override
  String get order_detail_inspection_price => 'Price';

  @override
  String get order_detail_inspection_price_apply => 'Requested Price';

  @override
  String get order_detail_inspection_template => 'Templates';

  @override
  String get order_detail_inspection_file => 'Attachments';

  @override
  String get order_detail_inspection_image => 'Images';

  @override
  String get order_detail_inspection_type => 'Servie Type';

  @override
  String get order_tips => 'Note';

  @override
  String get order_apply => 'Application for Inspecting';

  @override
  String get order_cancel => 'Cancel';

  @override
  String get order_canceled => 'Cancelled';

  @override
  String get order_dispatch_accept => 'Accept';

  @override
  String get order_dispatch_refuse => 'Refuse';

  @override
  String get order_inspection => 'Begin the Inspection';

  @override
  String get order_sure_cancel => 'Sure to cancel the order';

  @override
  String get order_sure_refuse => 'Sure to refuse this order';

  @override
  String get order_sure_confirm => 'Sure to accept this order';

  @override
  String get publish_title => 'Place an Order';

  @override
  String get publish_edit_title => 'Update Order';

  @override
  String get publish_welcome => 'Welcome to place an order, free quoting and communication';

  @override
  String get publish_sampling => 'Final Random Inspection (FRI)';

  @override
  String get publish_sampling_content => 'The inspection will be performed after 100% finished production and above 80% packing at least. The inspector will choose samples randomly according to ANSI/ASQZ1.4 (MIL-STD-105E) and your requirement accordingly,during inspection,we will check the data like appearance, workmanship, function, safety and other points required by customer. Picking out defectives and ask factory to rework or replace. The formal report will shows the entire knowledge of the cargo quality.';

  @override
  String get publish_point => 'Check Pint';

  @override
  String get publish_sampling_point => '● Clients requirement/Sample comparison\n● Quantity check\n● Product size/pattern/color\n● Appearance quality check\n● Functional test & Safety test\n● Shipping mark\n● Packing safety\n● Packing details\n● Special requirements';

  @override
  String get publish_all => 'Full Day Inspection (FUI)';

  @override
  String get publish_all_content => 'Full check means that we will go to the factory after the goods are 100% finished production,it can be happened before or after packing. We inspect all the products one by one, check the data like appearance, workmanship, function, safety and other points required by customer,to distinguish the pass products and defects.Picking out defectives and inform the factory to replace or rework. (If cargo quantity is large,will need to add inspectors or days,and charge will also be added)';

  @override
  String get publish_online => 'Onlie Inspection (OLI)';

  @override
  String get publish_online_content => 'This inspection will be happened during production,or 30-50% production finished,to check the product quality,pattern,color,size functional,appreance etc,to ensure the whole production will be consistent with the sample and contract,also It will be helpful to find out the problem earlier,to avoid the delay of delivery.';

  @override
  String get publish_online_point => '● Processing follow up\n● Production assessment & Production speed\n● Semi-products and finished products\n● Check the packing info and package\n● Advise improve the defective\n● Assess the delivery time';

  @override
  String get publish_factory => 'Factory Audit (FAT)';

  @override
  String get publish_factory_content => 'Factory Audits can uncover problems before production or order confirmation. You could have a clear understanding of your supplier\"s capabilities, quality control system, management and operating procedures. This enables you to select a qualified supplier with confidence.';

  @override
  String get publish_factory_point_title => 'Check points';

  @override
  String get publish_factory_point => '● Basic profile\n● Organization Structure\n● Production Process\n● Production ability\n● R&D\n● Facilities & Machinery\n● Quality assurance system & related certificates';

  @override
  String get publish_factory_review => 'Comprehensive evaluation';

  @override
  String get publish_factory_review_content => '● For each audit item, weigh the importance of each other, give different scores respectively, and then issue the qualification scoring table according to the audit questionnaire and field survey data';

  @override
  String get publish_watch => 'Container Loading Supervision (CLS)';

  @override
  String get publish_watch_content => 'Container loading supervision includes the checking of container condition,to ensure it is good for loading; To supervise your goods will be loaded accordingly correctly,and confirm the container been sealed safety.';

  @override
  String get publish_watch_point => '● Record the Container No and Trunck No\n● The container should be in good condition,no damage/wet/odor\n● Pre-load goods cartons quantity,randomly open some cartons to check YOUR goods inside\n● Supervise the loading without damage goods\n● The container seal and packing list';

  @override
  String get publish_watch_inspection => 'Inspection+Loading supervision (FRI+CLS)';

  @override
  String get publish_watch_inspection_content => 'To ensure the goods’s final quality and quantity,the inspector will randomly pick samples according to ANSI/ASQZ1.4 (MIL-STD-105E) from whole goods to check quality and product pattern,and supervise the loading supervision.';

  @override
  String get publish_watch_inspection_point => '● Products appreance/qualtiy/size measurement/functional/packing etc\n● Show the defects to the supplier,require replace or rework\n● The container should be in good condition,no damage/wet/odor\n● Supervise the loading without damage goods\n● The container seal and packing list';

  @override
  String get publish_next => 'Next';

  @override
  String get publish_inspection_time => 'Date';

  @override
  String get publish_inspection_time_selected => 'Inspection Date';

  @override
  String get publish_inspection_time_tip => 'Please Select';

  @override
  String get publish_inspection_people => 'Number';

  @override
  String get publish_people => 'People';

  @override
  String get publish_day => 'Day';

  @override
  String get publish_inspection_factory => 'Factory';

  @override
  String get publish_factory_tips => 'Please enter factory information';

  @override
  String get publish_address_book => 'Address Book';

  @override
  String get publish_goods_name => 'Product';

  @override
  String get publish_name_tips => 'Please enter product information';

  @override
  String get publish_po_tips => 'Please input P.O';

  @override
  String get publish_file_tips => 'Attachment';

  @override
  String get publish_camera => 'Camera';

  @override
  String get publish_file => 'Upload Files';

  @override
  String get publish_purchase => 'Create purchase order';

  @override
  String get publish_inspection => 'Create inspection order';

  @override
  String get publish_factory_tip => 'Please Select Address';

  @override
  String get publish_attention => 'Attentions';

  @override
  String get publish_attention_tips => 'What critical points that you want \nthe inspector to pay attention?';

  @override
  String get publish_stand_price => 'Fixed Price';

  @override
  String get publish_click_price => '';

  @override
  String get publish_vip_price => 'VIP Price';

  @override
  String get publish_vip_tips => 'Includes thorough personal service';

  @override
  String get publish_total => 'Total';

  @override
  String get publish_submit => 'Submit';

  @override
  String get publish_only_price_failed => 'No fixed price authorization';

  @override
  String get publish_price_tip => 'Please Select Price';

  @override
  String get publish_date_tips => 'Please Select Data';

  @override
  String get date_title => 'Inspect Date';

  @override
  String get date_save => 'Save';

  @override
  String get address_title => 'Update Factory Information';

  @override
  String get address_auto_tips => 'Input / paste the whole address, automatically identify the contact, email, phone, factory name and address';

  @override
  String get address_paste => 'Paste';

  @override
  String get address_ocr => 'Recognition';

  @override
  String get address_name => 'Factory Name';

  @override
  String get address_name_tip => 'Please enter factory information';

  @override
  String get address_person => 'Contact';

  @override
  String get address_person_tip => 'Please Enter Contact';

  @override
  String get address_mobile => 'Mobile';

  @override
  String get address_mobile_tip => 'Please enter mobile number';

  @override
  String get address_email => 'Email';

  @override
  String get address_email_tip => 'Please enter email address';

  @override
  String get address_area => 'Province/City/District';

  @override
  String get address_area_tip => 'Please select province/city/district 〉';

  @override
  String get address_detail => 'Detailed Address';

  @override
  String get address_detail_tip => 'Enter street name, house/building number and other details';

  @override
  String get address_location => 'Geolocation';

  @override
  String get address_save_tip => 'Save';

  @override
  String get address_clear => 'Clear';

  @override
  String get address_submit => 'Submit';

  @override
  String get address_recent => 'Recently Used Addresses';

  @override
  String get address_more => 'More Addresses';

  @override
  String get address_list_title => 'Address Management';

  @override
  String get address_insert => 'New Address';

  @override
  String get address_delete => 'Deleted';

  @override
  String get address_delete_result => 'Failed to delete';

  @override
  String get address_edit => 'Edit';

  @override
  String get address_delete_tips => 'Are you sure to delete?';

  @override
  String get address_detected_paste => 'Would you like to paste?';

  @override
  String get pay_title => 'Pay Order';

  @override
  String get pay_time => 'Remaining payment time';

  @override
  String get pay_paypal => 'PayPal';

  @override
  String get pay_zfb => 'AliPay';

  @override
  String get pay_usd => 'USD Account';

  @override
  String get pay_rmb => 'RMB Account';

  @override
  String get pay_pay => 'Pay';

  @override
  String get pay_result_success => 'Payment Successful';

  @override
  String get pay_result_success_wait => 'Payment successful, waiting for handling.';

  @override
  String get pay_result_failed => 'Payment Failed';

  @override
  String get pay_keep => 'Can be Record';

  @override
  String get check_title => 'Inspection Report';

  @override
  String get check_picture => 'Inspection Photos';

  @override
  String get check_file => 'Letter of Integrity Commitment';

  @override
  String get check_report => 'Inspection Result(manual)';

  @override
  String get check_draft => 'Draft';

  @override
  String get check_template => 'Report Template';

  @override
  String get check_submit => 'Submit';

  @override
  String get check_hint => 'Please enter image description';

  @override
  String get check_checking => 'Report Being Reviewed';

  @override
  String get check_check_success => 'Report Approved';

  @override
  String get check_check_failed => 'Report review failed, please revise the content and resubmit';

  @override
  String get review_title => 'Service Rating';

  @override
  String get review_next => 'Rating';

  @override
  String get contact_bnt => 'Chat Now';

  @override
  String get review_score => 'Service Quality';

  @override
  String get review_score1 => 'Disappointed';

  @override
  String get review_score2 => 'Unsatisfied';

  @override
  String get review_score3 => 'Average';

  @override
  String get review_score4 => 'Satisfied';

  @override
  String get review_score5 => 'Surprised';

  @override
  String get review_tips => 'Evaluate from multiple perspectives to help us further understand the inspector\"s work abilities';

  @override
  String get review_picture => 'Image';

  @override
  String get review_submit => 'Submit';

  @override
  String get setting_title => 'Settings';

  @override
  String get setting_address => 'Address Management';

  @override
  String get setting_clear_cache => 'Clear Cache';

  @override
  String get setting_clear_success => 'Successfully Cleared';

  @override
  String get setting_about_us => 'About Us';

  @override
  String get setting_receive_msg => 'Message Notification';

  @override
  String get setting_version => 'Version';

  @override
  String get setting_check_update => 'Check update';

  @override
  String get setting_login_out => 'Logout';

  @override
  String get setting_login_out_tips => 'Are you sure to logout？';

  @override
  String get setting_delete_account_tips => 'Are you sure to delete your account？';

  @override
  String get setting_policy_title => 'Privacy policy tips';

  @override
  String get setting_policy_sub_title => 'Please read and agree before next step';

  @override
  String get setting_privacy_policy => 'Privacy Policy';

  @override
  String get setting_user_agreement => 'User Agreement';

  @override
  String get setting_privacy_content => 'Welcome to the Inspection Online App\nWe attach great importance to your privacy and personal information protection. When you use this App, we will collect and use some of your personal information\n1. After you agree to the App Privacy Policy , we will initialize the integrated SDK and collect your device MAC address, IMSI, Android ID, IP address, hardware model, operating system version number, unique device identifier (IMEI, etc.), network device hardware address (MAC) , software version number, network access method, type, status, network quality data, operation logs, hardware serial numbers, service log information, etc. to ensure normal data statistics and security risk control of the App. \n2. We will not obtain, share or provide your information to third parties without your consent. \n3. You can access, correct, and delete your personal information, and we will also provide ways to cancel and complain. \n';

  @override
  String get setting_ihavereadandagreed => 'I have read and agreed ';

  @override
  String get setting_policy_tips2 => 'Please read carefully and understand ';

  @override
  String get wallet_title => 'Wallet';

  @override
  String get wallet_bill => 'Bill';

  @override
  String get wallet_rmb_account => 'RMB Account';

  @override
  String get wallet_usd_account => 'USD Account';

  @override
  String get wallet_account_heading => 'Account and settings';

  @override
  String get wallet_bank => 'Bank Card';

  @override
  String get wallet_wechat => 'WeChat';

  @override
  String get wallet_alipay => 'Alipay';

  @override
  String get wallet_charge => 'Recharge';

  @override
  String get wallet_cash => 'Withdrawal';

  @override
  String get wallet_balance => 'Balance';

  @override
  String get wallet_default_account => 'Default account';

  @override
  String get wallet_set_default_account => 'Set as default account';

  @override
  String get bill_title => 'Bill';

  @override
  String get bill_out => 'Expend';

  @override
  String get bill_in => 'Incoming';

  @override
  String get bill_month => 'Month';

  @override
  String get bill_fenxi => 'Operations Flow';

  @override
  String get bill_unfreeze => 'Unfreeze';

  @override
  String get bill_all => 'All';

  @override
  String get bill_income => 'Income';

  @override
  String get bill_outcome => 'Outcome';

  @override
  String get bill_freeze => 'Freeze';

  @override
  String get bill_withdraw => 'Withdraw';

  @override
  String get bank_title => 'My Bank Card';

  @override
  String get bank_add => 'New Bank Card';

  @override
  String get add_bank_title => 'New Bank Card';

  @override
  String get add_bank_name => 'Bank Name';

  @override
  String get add_bank_branch => 'Bank of Deposit';

  @override
  String get add_bank_card => 'Card Number';

  @override
  String get add_bank_real_name => 'Name';

  @override
  String get add_bank_address => 'Bank Address';

  @override
  String bind_title(Object bind) {
    return 'Binding $bind';
  }

  @override
  String get bind_account => 'Account';

  @override
  String get bind_image => 'Collection Code';

  @override
  String get bind_name => 'Name';

  @override
  String get bind_hint => 'Please enter';

  @override
  String get charge_title => 'Recharge';

  @override
  String get charge_account => 'Account';

  @override
  String get charge_money => 'Amount';

  @override
  String get charge_deposit_type_title => 'Charge Type';

  @override
  String get charge_deposit_type_online => 'Online Charge';

  @override
  String get charge_deposit_type_offline => 'Offline Charge';

  @override
  String get charge_offline_nopic_hint => 'Please upload your proof';

  @override
  String get charge_upload_proof => 'Please upload your proof';

  @override
  String get withdraw_list_title => 'Withdraw History';

  @override
  String get withdraw_rmb => 'Withdraw in RMB';

  @override
  String get withdraw_usd => 'Withdraw in USD';

  @override
  String get withdraw_status_checking => 'Withdrawing';

  @override
  String get withdraw_status_approved => 'Approved';

  @override
  String get withdraw_status_denied => 'Denied';

  @override
  String get withdraw_cash_status_unfinished => 'Unfinished';

  @override
  String get withdraw_cash_status_done => 'Finished';

  @override
  String get withdraw_cash_status_refused => 'Refused';

  @override
  String get charge_hint => 'Please enter amount';

  @override
  String get charge_submit => 'Submit';

  @override
  String get charge_rmb => 'Charge in RMB';

  @override
  String get charge_usd => 'Charge in USD';

  @override
  String get charge_history_title => 'Charge History';

  @override
  String get cash_title => 'Withdrawal';

  @override
  String get cash_account => 'Please select account';

  @override
  String get cash_money => 'Amount';

  @override
  String get cash_invoice_money => 'Invoice Amount';

  @override
  String get cash_invoice_money_hint => 'Please input invoice amount';

  @override
  String get cash_invoice_upload => 'Upload invoice';

  @override
  String get cash_account_list_title => 'Money will be deposit to these accounts randomly';

  @override
  String get cash_hint => 'Please enter amount';

  @override
  String get cash_withdraw_tips1 => 'You are withdrawing to ';

  @override
  String get cash_withdraw_tips2 => ', amount is ';

  @override
  String get cash_amount => 'Receipt Amount';

  @override
  String get cash_other => 'Fee';

  @override
  String get cash_submit => 'Submit';

  @override
  String get location_permission => 'Please enable location permission';

  @override
  String get location_cancel => 'Cancel';

  @override
  String get location_author => 'Authorization';

  @override
  String get group_title => 'Group Members';

  @override
  String get unknown_error => 'unknown error';

  @override
  String get data_parsing_exception => 'Data parsing exception';

  @override
  String get edit => 'Edit';

  @override
  String get no_data => 'No Data';

  @override
  String get note => 'Note';

  @override
  String get msg_locating => 'Locating';

  @override
  String get failed_to_download => 'Failed to download';

  @override
  String get pick_address => 'Click to input address of factory';

  @override
  String get update_now => 'Update Now';

  @override
  String get message => 'Message';

  @override
  String get view_order => 'View Order';

  @override
  String get today => 'Today';

  @override
  String get yesterday => 'Yesterday';

  @override
  String get send_file => 'Send File';

  @override
  String get login_expired => 'Senssion expired,please login again';

  @override
  String get exit_group_chat_confirm => 'Are you sure to exit group chat?';

  @override
  String get exit_group_chat_success => 'Exit Successfully';

  @override
  String get exit_group_chat_page_title => 'Group Chat Information';

  @override
  String get exit_group_chat_button_title => 'Exit Group Chat';

  @override
  String get group_chat_setting_view_more => 'View More';

  @override
  String get group_chat_setting_name => 'Group Chat Name';

  @override
  String get group_chat_setting_owner_update => 'Only the owner is allowed to modify the group name.';

  @override
  String get group_chat_name_page_title => 'Update Group Chat Name';

  @override
  String get group_chat_name_page_required => 'Please enter group chat name';

  @override
  String get group_chat_name_save => 'Save';

  @override
  String get group_chat_name_saved => 'Saved Successfully';

  @override
  String get conversation_manage_view_please => 'Please select the session that needs to be operated';

  @override
  String get conversation_manage_view_list => 'Conversation List';

  @override
  String get group_manage_select => 'Please select the group that needs to be operated';

  @override
  String get group_manage_list => 'Group List';

  @override
  String get please_enter => 'Pleas enter ';

  @override
  String get address_keyword => 'Please enter address keywords';

  @override
  String get inspector_min_fee => 'Please enter the minimum inspection fee';

  @override
  String get inspector_id_card_required => 'Please enter ID card number';

  @override
  String get inspector_id_card_upload => 'Please upload ID images';

  @override
  String get inspector_id_card_upload_fail => 'Failed to upload, please again';

  @override
  String get inspector_revoke => 'Are you sure to revoke the qualification of the inspector?';

  @override
  String get inspector_revoke_completed => 'Completed to revoke';

  @override
  String get male => 'Male';

  @override
  String get female => 'Female';

  @override
  String get elementary => 'Elementary';

  @override
  String get junior => 'Junior High';

  @override
  String get technical => 'Technical';

  @override
  String get senior => 'Senior';

  @override
  String get college => 'college';

  @override
  String get bachelor => 'Bachelor';

  @override
  String get master => 'Master';

  @override
  String get doctor => 'Doctor';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get upload_image => 'Upload Image';

  @override
  String get upload_file => 'Upload File';

  @override
  String get revoke_inspector => 'Revoke';

  @override
  String get deposit_card => 'Deposit Card';

  @override
  String get withdrawal_balance => 'The withdrawal amount cannot exceed the account balance.';

  @override
  String get failed_get_payment_info => 'Failed to obtain payment information.';

  @override
  String get recommended_order => 'Recommended for Ordering';

  @override
  String get withdrawal_method => 'Please provide at least one withdrawal method.';

  @override
  String get withdrawal_bind_alipay => 'Please bind alipay first';

  @override
  String get enabled_camera => 'Please enable camera access for taking photos';

  @override
  String get valid_email_mobile => 'Please enter a valid email address or phone number';

  @override
  String get apple_map => 'Apple Maps';

  @override
  String get baidu_map => 'Baidu Maps';

  @override
  String get amap => 'Amap';

  @override
  String get google_map => 'Google Maps';

  @override
  String get tencent_map => 'Tencent Maps';

  @override
  String get image_format => 'The image format must be png, jpg, or jpeg';

  @override
  String get enable_location_service => 'Please allow the app to use your phone\"s location service.';

  @override
  String get enable_location_service_tips => 'Enable location permission allows more accurate recommendation of orders';

  @override
  String get enable_permission_not_now => 'Not now';

  @override
  String get enable_permission_goto_setting => 'Go to settings';

  @override
  String get failed_location_service => 'The acquisition of location information has encountered an error.';

  @override
  String get turn_on_location_service => 'Please turn on the location service on your phone.';

  @override
  String get no_install_map => 'You haven\"t install the map';

  @override
  String get camera => 'Camera';

  @override
  String get photo_album => 'Photo Album';

  @override
  String get new_version => ' is newly launched';

  @override
  String get invalid_mail => 'Invalid email';

  @override
  String get invalid_password => 'Invalid pasword';

  @override
  String get invalid_mobile => 'Invalid mobile number';

  @override
  String get invalid_auth_code => 'Invalid auth code';

  @override
  String get invalid_login => 'Failed to login,please again';

  @override
  String get grabbing => 'Grabbing';

  @override
  String get hour_ago => 'h ago';

  @override
  String get minute_ago => 'm ago';

  @override
  String get report_type => 'Report Type';

  @override
  String get fri => 'FRI';

  @override
  String get fui => 'FUI';

  @override
  String get oli => 'OLI';

  @override
  String get fat => 'FAT';

  @override
  String get cls => 'CLS';

  @override
  String get fri_cls => 'FRI+CLS';

  @override
  String get order_payment => 'Order Payment';

  @override
  String get order_refund => 'Order Refund';

  @override
  String get expend_withdrawal => 'Expend-WithDrawal';

  @override
  String get incoming_refund => 'Incoming-Refund';

  @override
  String get incoming_recharge => 'Incoming-Recharge';

  @override
  String get chat_not_member => 'You are no longer a member of the group and cannot send messages';

  @override
  String get admins => 'Admins';

  @override
  String get theme_title => 'Theme';

  @override
  String get theme_light => 'Light Mode';

  @override
  String get theme_dark => 'Dark Mode';

  @override
  String get theme_auto => 'Follow System';

  @override
  String get amount_total => 'Total';

  @override
  String get amount_available => 'Available';

  @override
  String get amount_blocked => 'Blocked';

  @override
  String get download => 'download';

  @override
  String get downloading => ' downloading';

  @override
  String get saved => 'Save completed';

  @override
  String get order_number => 'Order Number';

  @override
  String get order_detail_inspection_cost => 'Cost';

  @override
  String get delete_account => 'Delete Account';

  @override
  String get delete_account_confirm => 'All information will be deleted。\nAre you sure?';

  @override
  String get delete_account_result => 'Account has been deleted.';

  @override
  String get not_exist_account => 'Invalid account.';

  @override
  String get new_password => 'Please enter new password';

  @override
  String get supervisor => 'Supervisor';

  @override
  String get downloadFiles => 'Downloaded Files';

  @override
  String get home_search_hint_inspector => 'Search orders by area or product';

  @override
  String get home_search_hint_admin => 'Search orders by area or product';

  @override
  String get search_recent_history => 'Recently searched';

  @override
  String get assign => 'assign';

  @override
  String get assigned => 'assigned';

  @override
  String get approve => 'approve';

  @override
  String get assign_inspector => 'Assign Inspector';

  @override
  String get unassigned => 'Unassigned';

  @override
  String get general_all => 'All';

  @override
  String get general_date => 'Date';

  @override
  String get general_desc => 'Description';

  @override
  String get general_amount => 'Amount';

  @override
  String get assign_search_hint => 'Please input nickname/name/email/phone';

  @override
  String get assign_cancel_message => 'Confirm cancel the assignment';

  @override
  String get assign_inspect_times => 'Inspected times';

  @override
  String get assign_leave_message_batch => 'Leave message';

  @override
  String get assign_price_zero_tips => 'Inspection fee cannot be 0';

  @override
  String get assign_applied => 'Applied';

  @override
  String get is_auth_forbidden => 'Forbidden';

  @override
  String get apply_time => 'Applied at';

  @override
  String get assign_message => 'Message';

  @override
  String get chat_send_message => 'Send message';

  @override
  String get chat_send_order => 'Send order';

  @override
  String get chat_panel_album => 'Album';

  @override
  String get chat_panel_camera => 'Camera';

  @override
  String get chat_panel_file => 'File';

  @override
  String get chat_toolbar_custom_service => 'Custom service';

  @override
  String get chat_toolbar_submit_order => 'Submit an order';

  @override
  String get home_navigation => 'navigation';

  @override
  String get price_input_error_zero => 'Order price should between 0 and 1 million';

  @override
  String get filter_all => 'All filters';

  @override
  String get filter_heading_order_status => 'Order status';

  @override
  String get filter_heading_insp_date => 'Inspection date';

  @override
  String get filter_heading_order_date => 'Order date';

  @override
  String get filter_heading_area => 'Province/City/Area';

  @override
  String get filter_date_start => 'Start date';

  @override
  String get filter_date_end => 'End date';

  @override
  String get filter_date_today => 'Today';

  @override
  String get filter_date_tomorrow => 'Tomorrow';

  @override
  String get filter_date_2days_later => 'Within 2 days';

  @override
  String get filter_date_3days_later => 'Within 3 days';

  @override
  String get sort_by_order_date => 'Sort by order date';

  @override
  String get sort_by_insp_date => 'Sort by inspection date';

  @override
  String get sort_by_distance => 'Sort by distance';

  @override
  String get purchase_all_replies => 'All replies';

  @override
  String get purchase_replies_count => ' replies';

  @override
  String get purchase_no_more_replies => 'No more replies';

  @override
  String get purchase_save_draft_title => 'Do you need to save draft?';

  @override
  String get purchase_save_draft_choice => 'Save as draft';

  @override
  String get purchase_save_draft_quit => 'Quit';

  @override
  String get purchase_search_hint => 'Search purchase orders';

  @override
  String get purchase_reply_hint => 'Input your reply';

  @override
  String get purchase_reply_reason_hint => 'Reason why recommend';

  @override
  String get purchase_complaint_hint => 'More information helps to handle more quickly';

  @override
  String get purchase_reply_paid_hint => 'Input your valuable reply';

  @override
  String get purchase_edit => 'Edit post';

  @override
  String get purchase_publish => 'Publish purchase post';

  @override
  String get purchase_publish_product_label => 'Product name';

  @override
  String get purchase_publish_title_label => 'Titleitle';

  @override
  String get purchase_publish_quantity => 'Quantity';

  @override
  String get purchase_publish_content_label => 'Description';

  @override
  String get purchase_publish_product_hint => 'Please input product name';

  @override
  String get purchase_publish_title_hint => 'Please input model, area or other information';

  @override
  String get end_date => 'End date';

  @override
  String get purchase_area => 'Area';

  @override
  String get purchase_permission_author_only => 'Only author of post can see replies';

  @override
  String get purchase_publish_quantity_hint => 'Please input quantity';

  @override
  String get purchase_publish_content_hint => 'Please input description';

  @override
  String get purchase_publish_price => 'Purchase price';

  @override
  String get purchase_publish_choose_category => 'Choose category';

  @override
  String get purchase_publish_choose_category_hint => 'Please choose a category';

  @override
  String get purchase_paid_publish_switch => 'Paid content';

  @override
  String get purchase_paid_publish_set_price => 'Set price';

  @override
  String get purchase_detail_response_all => 'All';

  @override
  String get purchase_detail_response_author_only => 'Author only';

  @override
  String get purchase_detail_response_asc => 'Asc';

  @override
  String get purchase_detail_response_desc => 'Desc';

  @override
  String get purchase_detail_more_reply => 'Reply';

  @override
  String get purchase_detail_more_up => 'Up';

  @override
  String get purchase_detail_more_cancel_up => 'Cancel up';

  @override
  String get purchase_my_posts => 'My Posts';

  @override
  String get purchase_my_replies => 'My Replies';

  @override
  String get purchase_my_appeals => 'My Appeals';

  @override
  String get purchase_appeal_detail => 'Appeal details';

  @override
  String get purchase_appeal_submit => 'Submit appeal';

  @override
  String get purchase_appeal_cancel => 'Cancel appeal';

  @override
  String get purchase_appeal_approve => 'Agreed appeal';

  @override
  String get purchase_appeal_denied => 'Denied appeal';

  @override
  String get purchase_paid_content_owner_tips => 'Paid content';

  @override
  String get purchase_paid_content_tips => 'Paid content, view after pay';

  @override
  String get purchase_paid_content_paid_tips => 'Paid content is unlocked';

  @override
  String get purchase_review_leave => 'Leave a review';

  @override
  String get purchase_review_my_score => 'My review';

  @override
  String get purchase_my_replies_original_header => 'In post';

  @override
  String get purchase_publish_bounty_tips => 'Tips: bounty is optional, higher bounty will get more attention.';

  @override
  String get purchase_reply_to => 'Reply to';

  @override
  String get purchase_modify_bounty => 'Modify bounty';

  @override
  String get purchase_bounty_money => 'Bounty';

  @override
  String get purchase_evaluated_person => 'provider ratings';

  @override
  String get purchase_comment_paid_supplier => 'Supplier';

  @override
  String get purchase_comment_paid_contact => 'Contact';

  @override
  String get purchase_comment_paid_phone => 'Phone';

  @override
  String get purchase_comment_paid_email => 'Email';

  @override
  String get purchase_comment_paid_address => 'Factory address';

  @override
  String get purchase_comment_paid_other => 'Other';

  @override
  String get purchase_comment_paid_low_price => 'Low price';

  @override
  String get purchase_appeal_title => 'Refund request';

  @override
  String get purchase_appeal_reason => 'Please describe your reason';

  @override
  String get purchase_appeal_request_price => 'Refund: ';

  @override
  String get purchase_appeal_request_reason => 'Reason: ';

  @override
  String get purchase_post_status_draft => 'Draft';

  @override
  String get purchase_post_status_reviewing => 'Reviewing';

  @override
  String get purchase_post_status_published => 'Published';

  @override
  String get purchase_post_status_denied => 'Denied';

  @override
  String get purchase_post_publish => 'Publish post';

  @override
  String get purchase_complaint_type_leading => 'Please choose type';

  @override
  String get purchase_complaint_type_1 => 'Sexualization';

  @override
  String get purchase_complaint_type_2 => 'Spam';

  @override
  String get purchase_complaint_type_3 => 'Abuse or thretening violence';

  @override
  String get purchase_complaint_type_4 => 'Breaks rules or laws';

  @override
  String get purchase_complaint_type_5 => 'False information';

  @override
  String get purchase_complaint_type_6 => 'Copyright violation';

  @override
  String get purchase_complaint_type_7 => 'Other';

  @override
  String get shop_goods_detail_title => 'Product Details';

  @override
  String get mall_buy_immediate => 'Order Now';

  @override
  String get mall_goods_count => 'Qty';

  @override
  String get mall_confirm_pay => 'Confirm Pay';

  @override
  String get mall_order_confirm => 'Order Confirm';

  @override
  String get mall_submit_order => 'Submit Order';

  @override
  String get mall_goods_price => 'Goods price';

  @override
  String get mall_express_price => 'Express price';

  @override
  String get mall_price_total => 'Total: ';

  @override
  String get mall_payment => 'Payment';

  @override
  String get mall_payment_methods => 'Payment methods';

  @override
  String get mall_pay_succeed => 'Payment succeed';

  @override
  String get mall_check_order_detail => 'View order details';

  @override
  String get mall_order_remark => 'Remark';

  @override
  String get mall_order_remark_input => 'Input remark';

  @override
  String get purchase_detail_more_report => 'Report';

  @override
  String get purchase_reply_paid_content_tips => 'Tips: please input correct infomations, after reviewed by admin, your reply can be viewed by others.';

  @override
  String get public_ip_address => 'IP address: ';

  @override
  String get inspection_widget_suit_tips => 'Wear suit, or you can buy now if you don\"t have it';

  @override
  String get purchase_paid_content_appeal => 'appeal';

  @override
  String get report_success => 'Report success';

  @override
  String tabbar_tab_names(String names) {
    String _temp0 = intl.Intl.selectLogic(
      names,
      {
        'tab_home': 'Home',
        'tab_shortcut': 'Shortcut',
        'tab_message': 'Message',
        'tab_mine': 'Me',
        'tab_publish': '',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String order_status_desc(String status) {
    String _temp0 = intl.Intl.selectLogic(
      status,
      {
        'order_status_unknown': 'Unknown',
        'order_wait_pay': 'Pending payment',
        'order_cancelled': 'Cancelled',
        'order_cancelled_refund_pending': 'Cancelled, refund pending',
        'order_refund_pending': 'Refund pending',
        'order_refund_partial': 'Partial refund',
        'order_refund_denied': 'Refund denied',
        'order_wait_dispatch': 'Awaiting',
        'order_doing': 'Inspecting',
        'order_finished': 'Completed',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String pay_type(String type) {
    String _temp0 = intl.Intl.selectLogic(
      type,
      {
        'pay_rmb': 'RMB Account',
        'pay_usd': 'USD Account',
        'pay_zfb': 'AliPay',
        'pay_paypal': 'PayPal',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String purchase_complaint_type(String type) {
    String _temp0 = intl.Intl.selectLogic(
      type,
      {
        'type_1': 'Sexualization',
        'type_2': 'Spam',
        'type_3': 'Abuse or thretening violence',
        'type_4': 'Breaks rules or laws',
        'type_5': 'False information',
        'type_6': 'Copyright violation',
        'type_7': 'Other',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String get purchase_reply_paid_my_evaluate => '(My Review)';

  @override
  String get bind_now => 'Bind now';

  @override
  String get cancel_register => 'Cancel register';

  @override
  String register_and_bind_email0(Object email) {
    return 'Register and bind $email as primary login email.';
  }

  @override
  String register_and_bind_email1(Object email) {
    return 'You have registered with $email already, bind and continue.';
  }

  @override
  String get register_and_bind_email2 => 'Register and bind an email as your primary login email.';

  @override
  String get new_register_bind_title => 'Register & Bind';

  @override
  String get new_register_bind_field_account_tips => 'Bind email/phone number';

  @override
  String get register => 'Register';

  @override
  String get switch_account => 'Switch account';

  @override
  String get switch_account_confirm_tips => 'Are you sure to switch account?';

  @override
  String get password_must_have => 'Your password must contain:';

  @override
  String get password_must_have_1 => 'Length 8-32 characters';

  @override
  String get password_must_have_2 => '1 lowercase letter (a-z)';

  @override
  String get password_must_have_3 => '1 number';

  @override
  String get password_must_have_4 => '1 symbol (e.g., !@#\$%^&*)';

  @override
  String get password_login => 'Login Password';

  @override
  String get password_login_again => 'Re-enter Password';

  @override
  String get choose_account_to_login => 'Choose account to login quickly';

  @override
  String get finish => 'Finish';

  @override
  String get done => 'Done';

  @override
  String get account_apple => 'Apple Account';

  @override
  String get account_google => 'Google Account';

  @override
  String get account_wechat => 'Wechat Account';

  @override
  String get account_facebook => 'Facebook Account';

  @override
  String get third_account_unbind => 'Unbind';

  @override
  String get third_account_bind => 'Bind';

  @override
  String get confirm_unbind => 'Are you confirm to unbind?';

  @override
  String get inspection_requirement => 'Inspection Requirement';

  @override
  String get liveroom_entrance => 'Live Rooms';

  @override
  String get add_account => 'Add account';

  @override
  String get message_order => 'Orders';

  @override
  String get message_email => 'Email';

  @override
  String get message_wallet => 'Wallet';

  @override
  String get message_user => 'User info';

  @override
  String get salesman => 'Salesman';

  @override
  String get public_continue => 'Continue';

  @override
  String get camera_permission_tips => 'In order to facilitate you to upload pictures, files or photos when using the App, you need to allow the use of camera and storage permissions.';

  @override
  String get ai_category_inspector => 'inspector';

  @override
  String get ai_nothing_category => 'Nothing Inspection Category';

  @override
  String get ai_category_name => 'Inspection Category';

  @override
  String get ai_quantity => 'Quantity';

  @override
  String get ai_packaging => 'Packaging';

  @override
  String get ai_shipping_mark => 'Shipping Mark';

  @override
  String get ai_product_style => 'Product Style';

  @override
  String get ai_test => 'Test';

  @override
  String get ai_craftsmanship => 'Craftsmanship';

  @override
  String get ai_test_verification => 'Test Verification';

  @override
  String get ai_category_measure => 'Measure';

  @override
  String get ai_spare_parts => 'Spare Parts';

  @override
  String get ai_sampling_number => 'Sampling Number';

  @override
  String ai_input_range_number(Object range) {
    return 'Enter a number within $range';
  }

  @override
  String ai_enter_range_number(Object range) {
    return 'Please enter a number within $range';
  }

  @override
  String get ai_selected => 'Selected';

  @override
  String get ai_selected_status => 'Selected Status';

  @override
  String get ai_order_quantity => 'Order Quantity';

  @override
  String get ai_packaged_boxes_quantity => 'Quantity of Packaged Boxes (Finished Products)';

  @override
  String get ai_unpackaged_boxes_quantity => 'Quantity of Unpackaged Boxes (Finished Products)';

  @override
  String get ai_sample_from_packaged => 'Sample from Packaged';

  @override
  String get ai_sample_from_unpackaged => 'Sample from Unpackaged';

  @override
  String get ai_spare_parts_quantity => 'Quantity of Spare Parts';

  @override
  String get ai_sampling_packaging_number => 'Sampling Packaging Number';

  @override
  String get ai_sampling_packaging_number_record => 'Sampling Packaging Number Record';

  @override
  String get ai_sampling_packaging_number_list => 'Sampling Packaging Number List';

  @override
  String get ai_judgment => 'Judgment';

  @override
  String get ai_judgment_item => 'Judgment Item';

  @override
  String get ai_standard => 'Standard';

  @override
  String get ai_result => 'Result';

  @override
  String get ai_conclusion => 'Conclusion';

  @override
  String get ai_overall_conclusion => 'Overall Conclusion';

  @override
  String get ai_consistency => 'Consistency';

  @override
  String get ai_yes => 'Yes';

  @override
  String get ai_no => 'No';

  @override
  String get ai_remarks => 'Remarks';

  @override
  String get ai_numerical => 'Numerical';

  @override
  String get ai_recommended_test_items => 'Recommended Test Items';

  @override
  String get ai_test_item => 'Test Item';

  @override
  String get ai_add_all => 'Fast Add';

  @override
  String get ai_add_plus => '+ Add';

  @override
  String get ai_add => 'Add';

  @override
  String ai_confirm_delete(Object name) {
    return 'Are you sure you want to delete $name?';
  }

  @override
  String get ai_enter_test_item => 'Please enter a test item';

  @override
  String get ai_defect_record => 'Defect Record';

  @override
  String get ai_defect_photo => 'Defect Photo';

  @override
  String get ai_defect_description => 'Defect Description';

  @override
  String get ai_defect_level => 'Defect Level';

  @override
  String get ai_found_quantity => 'Quantity Found';

  @override
  String get ai_handling_method => 'Handling Method';

  @override
  String get ai_edit => 'Edit';

  @override
  String get ai_delete => 'Delete';

  @override
  String get ai_pick_out => 'Pick Out';

  @override
  String get ai_replace => 'Replace';

  @override
  String get ai_rework => 'Rework';

  @override
  String get ai_edit_description => 'Edit Description';

  @override
  String get ai_critical => 'Critical';

  @override
  String get ai_important => 'Important';

  @override
  String get ai_minor => 'Minor';

  @override
  String get ai_defect_list => 'Defect List';

  @override
  String get ai_test_level => 'Test Level';

  @override
  String get ai_sampling_sample => 'Sampling Sample';

  @override
  String get ai_sampling_level => 'Sampling Level';

  @override
  String get ai_additional_information => 'Additional Information';

  @override
  String get ai_inspection_record => 'Inspection Record';

  @override
  String get ai_sample_count => 'Sample Count';

  @override
  String get ai_maximum_allowable_value => 'Maximum Allowable Value';

  @override
  String get ai_test_item_name => 'Test Item Name';

  @override
  String get ai_test_result => 'Test Result';

  @override
  String get ai_basic_information => 'Basic Information';

  @override
  String get ai_new_test_item => 'New Test Item';

  @override
  String get ai_test_project => 'Test Project';

  @override
  String get ai_measurement_project => 'Measurement Project';

  @override
  String get ai_measure_need_num => 'Measure Need Num';

  @override
  String get ai_measurement_unit => 'Measurement Unit';

  @override
  String get ai_measurement_method => 'Measurement Method';

  @override
  String get ai_measurement_record => 'Measurement Record';

  @override
  String get ai_measured => 'Measured';

  @override
  String get ai_unit_of_measurement => 'Unit of Measurement';

  @override
  String get ai_measured_value => 'Measured Value';

  @override
  String get ai_product_number => 'Product Number';

  @override
  String get ai_number => 'Number';

  @override
  String get ai_new_measurement_item => 'New Measurement Item';

  @override
  String get ai_length_width_height => 'Length, Width, Height';

  @override
  String get ai_dimensions_length => 'Length';

  @override
  String get ai_dimensions_width => 'Width';

  @override
  String get ai_dimensions_height => 'Height';

  @override
  String get ai_length_width => 'Length, Width';

  @override
  String get ai_other => 'Other';

  @override
  String get ai_allowable_error => 'Allowable Error';

  @override
  String get ai_report_summary => 'Report Summary';

  @override
  String get ai_special_note => 'Special Note';

  @override
  String get ai_overall_conclusion_2 => 'Overall Conclusion';

  @override
  String get ai_summary => 'Summary';

  @override
  String get ai_category_name_table => 'Inspection Category';

  @override
  String get ai_compliance => 'Compliance Status';

  @override
  String get ai_remarks_2 => 'Remarks';

  @override
  String get ai_defect_summary => 'Defect Summary';

  @override
  String get ai_no_guidance_instructions => 'No Guidance Instructions Available';

  @override
  String get ai_no_standard_instructions => 'No Standard Instructions Available';

  @override
  String get ai_please_fill_in => 'Please Fill In';

  @override
  String ai_please_supplement_level_or_sample(Object level, Object sample) {
    return 'Please Supplement $level or $sample';
  }

  @override
  String get ai_please_add => 'Please Add';

  @override
  String get ai_please_input => 'Please Input';

  @override
  String get ai_please_select => 'Please Select';

  @override
  String ai_name_not_filled(Object name) {
    return '$name Not Filled';
  }

  @override
  String get ai_addition_successful => 'Addition Successful';

  @override
  String get ai_confirm_action => 'Confirm';

  @override
  String get ai_cancel_action => 'Cancel';

  @override
  String get ai_submit => 'Submit';

  @override
  String get ai_next_item => 'Next Item';

  @override
  String get ai_complete => 'Complete';

  @override
  String get ai_change_description => 'Change Description';

  @override
  String get ai_action_guidance_instructions => 'Guidance Instructions';

  @override
  String get ai_action_standard_instructions => 'Standard Instructions';

  @override
  String get ai_add_description => 'Add Description';

  @override
  String get ai_change_description_note => 'Attention: The following are the discovered defects. If modified, the historical data will also use the new description!';

  @override
  String get ai_packing_completion_rate => 'Packing Completion Rate';

  @override
  String get ai_unprocessed_quantity => 'Unprocessed Quantity';

  @override
  String get ai_sample_level_type_0 => 'I Level';

  @override
  String get ai_sample_level_type_1 => 'II Level';

  @override
  String get ai_sample_level_type_2 => 'III Level';

  @override
  String get ai_sample_level_type_s1 => 'S-1';

  @override
  String get ai_sample_level_type_s2 => 'S-2';

  @override
  String get ai_sample_level_type_s3 => 'S-3';

  @override
  String get ai_sample_level_type_s4 => 'S-4';

  @override
  String get ai_sample_level_type_nothing => 'Custom';

  @override
  String get ai_inspection_image => 'Picture';

  @override
  String get ai_photo_confirm => 'Confirm';

  @override
  String get ai_add_product_ask_save => 'Do you need to save this edit?';

  @override
  String get ai_add_product_save => 'Save';

  @override
  String get ai_add_product_edit_model => 'Edit Model';

  @override
  String get ai_add_product_model_name => 'Model Name';

  @override
  String get ai_add_product_input_model => 'Enter Model Name';

  @override
  String get ai_add_product_num => 'Quantity';

  @override
  String get ai_add_product_input_num => 'Enter Quantity';

  @override
  String get ai_add_product_unit => 'Unit';

  @override
  String get ai_add_product_ask_delete => 'Do you want to delete this model?';

  @override
  String get ai_add_product_edit_product => 'Edit Product';

  @override
  String get ai_add_product_product_name => 'Product Name';

  @override
  String get ai_add_product_model => 'Model';

  @override
  String get ai_add_product_input_product_name => 'Enter Product Name';

  @override
  String get ai_add_product_new_model => 'Add New Model';

  @override
  String get ai_add_product_ask_product => 'Do you want to delete this product and all its models?';

  @override
  String get ai_add_product_picture_lost => 'Missing Picture';

  @override
  String ai_add_product_lost_des(Object lostStr) {
    return 'Currently missing $lostStr. Please complete the information before inspection.';
  }

  @override
  String get ai_add_product_model_full => 'Full Product Model Name';

  @override
  String get ai_add_product_model_title => 'Product Model';

  @override
  String get ai_add_product_new => 'Add Product';

  @override
  String get ai_model_unit_piece => 'piece';

  @override
  String get ai_model_unit_only => 'piece';

  @override
  String get ai_model_unit_item => 'piece';

  @override
  String get ai_model_unit_pair => 'pair';

  @override
  String get ai_model_unit_set => 'set';

  @override
  String get ai_model_unit_dozen => 'dozen';

  @override
  String get ai_model_unit_roll => 'roll';

  @override
  String get ai_model_unit_vehicle => 'vehicle';

  @override
  String get ai_model_unit_head => 'head';

  @override
  String get ai_model_unit_bag => 'bag';

  @override
  String get ai_model_unit_box => 'box';

  @override
  String get ai_model_unit_pack => 'pack';

  @override
  String get ai_model_unit_yard => 'yard';

  @override
  String get ai_model_unit_meter => 'meter';

  @override
  String get ai_model_unit_kilogram => 'kilogram';

  @override
  String get ai_model_unit_metric_ton => 'metric ton';

  @override
  String get ai_model_unit_liter => 'liter';

  @override
  String get ai_model_unit_gallon => 'gallon';

  @override
  String get ai_model_unit_other => 'other';

  @override
  String get ai_default_config_des => 'There are currently no detection templates for the product. You can choose the template below or call (+86) to configure the template.';

  @override
  String get ai_default_config_category_all => 'Category (All)';

  @override
  String get ai_default_config_select_template => 'Please select a template';

  @override
  String get ai_default_config_template_selection => 'Template Selection';

  @override
  String get ai_default_config_search_template => 'Search Template';

  @override
  String get ai_default_config_classify => 'classify';

  @override
  String get ai_default_config_preview => 'Preview';

  @override
  String get ai_default_config_use => 'Use';

  @override
  String get ai_default_config_current_use_button => 'Apply to This Product';

  @override
  String get ai_default_config_more_use_button => 'Apply to More Products';

  @override
  String get ai_default_config_product_list => 'Product List';

  @override
  String get ai_default_config_use_warning => 'Note: 【Mod】Product template already loaded; 【Ops】Template already configured by operations. Loading a new template will overwrite previous data.';

  @override
  String get ai_default_config_tag_default => 'Ops';

  @override
  String get ai_default_config_tag_manual => 'Mod';

  @override
  String get ai_default_config_load_progress => 'Loading Progress';

  @override
  String ai_default_config_template_progress(Object name) {
    return 'Template loaded successfully for $name.';
  }

  @override
  String ai_default_config_template_fail_count(Object name) {
    return 'Failed for $name. Click to retry.';
  }

  @override
  String get ai_default_config_load => 'Load';

  @override
  String get ai_default_config_success => 'Success';

  @override
  String get ai_default_config_fail => 'Failure';

  @override
  String get ai_default_config_template => 'template';

  @override
  String get ai_add_model_count_warning => 'The quantity must be greater than 0';

  @override
  String get ai_default_config_product_edit => 'product edit';

  @override
  String get ai_wait => 'wait';

  @override
  String get ai_product_info => 'Product information';

  @override
  String get ai_product_category => 'Product classification';

  @override
  String get ai_product_unit => 'Product unit';

  @override
  String get ai_package_num_done => 'Number of packed cases';

  @override
  String get ai_product_full => 'Supplementary information';

  @override
  String get ai_product_full_tip => 'The product information in the order is incomplete. Please supplement it according to the actual product information in the inspection site';

  @override
  String get ai_each_box => 'Each box';

  @override
  String get ai_simple_count => 'Number of samples';

  @override
  String get ai_simple_level => 'Sampling standard';

  @override
  String get ai_simple_num => 'Sampling quantity';

  @override
  String get ai_simple_no => 'Sample box number';

  @override
  String get ai_simple_result => 'Result determination';

  @override
  String get ai_simple_project => 'Check item';

  @override
  String get ai_simple_project_manage => 'Management check item';

  @override
  String get ai_simple_project_edit => 'Edit check item';

  @override
  String get ai_simple_project_recmend => 'Intelligent recommendation';

  @override
  String get ai_simple_project_input => 'Fill in the inspection record';

  @override
  String get ai_simple_help => 'Help';

  @override
  String get ai_simple_project_record => 'Inspection record';

  @override
  String get ai_simple_require => 'Customer requirement';

  @override
  String get ai_simple_record => 'Records';

  @override
  String get ai_simple_dsec => 'Description';

  @override
  String get ai_simple_before => 'previous';

  @override
  String get ai_simple_add => 'Add a set';

  @override
  String get ai_simple_add_desc => 'Add a description to the photo';

  @override
  String get ai_simple_add_citations => 'citations';

  @override
  String get ai_no_more => 'No more data';

  @override
  String get ai_wrong_tip => 'The quantity cannot be greater than the total';

  @override
  String get ai_defect_records => 'Defect record';

  @override
  String get ai_check_require => 'Sampling requirement';

  @override
  String get ai_find_defect => 'Find defect';

  @override
  String get ai_defect_question => 'Defect problem';

  @override
  String get ai_modify_level => 'Modified sampling grade';

  @override
  String get ai_defect_quick => 'Add process defects quickly';

  @override
  String get ai_defect_self => 'Custom defect name';

  @override
  String get ai_defect_record_list => 'Defect record list';

  @override
  String get ai_measure_require => 'Measurement requirement';

  @override
  String get ai_measurement_item => 'Measurement item';

  @override
  String get ai_measurement_error => 'error';

  @override
  String get ai_measurement_standard => 'Measuring standard';

  @override
  String get ai_measurement_value_standard => 'Standard value';

  @override
  String get ai_measurement_camera => 'Photograph';

  @override
  String get ai_measurement_add => 'Add measurement standards quickly';

  @override
  String get ai_product_first => 'cover';

  @override
  String get ai_product_report => 'Generate report';

  @override
  String get ai_product_report_tip => 'Please select the first product image';

  @override
  String get ai_product_report_special => 'Please enter anything that requires special attention';

  @override
  String get ai_product_report_sign => 'signature';

  @override
  String get ai_product_report_sign_done => 'Signature complete';

  @override
  String get ai_defect_names => 'Defect name';

  @override
  String get ai_input_tip => 'Please enter name';

  @override
  String get ai_add_measure_tip => 'Please add the measurement standard first';

  @override
  String get ai_wrong_num => 'Quantity error';

  @override
  String get ai_wrong_name => 'Please enter the product name';

  @override
  String get ai_wrong_sample_num => 'Cannot be greater than the number of samples';

  @override
  String get ai_per_box => 'Quantity per carton';

  @override
  String get ai_wrong_sample_num_cal => 'Packed sampling + unpacked sampling must equal the number of samples sampled';

  @override
  String get ai_sure_delete => 'Are you sure to delete it?';

  @override
  String get ai_choose_tip => 'Please select the defect handling method and the number';

  @override
  String get ai_weight => 'weight';

  @override
  String get sampling_plan => 'Sampling plan';

  @override
  String get single => 'Single';

  @override
  String get normal => 'Normal';

  @override
  String get summarize => 'Summarize';

  @override
  String get po_number => 'PO Number';

  @override
  String get product_quantity => 'Product Quantity';

  @override
  String get customer_name => 'Customer Name';

  @override
  String get supplier_name => 'Supplier Name';

  @override
  String get inspection_date => 'Inspection Date';

  @override
  String get arrival_time => 'Arrival Time';

  @override
  String get completion_time => 'Completion Time';

  @override
  String get inspection_address => 'Inspection Address';

  @override
  String get inspector => 'Inspector';

  @override
  String get inspection_report_note => 'This inspection report is for reference only. Final approval is subject to customer confirmation.';

  @override
  String get remark_toast => 'Please fill in the remarks first';

  @override
  String get process_appearance_judgment => 'Process appearance judgment';

  @override
  String get test_validation_judgment => 'Test validation judgment';

  @override
  String check_save(Object name) {
    return 'Are you sure you want to save $name?';
  }

  @override
  String get select_template_config_tip => 'If you need to configure the inspection template, please contact your order follower';
}
