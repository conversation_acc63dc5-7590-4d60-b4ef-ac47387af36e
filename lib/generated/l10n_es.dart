import 'package:intl/intl.dart' as intl;

import 'l10n.dart';

// ignore_for_file: type=lint

/// The translations for Spanish Castilian (`es`).
class SEs extends S {
  SEs([String locale = 'es']) : super(locale);

  @override
  String get app_name => 'Inspección en línea';

  @override
  String get search => 'Buscar';

  @override
  String get shortcut_tab_name => 'Navegación rápida';

  @override
  String get loading => 'Cargando...';

  @override
  String get nomore => 'No hay más contenido';

  @override
  String get confirm => 'Confirmar';

  @override
  String get more_replies => 'Más respuestas';

  @override
  String get purchase_paid_publish_information_title => 'Esto es lo que los clientes pagan por ver';

  @override
  String get purchase_set_fee => 'Establecer tarifas de visualización';

  @override
  String get purchase_comment_paid_supplier_hint => 'Por favor ingrese el nombre del proveedor';

  @override
  String get purchase_comment_paid_contact_hint => 'Por favor ingrese el nombre del contacto';

  @override
  String get purchase_comment_paid_phone_hint => 'Por favor ingrese el número de teléfono del contacto';

  @override
  String get purchase_comment_paid_email_hint => 'Por favor ingrese el correo electrónico del contacto';

  @override
  String get purchase_comment_paid_address_hint => 'Por favor ingrese la dirección de la fábrica';

  @override
  String get purchase_comment_paid_other_hint => 'Otra información (opcional)';

  @override
  String get purchase_comment_paid_low_price_hint => 'Por favor ingrese el precio base del producto (opcional)';

  @override
  String get purchase_reply_paid_title => 'Respuesta pagada';

  @override
  String get purchase_reply_paid_desc => '(Información del proveedor y precio de referencia del producto)';

  @override
  String purchase_reply_paid_evaluated_people(Object people_num) {
    return '($people_num personas han evaluado)';
  }

  @override
  String get language_setting => 'Configuración de idioma';

  @override
  String get public_and => 'y';

  @override
  String get public_publish => 'Publicar';

  @override
  String get public_distance => 'Distancia';

  @override
  String get public_deny => 'Rechazar';

  @override
  String get public_see_more => 'Ver más';

  @override
  String get general_select => 'Seleccionar';

  @override
  String get language_page_title => 'Multiidioma';

  @override
  String get language_chinese => 'Chino';

  @override
  String get language_english => 'Inglés';

  @override
  String get public_seconds_ago => 'hace segundos';

  @override
  String get public_minutes_ago => 'hace minutos';

  @override
  String get public_hours_ago => 'hace horas';

  @override
  String get public_status_applied => 'Aplicado';

  @override
  String get public_status_refused => 'Rechazado';

  @override
  String get public_status_approved => 'Aprobado';

  @override
  String get public_status_canceled => 'Cancelado';

  @override
  String get public_app_name => 'Inspector';

  @override
  String get public_send => 'Enviar';

  @override
  String get public_ok => 'OK';

  @override
  String get public_price => 'Precio';

  @override
  String get public_cancel => 'Cancelar';

  @override
  String get public_manage => 'Gestionar';

  @override
  String get public_finish => 'Finalizar';

  @override
  String get public_reset => 'Restablecer';

  @override
  String get public_leave_message => 'Dejar mensaje';

  @override
  String get public_share => 'Compartir';

  @override
  String get login_submit => 'Iniciar sesión';

  @override
  String get login_mobile_login => 'Iniciar sesión con teléfono';

  @override
  String get login_mobile_tips => 'Por favor ingrese el número de teléfono';

  @override
  String get login_email_login => 'Iniciar sesión con correo electrónico';

  @override
  String get login_email_tips => 'Por favor ingrese el correo electrónico';

  @override
  String get registry_email_phone_tips => 'Por favor ingrese el correo electrónico o número de teléfono';

  @override
  String get login_verify_tips => 'Ingrese el código de verificación';

  @override
  String get login_password_tips => 'Ingrese la contraseña';

  @override
  String get login_password_login => 'Iniciar sesión con contraseña';

  @override
  String get login_verify_login => 'Iniciar sesión con código de verificación';

  @override
  String get login_register => 'Registrarse';

  @override
  String get login_take_code => 'Obtener código de verificación';

  @override
  String get login_forget_password => 'Olvidé la contraseña';

  @override
  String get login_agreement => 'Acepto el \'Acuerdo de inspector.Itd\'';

  @override
  String get login_area_selected => 'Seleccionar región del país';

  @override
  String get tab_home => 'Inicio';

  @override
  String get tab_order => 'Pedidos';

  @override
  String get tab_shortcut => 'Acceso directo';

  @override
  String get tab_purchase => 'Compras';

  @override
  String get tab_message => 'Mensajes';

  @override
  String get tab_mine => 'Mi cuenta';

  @override
  String get supplement_title => 'Por favor complete primero su información personal';

  @override
  String get supplement_next => 'Completar';

  @override
  String get home_title => 'Plaza de inspección';

  @override
  String get home_record => 'Registro de solicitudes';

  @override
  String get home_newest => 'Inspecciones más recientes';

  @override
  String get home_nearest => 'Inspecciones cercanas';

  @override
  String home_recommend(Object money) {
    return 'Recompensa de recomendación RMB $money';
  }

  @override
  String get home_sampling => 'Inspección de muestreo';

  @override
  String get home_word => 'Informe WORD';

  @override
  String home_unit(Object day, Object people) {
    return '$people personas/$day días';
  }

  @override
  String get home_product_tip => 'Producto:';

  @override
  String get home_person_apply => 'personas solicitaron';

  @override
  String get home_know_tip => 'Guía de inspección';

  @override
  String get home_inspection_tip => 'La tarifa de inspección predeterminada es el precio acordado, se puede modificar, una tarifa baja puede tener prioridad para la asignación';

  @override
  String get home_reviewed => 'He leído y cumpliré';

  @override
  String get home_apply => 'Solicitar';

  @override
  String get home_apply_price => 'Por favor ingrese el monto ¥';

  @override
  String get home_apply_check => 'Por favor revise la guía de inspección';

  @override
  String get home_apply_tips => 'Usted no es un inspector, si tiene más de 1 año de experiencia en inspección de comercio exterior, proporcione pruebas de calificación relevantes';

  @override
  String get home_complete_profile_tips => 'Completar el perfil del inspector y otra información relevante puede aumentar la tasa de aprobación de la solicitud de inspección';

  @override
  String get home_apply_sure => 'Enviar para revisión';

  @override
  String get home_complete_profile_sure => 'Completar';

  @override
  String get home_apply_cancel => 'Cancelar solicitud';

  @override
  String get home_update => 'Modificar';

  @override
  String get home_navi => 'Navegación';

  @override
  String get mine_unauth => 'No verificado';

  @override
  String get mine_checking => 'En revisión';

  @override
  String get mine_check_failed => 'Revisión fallida';

  @override
  String get mine_vip_level => 'Nivel VIP';

  @override
  String get mine_credit_quota => 'Límite de crédito';

  @override
  String get mine_authed => 'Modificar información de verificación';

  @override
  String get mine_authed_inspector => 'Modificar información del inspector';

  @override
  String get mine_amount => 'Saldo de la cuenta';

  @override
  String get mine_cash => 'Recargar/Retirar';

  @override
  String get mine_order => 'Mis pedidos';

  @override
  String get mine_purchase => 'Mis compras';

  @override
  String get mine_check => 'Mis inspecciones';

  @override
  String get mine_address => 'Libreta de direcciones (Proveedor)';

  @override
  String get mine_recommend => 'Recomendar';

  @override
  String get mine_setting => 'Configuración';

  @override
  String get mine_header_inspect => 'Gestión de inspección';

  @override
  String get mine_header_purchase => 'Gestión de compras';

  @override
  String get mine_header_other => 'Otros';

  @override
  String get mine_inspect_mine => 'Mis inspecciones';

  @override
  String get mine_inspect_order => 'Gestión de pedidos';

  @override
  String get mine_inspect_history => 'Historial de solicitudes';

  @override
  String get mine_purchase_mine => 'Mis compras';

  @override
  String get mine_purchase_reply => 'Historial de respuestas';

  @override
  String get mine_purchase_appeal => 'Gestión de apelaciones';

  @override
  String get mine_other_recommend => 'Recomendar';

  @override
  String get mine_other_address => 'Libreta de direcciones (Proveedor)';

  @override
  String get mine_other_settings => 'Configuración';

  @override
  String get profile_title => 'Información personal';

  @override
  String get profile_avatar => 'Avatar';

  @override
  String get profile_name => 'Apodo';

  @override
  String get profile_mobile => 'Número de teléfono';

  @override
  String get profile_country => 'País';

  @override
  String get profile_real_name => 'Nombre real';

  @override
  String get profile_city => 'Ciudad';

  @override
  String get profile_email => 'Correo electrónico';

  @override
  String get profile_wechat => 'WeChat';

  @override
  String get profile_bind_manage => 'Gestión de vinculación de cuentas';

  @override
  String get profile_info_failed => 'Fallo en la actualización de información';

  @override
  String get apply_title => 'Solicitud de calificación de inspector';

  @override
  String get apply_nick => 'Apodo';

  @override
  String get apply_sex => 'Género';

  @override
  String get apply_birthday => 'Fecha de nacimiento';

  @override
  String get apply_education => 'Educación';

  @override
  String get apply_address => 'Dirección de residencia';

  @override
  String get apply_price => 'Tarifa mínima de inspección';

  @override
  String get apply_shebao => 'Seguro social';

  @override
  String get apply_id_card => 'Número de identificación';

  @override
  String get apply_file => 'Editar currículum';

  @override
  String get apply_file_tip => 'Por favor ingrese su información básica y experiencia';

  @override
  String get apply_upload_file => 'Subir currículum';

  @override
  String get apply_upload_file_failed => 'Fallo al subir el currículum';

  @override
  String get apply_upload_card => 'Subir foto de identificación';

  @override
  String get apply_card_front => 'Foto frontal (lado de la cara)';

  @override
  String get apply_card_back => 'Foto trasera (lado del emblema nacional)';

  @override
  String get apply_submit => 'Enviar';

  @override
  String get apply_enter => 'Por favor ingrese';

  @override
  String get apply_next_tip => 'Por favor confirme que la información está completa antes de enviar';

  @override
  String get apply_auth_failed => 'Verificación de identidad fallida, por favor suba la foto correcta';

  @override
  String get apply_checking => 'Identidad en revisión';

  @override
  String get apply_check_success => 'Revisión de identidad aprobada';

  @override
  String get apply_check_failed => 'Revisión fallida, por favor modifique el contenido y vuelva a enviar';

  @override
  String get order_title => 'Mis pedidos';

  @override
  String get order_input => 'Mis inspecciones';

  @override
  String get order_output => 'Publicar pedido';

  @override
  String get order_all => 'Todos';

  @override
  String get order_wait_pay => 'Pendiente de pago';

  @override
  String get order_cancelled => 'Cancelado';

  @override
  String get order_status => 'Estado del pedido';

  @override
  String get order_status_unknown => 'Desconocido';

  @override
  String get order_refund_pending => 'Reembolso pendiente de revisión';

  @override
  String get order_cancelled_refund_pending => 'Cancelado, reembolso pendiente de revisión';

  @override
  String get order_refund_partial => 'Reembolso parcial';

  @override
  String get order_refund_denied => 'Reembolso denegado';

  @override
  String get order_wait_dispatch => 'Pendiente de asignación';

  @override
  String get order_ready_inspect => 'Listo para inspección';

  @override
  String get order_need_pay => 'Pagar';

  @override
  String get order_wait => 'Listo para inspección';

  @override
  String get order_confirm => 'Confirmar asignación';

  @override
  String get order_doing => 'En inspección';

  @override
  String get order_comment => 'Pendiente de evaluación';

  @override
  String get order_finished => 'Completado';

  @override
  String get order_goods_info => 'Información del producto';

  @override
  String get order_goods_name => 'Nombre del producto';

  @override
  String get order_goods_model => 'Modelo del producto';

  @override
  String get order_goods_count => 'Cantidad';

  @override
  String get order_goods_unit => 'Unidad';

  @override
  String get order_order_time => 'Hora del pedido';

  @override
  String get order_order_amount => 'Monto del pedido';

  @override
  String get order_detail_title => 'Detalles del pedido';

  @override
  String get order_applying => 'Aplicado';

  @override
  String get order_apply_expired => 'Expirado';

  @override
  String get order_apply_dispatched => 'Asignado';

  @override
  String get order_create_time => 'Hora de creación del pedido';

  @override
  String get order_look => 'Ver informe de inspección';

  @override
  String get order_report_next => 'Enviar informe de inspección';

  @override
  String get order_detail_inspection_info => 'Información de inspección';

  @override
  String get order_inspection_status_unpaid => 'No pagado';

  @override
  String get order_inspection_status_returned => 'Devuelto';

  @override
  String get order_inspection_status_waiting_start => 'Esperando inicio';

  @override
  String get order_detail_related_info => 'Información de subpedidos relacionados';

  @override
  String get order_detail_inspection_product => 'Nombre del producto';

  @override
  String get order_detail_inspection_time => 'Hora de inspección';

  @override
  String get order_detail_inspection_city => 'Ciudad de inspección';

  @override
  String get order_detail_inspection_factory => 'Fábrica de inspección';

  @override
  String get order_detail_inspection_address => 'Dirección de inspección';

  @override
  String get order_detail_inspection_person => 'Contacto';

  @override
  String get order_detail_inspection_phone => 'Teléfono de contacto';

  @override
  String get order_detail_inspection_email => 'Correo electrónico de contacto';

  @override
  String get order_detail_inspection_amount => 'Información de precio';

  @override
  String get order_detail_inspection_sample => 'Muestra';

  @override
  String get order_detail_inspection_standard => 'Nivel de muestreo';

  @override
  String get order_detail_inspection_critical => 'AQL';

  @override
  String get order_detail_inspection_major => 'Mayor';

  @override
  String get order_detail_inspection_minor => 'Menor';

  @override
  String get order_detail_inspection_day => 'Días de inspección';

  @override
  String get order_detail_inspection_number => 'Número de inspectores';

  @override
  String get order_detail_inspection_price => 'Precio de inspección';

  @override
  String get order_detail_inspection_price_apply => 'Precio de solicitud';

  @override
  String get order_detail_inspection_template => 'Plantilla de inspección';

  @override
  String get order_detail_inspection_file => 'Adjunto';

  @override
  String get order_detail_inspection_image => 'Imagen';

  @override
  String get order_detail_inspection_type => 'Tipo de inspección';

  @override
  String get order_tips => 'Muestras y notas';

  @override
  String get order_apply => 'Solicitar inspección';

  @override
  String get order_cancel => 'Cancelar';

  @override
  String get order_canceled => 'Cancelado';

  @override
  String get order_dispatch_accept => 'Aceptar';

  @override
  String get order_dispatch_refuse => 'Rechazar';

  @override
  String get order_inspection => 'Iniciar inspección';

  @override
  String get order_sure_cancel => '¿Seguro que desea cancelar el pedido?';

  @override
  String get order_sure_refuse => '¿Seguro que desea rechazar este pedido?';

  @override
  String get order_sure_confirm => '¿Seguro que desea aceptar este pedido?';

  @override
  String get publish_title => 'Publicar pedido';

  @override
  String get publish_edit_title => 'Editar pedido';

  @override
  String get publish_welcome => 'Bienvenido a realizar pedidos, precios libres, comunicación libre con los inspectores';

  @override
  String get publish_sampling => 'Inspección de muestreo';

  @override
  String get publish_sampling_content => 'Aplicable a productos 100% terminados, con al menos el 80% de los productos ya empaquetados, listos para envío. Realizamos una inspección de muestreo aleatorio basada en el plan de muestreo internacional ANSI/ASQZ1.4(MIL-STD-105E) y sus requisitos especiales. En el informe de inspección de muestreo, reflejaremos completamente la cantidad de productos terminados, las condiciones de empaque y si cumplen con los requisitos de AQL (Nivel de Calidad Aceptable), dándole una comprensión completa de la calidad del lote completo de productos antes del envío, evitando cualquier riesgo en su pedido';

  @override
  String get publish_point => 'Puntos de inspección';

  @override
  String get publish_sampling_point => '● Verificación de datos/muestras del cliente\n● Verificación de cantidad completada\n● Verificación de tamaño, estilo, color del producto\n● Inspección de apariencia y mano de obra\n● Prueba de función y seguridad del producto\n● Inspección de etiquetas de envío\n● Integridad del embalaje\n● Detalles específicos de embalaje\n● Requisitos especiales del cliente';

  @override
  String get publish_all => 'Inspección completa';

  @override
  String get publish_all_content => 'La inspección completa se puede realizar antes o después del embalaje. Según los requisitos del cliente, se inspecciona cada producto en cuanto a apariencia, tamaño, mano de obra, función y seguridad, separando los productos buenos de los defectuosos, e informando los resultados de la inspección al cliente de manera oportuna';

  @override
  String get publish_online => 'Inspección en línea';

  @override
  String get publish_online_content => 'La inspección en línea se realiza durante el proceso de producción o antes de que toda la producción esté terminada y empaquetada. Puede ayudarle a confirmar que la calidad, función, apariencia y otros elementos del producto se mantienen consistentes con sus especificaciones durante todo el proceso de producción, también ayuda a descubrir cualquier disconformidad temprano, reduciendo así el riesgo de retrasos en la entrega por parte de la fábrica';

  @override
  String get publish_online_point => '● Seguimiento de la situación de producción\n● Evaluación de la línea de producción y confirmación del progreso de producción\n● Inspección de muestras de productos semiacabados y terminados\n● Verificación de información de embalaje y materiales de embalaje\n● Mejora de productos defectuosos\n● Evaluación del tiempo de entrega';

  @override
  String get publish_factory => 'Auditoría de fábrica';

  @override
  String get publish_factory_content => 'La auditoría de fábrica utiliza principalmente un método de evaluación objetivo, evaluando y auditando la fábrica cuantitativamente según estándares o criterios predeterminados. Se genera un informe de evaluación basado en la puntuación in situ y los resultados de la auditoría integral de la fábrica, como base para que el cliente determine si la fábrica puede ser un proveedor calificado';

  @override
  String get publish_factory_point_title => 'Contenido de la auditoría';

  @override
  String get publish_factory_point => '● Visión general de la fábrica (información básica)\n● Estructura organizativa\n● Proceso de producción\n● Capacidad de producción\n● Capacidad de investigación y desarrollo\n● Equipos y instalaciones';

  @override
  String get publish_factory_review => 'Evaluación integral';

  @override
  String get publish_factory_review_content => '● Para cada ítem de auditoría, considere su importancia relativa, asigne diferentes puntajes, luego genere una tabla de calificación de calificación basada en el formulario de encuesta de auditoría y los datos de la encuesta de campo';

  @override
  String get publish_watch => 'Supervisión de carga';

  @override
  String get publish_watch_content => 'La supervisión de carga del contenedor incluye principalmente la evaluación del estado del contenedor, la verificación de la información del producto, el recuento de cajas cargadas, la inspección de la información de embalaje y la supervisión de todo el proceso de carga. Para reducir el alto riesgo de sustitución de mercancías después de la carga, el inspector supervisa en el sitio de carga para garantizar que los productos que usted paga se carguen de manera segura';

  @override
  String get publish_watch_point => '● Registrar el número del contenedor y el número del camión\n● Inspeccionar el contenedor en busca de daños, humedad y olores extraños, tomar fotos del contenedor vacío\n● Verificar la cantidad de cajas a cargar y el estado del embalaje exterior, comprobar aleatoriamente algunas cajas para confirmar los productos realmente cargados\n● Supervisar el proceso de carga para asegurar un daño mínimo y un uso máximo del espacio\n● Tomar fotos de la condición de cierre del contenedor, número de precinto del contenedor y lista de empaque, registrar la hora de salida del contenedor';

  @override
  String get publish_watch_inspection => 'Inspección + Supervisión de carga';

  @override
  String get publish_watch_inspection_content => 'Para garantizar la calidad final y la integridad del producto, antes de estar listo para el envío, tomamos muestras aleatorias de los productos terminados para la inspección de muestreo basada en el plan de muestreo internacional, y verificamos los datos proporcionados por el cliente, además de supervisar todo el proceso de carga del contenedor';

  @override
  String get publish_watch_inspection_point => '● Antes de que llegue el contenedor, verificar los datos/muestras del cliente, realizar inspección de muestreo sobre la apariencia, mano de obra, función y seguridad del producto, así como el embalaje, etiquetas de envío, etc.\n● Comunicarse inmediatamente con la fábrica si se encuentran productos defectuosos, para reemplazo o reproceso\n● Inspeccionar el contenedor en busca de daños, humedad y olores extraños, tomar fotos del contenedor vacío\n● Supervisar el proceso de carga para asegurar un daño mínimo y un uso máximo del espacio\n● Tomar fotos de la condición de cierre del contenedor, número de precinto del contenedor y lista de empaque, registrar la hora de salida del contenedor';

  @override
  String get publish_next => 'Siguiente';

  @override
  String get publish_inspection_time => 'Tiempo de inspección';

  @override
  String get publish_inspection_time_selected => 'Seleccionar tiempo de inspección';

  @override
  String get publish_inspection_time_tip => 'Por favor seleccione';

  @override
  String get publish_inspection_people => 'Número de inspectores';

  @override
  String get publish_people => 'personas';

  @override
  String get publish_day => 'días';

  @override
  String get publish_inspection_factory => 'Fábrica de inspección';

  @override
  String get publish_factory_tips => 'Ingrese la fábrica de inspección';

  @override
  String get publish_address_book => 'Libreta de direcciones';

  @override
  String get publish_goods_name => 'Nombre del producto';

  @override
  String get publish_name_tips => 'Ingrese uno o dos nombres representativos si hay varios productos';

  @override
  String get publish_po_tips => 'Por favor ingrese el número de P.O';

  @override
  String get publish_file_tips => 'Subir adjunto';

  @override
  String get publish_camera => 'Subir foto';

  @override
  String get publish_file => 'Subir archivo';

  @override
  String get publish_purchase => 'Publicar pedido de compra';

  @override
  String get publish_inspection => 'Publicar pedido de inspección';

  @override
  String get publish_factory_tip => 'Por favor seleccione primero la información de dirección de inspección';

  @override
  String get publish_attention => 'Notas';

  @override
  String get publish_attention_tips => 'Por favor ingrese qué problemas necesita que el inspector preste especial atención';

  @override
  String get publish_stand_price => 'Precio fijo';

  @override
  String get publish_click_price => 'Cambiar';

  @override
  String get publish_vip_price => 'Precio VIP';

  @override
  String get publish_vip_tips => 'Proporciona servicio de seguimiento manual durante todo el proceso';

  @override
  String get publish_total => 'Total';

  @override
  String get publish_submit => 'Enviar';

  @override
  String get publish_only_price_failed => 'Sin permiso de precio fijo';

  @override
  String get publish_price_tip => 'Por favor seleccione el precio';

  @override
  String get publish_date_tips => 'Por favor seleccione la fecha';

  @override
  String get date_title => 'Fecha de inspección';

  @override
  String get date_save => 'Guardar';

  @override
  String get address_title => 'Editar información de fábrica';

  @override
  String get address_auto_tips => 'Por favor pegue o ingrese el texto, haga clic en \"Identificar\" para identificar automáticamente el nombre de la fábrica, nombre y teléfono, dirección, etc.';

  @override
  String get address_paste => 'Pegar';

  @override
  String get address_ocr => 'Identificar';

  @override
  String get address_name => 'Nombre de la fábrica';

  @override
  String get address_name_tip => 'Por favor ingrese la información de la fábrica de inspección';

  @override
  String get address_person => 'Contacto';

  @override
  String get address_person_tip => 'Por favor ingrese el contacto';

  @override
  String get address_mobile => 'Número de teléfono móvil';

  @override
  String get address_mobile_tip => 'Por favor ingrese el número de teléfono móvil';

  @override
  String get address_email => 'Correo electrónico';

  @override
  String get address_email_tip => 'Por favor ingrese la dirección de correo electrónico';

  @override
  String get address_area => 'Provincia-Ciudad-Distrito';

  @override
  String get address_area_tip => 'Por favor seleccione Provincia-Ciudad-Distrito  〉';

  @override
  String get address_detail => 'Dirección detallada';

  @override
  String get address_detail_tip => 'Ingrese información de calle, número, etc.';

  @override
  String get address_location => 'Ubicación';

  @override
  String get address_save_tip => 'Guardar en libreta de direcciones';

  @override
  String get address_clear => 'Borrar';

  @override
  String get address_submit => 'Enviar';

  @override
  String get address_recent => 'Direcciones usadas recientemente';

  @override
  String get address_more => 'Más direcciones';

  @override
  String get address_list_title => 'Gestión de direcciones';

  @override
  String get address_insert => 'Agregar dirección';

  @override
  String get address_delete => 'Eliminar';

  @override
  String get address_delete_result => 'Eliminación fallida';

  @override
  String get address_edit => 'Editar';

  @override
  String get address_delete_tips => '¿Confirmar eliminar dirección?';

  @override
  String get address_detected_paste => 'Se detectó información de dirección, ¿usar esta dirección?';

  @override
  String get pay_title => 'Pagar pedido';

  @override
  String get pay_time => 'Tiempo restante para el pago';

  @override
  String get pay_paypal => 'PayPal';

  @override
  String get pay_zfb => 'Alipay';

  @override
  String get pay_usd => 'Cuenta USD';

  @override
  String get pay_rmb => 'Cuenta RMB';

  @override
  String get pay_pay => 'Pagar';

  @override
  String get pay_result_success => 'Pago exitoso';

  @override
  String get pay_result_success_wait => 'Pago exitoso, esperando procesamiento del pedido.';

  @override
  String get pay_result_failed => 'Pago fallido';

  @override
  String get pay_keep => 'Se puede contabilizar';

  @override
  String get check_title => 'Informe de inspección';

  @override
  String get check_picture => 'Fotos de inspección';

  @override
  String get check_file => 'Carta de compromiso de integridad';

  @override
  String get check_report => 'Carta de compromiso de integridad + Informe manuscrito';

  @override
  String get check_draft => 'Borrador de informe';

  @override
  String get check_template => 'Plantilla de informe';

  @override
  String get check_submit => 'Enviar';

  @override
  String get check_hint => 'Por favor ingrese la descripción de la imagen';

  @override
  String get check_checking => 'Informe en revisión';

  @override
  String get check_check_success => 'Revisión del informe aprobada';

  @override
  String get check_check_failed => 'Revisión del informe fallida, por favor modifique el contenido y vuelva a enviar';

  @override
  String get review_title => 'Evaluación del servicio';

  @override
  String get review_next => 'Evaluar';

  @override
  String get contact_bnt => 'Contactar ahora';

  @override
  String get review_score => 'Nivel de servicio';

  @override
  String get review_score1 => 'Decepcionante';

  @override
  String get review_score2 => 'Insatisfactorio';

  @override
  String get review_score3 => 'Normal';

  @override
  String get review_score4 => 'Satisfactorio';

  @override
  String get review_score5 => 'Excelente';

  @override
  String get review_tips => 'Evalúe desde múltiples ángulos para ayudarnos a entender mejor las capacidades de trabajo del inspector';

  @override
  String get review_picture => 'Imagen';

  @override
  String get review_submit => 'Enviar';

  @override
  String get setting_title => 'Configuración';

  @override
  String get setting_address => 'Gestión de direcciones';

  @override
  String get setting_clear_cache => 'Limpiar caché';

  @override
  String get setting_clear_success => 'Limpieza exitosa';

  @override
  String get setting_about_us => 'Acerca de nosotros';

  @override
  String get setting_receive_msg => 'Recibir notificaciones de mensajes';

  @override
  String get setting_version => 'Número de versión';

  @override
  String get setting_check_update => 'Buscar actualizaciones';

  @override
  String get setting_login_out => 'Cerrar sesión';

  @override
  String get setting_login_out_tips => '¿Está seguro de que desea cerrar sesión?';

  @override
  String get setting_delete_account_tips => '¿Está seguro de que desea eliminar la cuenta?';

  @override
  String get setting_policy_title => 'Aviso de autorización de política de privacidad';

  @override
  String get setting_policy_sub_title => 'Antes de continuar, por favor lea y acepte';

  @override
  String get setting_privacy_policy => 'Política de privacidad';

  @override
  String get setting_user_agreement => 'Acuerdo de usuario';

  @override
  String get setting_privacy_content => 'Bienvenido a la aplicación de Inspección en línea\nValoramos mucho su privacidad y la protección de su información personal. Durante su uso de esta aplicación, recopilaremos y utilizaremos parte de su información personal\n1. Después de que acepte la política de privacidad de la aplicación, realizaremos el trabajo de inicialización del SDK integrado, que recopilará la dirección MAC de su dispositivo, IMSI, ID de Android, dirección IP, modelo de hardware, número de versión del sistema operativo, identificador único de dispositivo (como IMEI), dirección de hardware de red (MAC), número de versión de software, método de acceso a la red, tipo, estado, datos de calidad de red, registros de operación, número de serie de hardware, información de registro de servicio, etc. para garantizar las estadísticas normales de datos y el control de seguridad de la aplicación.\n2. Sin su consentimiento, no obtendremos, compartiremos ni proporcionaremos su información a terceros.\n3. Puede acceder, corregir y eliminar su información personal, y también proporcionaremos formas de cancelar y presentar quejas.';

  @override
  String get setting_ihavereadandagreed => 'He leído y acepto';

  @override
  String get setting_policy_tips2 => 'Por favor lea y entienda cuidadosamente';

  @override
  String get wallet_title => 'Billetera';

  @override
  String get wallet_bill => 'Factura';

  @override
  String get wallet_rmb_account => 'Cuenta RMB';

  @override
  String get wallet_usd_account => 'Cuenta USD';

  @override
  String get wallet_account_heading => 'Cuenta de retiro y configuración';

  @override
  String get wallet_bank => 'Tarjeta bancaria';

  @override
  String get wallet_wechat => 'WeChat';

  @override
  String get wallet_alipay => 'Alipay';

  @override
  String get wallet_charge => 'Recargar';

  @override
  String get wallet_cash => 'Retirar';

  @override
  String get wallet_balance => 'Saldo';

  @override
  String get wallet_default_account => 'Cuenta predeterminada';

  @override
  String get wallet_set_default_account => 'Establecer como cuenta predeterminada';

  @override
  String get bill_title => 'Factura';

  @override
  String get bill_out => 'Gasto';

  @override
  String get bill_in => 'Ingreso';

  @override
  String get bill_month => 'Mes';

  @override
  String get bill_fenxi => 'Análisis de ingresos y gastos';

  @override
  String get bill_unfreeze => 'Descongelar';

  @override
  String get bill_all => 'Todas las facturas';

  @override
  String get bill_income => 'Ingreso';

  @override
  String get bill_outcome => 'Gasto';

  @override
  String get bill_freeze => 'Congelar';

  @override
  String get bill_withdraw => 'Retirar';

  @override
  String get bank_title => 'Mis tarjetas bancarias';

  @override
  String get bank_add => 'Agregar tarjeta bancaria';

  @override
  String get add_bank_title => 'Agregar tarjeta bancaria';

  @override
  String get add_bank_name => 'Nombre de la tarjeta bancaria';

  @override
  String get add_bank_branch => 'Sucursal bancaria';

  @override
  String get add_bank_card => 'Número de tarjeta';

  @override
  String get add_bank_real_name => 'Nombre';

  @override
  String get add_bank_address => 'Dirección de apertura de cuenta';

  @override
  String bind_title(Object bind) {
    return 'Vincular $bind';
  }

  @override
  String get bind_account => 'Cuenta';

  @override
  String get bind_image => 'Código de pago';

  @override
  String get bind_name => 'Nombre';

  @override
  String get bind_hint => 'Por favor ingrese';

  @override
  String get charge_title => 'Recargar';

  @override
  String get charge_account => 'Cuenta de recarga';

  @override
  String get charge_money => 'Monto de recarga';

  @override
  String get charge_deposit_type_title => 'Método de recarga';

  @override
  String get charge_deposit_type_online => 'Recarga en línea';

  @override
  String get charge_deposit_type_offline => 'Transferencia fuera de línea';

  @override
  String get charge_offline_nopic_hint => 'Por favor suba el comprobante de recarga';

  @override
  String get charge_upload_proof => 'Por favor suba el comprobante de transferencia';

  @override
  String get withdraw_list_title => 'Historial de retiros';

  @override
  String get withdraw_rmb => 'Retiro RMB';

  @override
  String get withdraw_usd => 'Retiro USD';

  @override
  String get withdraw_status_checking => 'En revisión';

  @override
  String get withdraw_status_approved => 'Revisión aprobada';

  @override
  String get withdraw_status_denied => 'Revisión rechazada';

  @override
  String get withdraw_cash_status_unfinished => 'No transferido';

  @override
  String get withdraw_cash_status_done => 'Transferido';

  @override
  String get withdraw_cash_status_refused => 'Rechazado';

  @override
  String get charge_hint => 'Por favor ingrese el monto de recarga';

  @override
  String get charge_submit => 'Confirmar';

  @override
  String get charge_rmb => 'Recarga RMB';

  @override
  String get charge_usd => 'Recarga USD';

  @override
  String get charge_history_title => 'Historial de recargas';

  @override
  String get cash_title => 'Retirar';

  @override
  String get cash_account => 'Seleccionar cuenta de retiro';

  @override
  String get cash_money => 'Monto de retiro';

  @override
  String get cash_invoice_money => 'Monto de factura';

  @override
  String get cash_invoice_money_hint => 'Por favor ingrese el monto de la factura';

  @override
  String get cash_invoice_upload => 'Subir factura';

  @override
  String get cash_account_list_title => 'Después de que la solicitud sea aprobada, los fondos de retiro se transferirán aleatoriamente a una de las siguientes cuentas:';

  @override
  String get cash_hint => 'Por favor ingrese el monto de retiro';

  @override
  String get cash_withdraw_tips1 => 'Está retirando a';

  @override
  String get cash_withdraw_tips2 => ', el monto de retiro es';

  @override
  String get cash_amount => 'Monto a recibir';

  @override
  String get cash_other => 'Tarifa de servicio';

  @override
  String get cash_submit => 'Confirmar';

  @override
  String get location_permission => 'Se necesita usar el permiso de ubicación, por favor active';

  @override
  String get location_cancel => 'Cancelar';

  @override
  String get location_author => 'Autorizar';

  @override
  String get group_title => 'Miembros del grupo';

  @override
  String get unknown_error => 'Error desconocido';

  @override
  String get data_parsing_exception => 'Excepción en el análisis de datos';

  @override
  String get edit => 'Editar';

  @override
  String get no_data => 'Sin datos';

  @override
  String get note => 'Nota explicativa';

  @override
  String get msg_locating => 'Localizando';

  @override
  String get failed_to_download => 'Error al descargar la actualización';

  @override
  String get pick_address => 'Haga clic para ingresar la dirección de la fábrica';

  @override
  String get update_now => 'Actualizar ahora';

  @override
  String get message => 'Mensaje';

  @override
  String get view_order => 'Ver pedido';

  @override
  String get today => 'Hoy';

  @override
  String get yesterday => 'Ayer';

  @override
  String get send_file => 'Enviar archivo';

  @override
  String get login_expired => 'La sesión ha expirado, por favor inicie sesión nuevamente';

  @override
  String get exit_group_chat_confirm => '¿Está seguro de que desea salir del chat grupal?';

  @override
  String get exit_group_chat_success => 'Ha salido del chat grupal';

  @override
  String get exit_group_chat_page_title => 'Información del chat';

  @override
  String get exit_group_chat_button_title => 'Salir del chat grupal';

  @override
  String get group_chat_setting_view_more => 'Ver más miembros del grupo';

  @override
  String get group_chat_setting_name => 'Nombre del chat grupal';

  @override
  String get group_chat_setting_owner_update => 'Solo el propietario del grupo puede modificar el nombre del grupo';

  @override
  String get group_chat_name_page_title => 'Modificar nombre del chat grupal';

  @override
  String get group_chat_name_page_required => 'Por favor ingrese el nombre del chat grupal';

  @override
  String get group_chat_name_save => 'Guardar';

  @override
  String get group_chat_name_saved => 'Nombre del chat grupal modificado';

  @override
  String get conversation_manage_view_please => 'Por favor seleccione la conversación que desea operar';

  @override
  String get conversation_manage_view_list => 'Lista de conversaciones';

  @override
  String get group_manage_select => 'Por favor seleccione el grupo que desea operar';

  @override
  String get group_manage_list => 'Lista de grupos';

  @override
  String get please_enter => 'Por favor ingrese';

  @override
  String get address_keyword => 'Por favor ingrese palabras clave de la dirección';

  @override
  String get inspector_min_fee => 'Por favor ingrese la tarifa mínima de inspección';

  @override
  String get inspector_id_card_required => 'Por favor ingrese el número de identificación';

  @override
  String get inspector_id_card_upload => 'Por favor suba la foto de identificación';

  @override
  String get inspector_id_card_upload_fail => 'Error al subir la foto de identificación, por favor vuelva a subirla';

  @override
  String get inspector_revoke => '¿Está seguro de que desea revocar la calificación de inspector?';

  @override
  String get inspector_revoke_completed => 'Calificación de inspector revocada';

  @override
  String get male => 'Masculino';

  @override
  String get female => 'Femenino';

  @override
  String get elementary => 'Primaria';

  @override
  String get junior => 'Secundaria';

  @override
  String get technical => 'Técnica';

  @override
  String get senior => 'Preparatoria';

  @override
  String get college => 'Universidad';

  @override
  String get bachelor => 'Licenciatura';

  @override
  String get master => 'Maestría';

  @override
  String get doctor => 'Doctorado';

  @override
  String get yes => 'Sí';

  @override
  String get no => 'No';

  @override
  String get upload_image => 'Subir imagen';

  @override
  String get upload_file => 'Subir archivo';

  @override
  String get revoke_inspector => 'Revocar calificación de inspector';

  @override
  String get deposit_card => 'Tarjeta de ahorro';

  @override
  String get withdrawal_balance => 'El monto de retiro no puede exceder el saldo de la cuenta';

  @override
  String get failed_get_payment_info => 'Error al obtener información de pago';

  @override
  String get recommended_order => 'Pedido recomendado';

  @override
  String get withdrawal_method => 'Por favor proporcione al menos un método de retiro';

  @override
  String get withdrawal_bind_alipay => 'Por favor vincule Alipay primero';

  @override
  String get enabled_camera => 'Por favor configure para permitir el uso de la cámara para tomar fotos';

  @override
  String get valid_email_mobile => 'Por favor ingrese una dirección de correo electrónico o número de teléfono válido';

  @override
  String get apple_map => 'Mapa de Apple';

  @override
  String get baidu_map => 'Mapa de Baidu';

  @override
  String get amap => 'Mapa de Amap';

  @override
  String get google_map => 'Mapa de Google';

  @override
  String get tencent_map => 'Mapa de Tencent';

  @override
  String get image_format => 'El formato de imagen debe ser png, jpg, jpeg';

  @override
  String get enable_location_service => 'Se necesita activar el permiso de ubicación';

  @override
  String get enable_location_service_tips => 'Active el permiso de ubicación para buscar pedidos de inspección cercanos con precisión';

  @override
  String get enable_permission_not_now => 'No configurar ahora';

  @override
  String get enable_permission_goto_setting => 'Ir a configuración';

  @override
  String get failed_location_service => 'Error al obtener información de ubicación';

  @override
  String get turn_on_location_service => 'Por favor active el servicio de ubicación del teléfono';

  @override
  String get no_install_map => 'No ha instalado el mapa';

  @override
  String get camera => 'Cámara';

  @override
  String get photo_album => 'Álbum de fotos';

  @override
  String get new_version => 'Nueva versión lanzada';

  @override
  String get invalid_mail => 'El correo electrónico del usuario no existe';

  @override
  String get invalid_password => 'Contraseña incorrecta';

  @override
  String get invalid_mobile => 'El número de teléfono no existe';

  @override
  String get invalid_auth_code => 'Código de verificación incorrecto';

  @override
  String get invalid_login => 'Inicio de sesión fallido, por favor intente nuevamente';

  @override
  String get grabbing => 'Tomando el pedido';

  @override
  String get hour_ago => 'horas publicado';

  @override
  String get minute_ago => 'minutos publicado';

  @override
  String get report_type => 'Tipo de informe';

  @override
  String get fri => 'Inspección de muestreo';

  @override
  String get fui => '';

  @override
  String get oli => '';

  @override
  String get fat => '';

  @override
  String get cls => 'Supervisión de contenedor';

  @override
  String get fri_cls => '';

  @override
  String get order_payment => 'Pago de pedido';

  @override
  String get order_refund => 'Reembolso de pedido';

  @override
  String get expend_withdrawal => 'Gasto - Retiro';

  @override
  String get incoming_refund => 'Ingreso - Reembolso de pedido';

  @override
  String get incoming_recharge => 'Ingreso - Recarga';

  @override
  String get chat_not_member => 'Ya no es miembro del grupo, no puede enviar mensajes';

  @override
  String get admins => 'Contactar servicio al cliente';

  @override
  String get theme_title => 'Tema';

  @override
  String get theme_light => 'Tema claro';

  @override
  String get theme_dark => 'Tema oscuro';

  @override
  String get theme_auto => 'Seguir sistema';

  @override
  String get amount_total => 'Total';

  @override
  String get amount_available => 'Disponible';

  @override
  String get amount_blocked => 'Congelado';

  @override
  String get download => 'Clic para descargar';

  @override
  String get downloading => 'Descargando';

  @override
  String get saved => 'Guardado';

  @override
  String get order_number => 'Número de pedido';

  @override
  String get order_detail_inspection_cost => 'Costo de inspección';

  @override
  String get delete_account => 'Eliminar cuenta';

  @override
  String get delete_account_confirm => 'Toda la información no se guardará.\n¿Está seguro de eliminar?';

  @override
  String get delete_account_result => 'Cuenta eliminada';

  @override
  String get not_exist_account => 'La cuenta no existe';

  @override
  String get new_password => 'Ingrese nueva contraseña';

  @override
  String get supervisor => 'Supervisor';

  @override
  String get downloadFiles => 'Archivos descargados';

  @override
  String get home_search_hint_inspector => 'Buscar pedidos por ciudad/nombre de producto';

  @override
  String get home_search_hint_admin => 'Buscar pedidos por ciudad/nombre de producto';

  @override
  String get search_recent_history => 'Búsquedas recientes';

  @override
  String get assign => 'Asignar';

  @override
  String get assigned => 'Asignado';

  @override
  String get approve => 'Aprobar';

  @override
  String get assign_inspector => 'Asignar inspector';

  @override
  String get unassigned => 'Sin asignar';

  @override
  String get general_all => 'Todos';

  @override
  String get general_date => 'Fecha';

  @override
  String get general_desc => 'Descripción';

  @override
  String get general_amount => 'Monto';

  @override
  String get assign_search_hint => 'Por favor ingrese apodo/nombre/correo/teléfono';

  @override
  String get assign_cancel_message => 'Confirmar cancelar la asignación de este inspector';

  @override
  String get assign_inspect_times => 'Número de inspecciones';

  @override
  String get assign_leave_message_batch => 'Dejar mensaje en lote';

  @override
  String get assign_price_zero_tips => 'El costo de inspección no puede ser 0';

  @override
  String get assign_applied => 'Aplicado';

  @override
  String get is_auth_forbidden => 'Forbidden';

  @override
  String get apply_time => 'Aplicado en';

  @override
  String get assign_message => 'Mensaje';

  @override
  String get chat_send_message => 'Enviar mensaje';

  @override
  String get chat_send_order => 'Enviar pedido';

  @override
  String get chat_panel_album => 'Álbum';

  @override
  String get chat_panel_camera => 'Cámara';

  @override
  String get chat_panel_file => 'Archivo';

  @override
  String get chat_toolbar_custom_service => 'Servicio al cliente exclusivo';

  @override
  String get chat_toolbar_submit_order => 'Realizar pedido de inspección';

  @override
  String get home_navigation => 'Clic para navegar';

  @override
  String get price_input_error_zero => 'El pedido debe estar entre 0 y 1 millón';

  @override
  String get filter_all => 'Todos los filtros';

  @override
  String get filter_heading_order_status => 'Por estado del pedido';

  @override
  String get filter_heading_insp_date => 'Por fecha de inspección';

  @override
  String get filter_heading_order_date => 'Por fecha de publicación';

  @override
  String get filter_heading_area => 'Por área';

  @override
  String get filter_date_start => 'Fecha de inicio';

  @override
  String get filter_date_end => 'Fecha de fin';

  @override
  String get filter_date_today => 'Pedidos de hoy';

  @override
  String get filter_date_tomorrow => 'Pedidos de mañana';

  @override
  String get filter_date_2days_later => 'Pedidos en 2 días';

  @override
  String get filter_date_3days_later => 'Pedidos en 3 días';

  @override
  String get sort_by_order_date => 'Ordenar por fecha de pedido';

  @override
  String get sort_by_insp_date => 'Ordenar por fecha de inspección';

  @override
  String get sort_by_distance => 'Ordenar por distancia';

  @override
  String get purchase_all_replies => 'Todas las respuestas';

  @override
  String get purchase_replies_count => 'respuestas';

  @override
  String get purchase_no_more_replies => 'No hay más respuestas';

  @override
  String get purchase_save_draft_title => '¿Guardar como borrador?';

  @override
  String get purchase_save_draft_choice => 'Guardar como borrador';

  @override
  String get purchase_save_draft_quit => 'Salir directamente';

  @override
  String get purchase_search_hint => 'Buscar pedidos de compra';

  @override
  String get purchase_reply_hint => 'Responder al contenido de la publicación';

  @override
  String get purchase_reply_reason_hint => 'Reason why recommend';

  @override
  String get purchase_complaint_hint => 'Proporcionar más información ayudará a procesar el informe más rápidamente';

  @override
  String get purchase_reply_paid_hint => 'Ingrese el contenido de la recompensa';

  @override
  String get purchase_edit => 'Editar publicación';

  @override
  String get purchase_publish => 'Publicar solicitud de recompensa';

  @override
  String get purchase_publish_product_label => 'Nombre del producto';

  @override
  String get purchase_publish_title_label => 'Título de la recompensa';

  @override
  String get purchase_publish_quantity => 'Quantity';

  @override
  String get purchase_publish_content_label => 'Descripción detallada';

  @override
  String get purchase_publish_product_hint => 'Por favor ingrese el nombre del producto';

  @override
  String get purchase_publish_title_hint => 'Puede incluir nombre del modelo, región, cantidad, etc.';

  @override
  String get end_date => 'End date';

  @override
  String get purchase_area => 'Area';

  @override
  String get purchase_permission_author_only => 'Only author of post can see replies';

  @override
  String get purchase_publish_quantity_hint => 'Please input quantity';

  @override
  String get purchase_publish_content_hint => 'Por favor ingrese una descripción detallada';

  @override
  String get purchase_publish_price => 'Precio de la recompensa';

  @override
  String get purchase_publish_choose_category => 'Seleccionar categoría';

  @override
  String get purchase_publish_choose_category_hint => 'Por favor seleccione una categoría';

  @override
  String get purchase_paid_publish_switch => 'Vista de pago';

  @override
  String get purchase_paid_publish_set_price => 'Establecer precio';

  @override
  String get purchase_detail_response_all => 'Todas las respuestas';

  @override
  String get purchase_detail_response_author_only => 'Ver solo del autor';

  @override
  String get purchase_detail_response_asc => 'Orden ascendente';

  @override
  String get purchase_detail_response_desc => 'Orden descendente';

  @override
  String get purchase_detail_more_reply => 'Responder';

  @override
  String get purchase_detail_more_up => 'Me gusta';

  @override
  String get purchase_detail_more_cancel_up => 'Cancelar me gusta';

  @override
  String get purchase_my_posts => 'Mis publicaciones';

  @override
  String get purchase_my_replies => 'Mis respuestas';

  @override
  String get purchase_my_appeals => 'Mis apelaciones';

  @override
  String get purchase_appeal_detail => 'Detalles de la apelación';

  @override
  String get purchase_appeal_submit => 'Enviar apelación';

  @override
  String get purchase_appeal_cancel => 'Cancelar apelación';

  @override
  String get purchase_appeal_approve => 'Apelación aprobada';

  @override
  String get purchase_appeal_denied => 'Apelación rechazada';

  @override
  String get purchase_paid_content_owner_tips => 'Contenido de pago';

  @override
  String get purchase_paid_content_tips => 'Contenido de pago, pague para ver';

  @override
  String get purchase_paid_content_paid_tips => 'Contenido de pago desbloqueado';

  @override
  String get purchase_review_leave => 'Dejar una reseña';

  @override
  String get purchase_review_my_score => 'Mi evaluación';

  @override
  String get purchase_my_replies_original_header => 'Publicación original';

  @override
  String get purchase_publish_bounty_tips => 'Nota: El monto de la recompensa se puede llenar libremente, una recompensa más alta puede atraer a más inspectores para proporcionar activamente la información que necesita.';

  @override
  String get purchase_reply_to => 'Responder a';

  @override
  String get purchase_modify_bounty => 'Modificar recompensa';

  @override
  String get purchase_bounty_money => 'Recompensa';

  @override
  String get purchase_evaluated_person => 'personas han evaluado';

  @override
  String get purchase_comment_paid_supplier => 'Proveedor';

  @override
  String get purchase_comment_paid_contact => 'Contacto';

  @override
  String get purchase_comment_paid_phone => 'Número de teléfono';

  @override
  String get purchase_comment_paid_email => 'Correo electrónico';

  @override
  String get purchase_comment_paid_address => 'Dirección de la fábrica';

  @override
  String get purchase_comment_paid_other => 'Otros';

  @override
  String get purchase_comment_paid_low_price => 'Precio base del producto';

  @override
  String get purchase_appeal_title => 'Solicitar reembolso';

  @override
  String get purchase_appeal_reason => 'Por favor ingrese el motivo de la apelación';

  @override
  String get purchase_appeal_request_price => 'Monto de la apelación:';

  @override
  String get purchase_appeal_request_reason => 'Motivo de la apelación:';

  @override
  String get purchase_post_status_draft => 'Borrador';

  @override
  String get purchase_post_status_reviewing => 'En revisión';

  @override
  String get purchase_post_status_published => 'Revisado y aprobado';

  @override
  String get purchase_post_status_denied => 'No aprobado';

  @override
  String get purchase_post_publish => 'Publicar';

  @override
  String get purchase_complaint_type_leading => 'Por favor seleccione el tipo de informe';

  @override
  String get purchase_complaint_type_1 => 'Contenido pornográfico';

  @override
  String get purchase_complaint_type_2 => 'Publicidad no deseada';

  @override
  String get purchase_complaint_type_3 => 'Acoso/Ataque';

  @override
  String get purchase_complaint_type_4 => 'Actividad ilegal';

  @override
  String get purchase_complaint_type_5 => 'Información política inexacta';

  @override
  String get purchase_complaint_type_6 => 'Violación de derechos';

  @override
  String get purchase_complaint_type_7 => 'Otros';

  @override
  String get shop_goods_detail_title => 'Detalles del producto';

  @override
  String get mall_buy_immediate => 'Comprar ahora';

  @override
  String get mall_goods_count => 'Cantidad';

  @override
  String get mall_confirm_pay => 'Confirmar pago';

  @override
  String get mall_order_confirm => 'Confirmar pedido';

  @override
  String get mall_submit_order => 'Enviar pedido';

  @override
  String get mall_goods_price => 'Precio del producto';

  @override
  String get mall_express_price => 'Costo de envío';

  @override
  String get mall_price_total => 'Total:';

  @override
  String get mall_payment => 'Caja';

  @override
  String get mall_payment_methods => 'Métodos de pago';

  @override
  String get mall_pay_succeed => 'Pago exitoso';

  @override
  String get mall_check_order_detail => 'Ver detalles del pedido';

  @override
  String get mall_order_remark => 'Nota del pedido';

  @override
  String get mall_order_remark_input => 'Ingresar nota';

  @override
  String get purchase_detail_more_report => 'Informar';

  @override
  String get purchase_reply_paid_content_tips => 'Nota: Por favor complete la información verdadera, las respuestas de pago necesitan esperar revisión después de la publicación, y solo pueden ser vistas por otros después de la revisión';

  @override
  String get public_ip_address => 'Ubicación IP:';

  @override
  String get inspection_widget_suit_tips => 'Durante la inspección, use una tarjeta de identificación o uniforme de trabajo. Si aún no tiene uno, puede comprarlo en la página de inicio';

  @override
  String get purchase_paid_content_appeal => 'Apelar';

  @override
  String get report_success => 'Informe exitoso';

  @override
  String tabbar_tab_names(String names) {
    String _temp0 = intl.Intl.selectLogic(
      names,
      {
        'tab_home': 'Inicio',
        'tab_shortcut': 'Acceso directo',
        'tab_message': 'Mensajes',
        'tab_mine': 'Mi cuenta',
        'tab_publish': '',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String order_status_desc(String status) {
    String _temp0 = intl.Intl.selectLogic(
      status,
      {
        'order_status_unknown': 'Desconocido',
        'order_wait_pay': 'Pendiente de pago',
        'order_cancelled': 'Cancelado',
        'order_cancelled_refund_pending': 'Cancelado, reembolso pendiente de revisión',
        'order_refund_pending': 'Reembolso pendiente de revisión',
        'order_refund_partial': 'Reembolso parcial',
        'order_refund_denied': 'Reembolso denegado',
        'order_wait_dispatch': 'Pendiente de asignación',
        'order_doing': 'En inspección',
        'order_finished': 'Completado',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String pay_type(String type) {
    String _temp0 = intl.Intl.selectLogic(
      type,
      {
        'pay_rmb': 'Cuenta RMB',
        'pay_usd': 'Cuenta USD',
        'pay_zfb': 'Alipay',
        'pay_paypal': 'PayPal',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String purchase_complaint_type(String type) {
    String _temp0 = intl.Intl.selectLogic(
      type,
      {
        'type_1': 'Contenido pornográfico',
        'type_2': 'Publicidad no deseada',
        'type_3': 'Acoso/Ataque',
        'type_4': 'Actividad ilegal',
        'type_5': 'Información política inexacta',
        'type_6': 'Violación de derechos',
        'type_7': 'Otros',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String get purchase_reply_paid_my_evaluate => '(Mi evaluación)';

  @override
  String get bind_now => 'Vincular ahora';

  @override
  String get cancel_register => 'Cancelar registro';

  @override
  String register_and_bind_email0(Object email) {
    return 'Registrar y vincular $email como correo electrónico principal';
  }

  @override
  String register_and_bind_email1(Object email) {
    return 'Su correo electrónico $email ya está registrado, continuar vinculando';
  }

  @override
  String get register_and_bind_email2 => 'Registrar y vincular correo electrónico principal';

  @override
  String get new_register_bind_title => 'Configurar correo electrónico principal de inicio de sesión y registrar';

  @override
  String get new_register_bind_field_account_tips => 'Vincular correo electrónico/número de teléfono';

  @override
  String get register => 'Registrar';

  @override
  String get switch_account => 'Cambiar cuenta';

  @override
  String get switch_account_confirm_tips => '¿Está seguro de que desea cambiar de cuenta?';

  @override
  String get password_must_have => 'Su contraseña debe contener:';

  @override
  String get password_must_have_1 => '8-32 caracteres de longitud';

  @override
  String get password_must_have_2 => '1 letra minúscula (a-z)';

  @override
  String get password_must_have_3 => '1 número';

  @override
  String get password_must_have_4 => '1 símbolo (como !@#\\\$%^&*)';

  @override
  String get password_login => 'Ingrese la contraseña de inicio de sesión';

  @override
  String get password_login_again => 'Ingrese la nueva contraseña nuevamente';

  @override
  String get choose_account_to_login => 'Seleccione una cuenta para iniciar sesión rápidamente';

  @override
  String get finish => 'Finalizar';

  @override
  String get done => 'Hecho';

  @override
  String get account_apple => 'Iniciar sesión con Apple';

  @override
  String get account_google => 'Iniciar sesión con Google';

  @override
  String get account_wechat => 'Iniciar sesión con WeChat';

  @override
  String get account_facebook => 'Iniciar sesión con Facebook';

  @override
  String get third_account_unbind => 'Desvincular';

  @override
  String get third_account_bind => 'Vincular';

  @override
  String get confirm_unbind => '¿Está seguro de que desea desvincular?';

  @override
  String get inspection_requirement => 'Requisitos de inspección';

  @override
  String get liveroom_entrance => 'Cola de sala en vivo';

  @override
  String get add_account => 'Agregar nueva cuenta';

  @override
  String get message_order => 'Orders';

  @override
  String get message_email => 'Email';

  @override
  String get message_wallet => 'Wallet';

  @override
  String get message_user => 'User info';

  @override
  String get salesman => 'Salesman';

  @override
  String get public_continue => 'Continue';

  @override
  String get camera_permission_tips => 'In order to facilitate you to upload pictures, files or photos when using the App, you need to allow the use of camera and storage permissions.';

  @override
  String get ai_category_inspector => 'Inspector de categoría';

  @override
  String get ai_nothing_category => 'No hay categoría para detectar';

  @override
  String get ai_category_name => 'Nombre de la categoría';

  @override
  String get ai_quantity => 'Cantidad';

  @override
  String get ai_packaging => 'Empaquetado';

  @override
  String get ai_shipping_mark => 'Marca de envío';

  @override
  String get ai_product_style => 'Estilo del producto';

  @override
  String get ai_test => 'Prueba';

  @override
  String get ai_craftsmanship => 'Habilidad artesanal';

  @override
  String get ai_test_verification => 'Verificación de pruebas';

  @override
  String get ai_category_measure => 'Medición de categoría';

  @override
  String get ai_spare_parts => 'Piezas de repuesto';

  @override
  String get ai_sampling_number => 'Número de muestreo';

  @override
  String ai_input_range_number(Object range) {
    return 'Ingrese un número dentro de $range';
  }

  @override
  String ai_enter_range_number(Object range) {
    return 'Por favor ingrese un número dentro de $range';
  }

  @override
  String get ai_selected => 'Seleccionado';

  @override
  String get ai_selected_status => 'Estado seleccionado';

  @override
  String get ai_order_quantity => 'Cantidad del pedido';

  @override
  String get ai_packaged_boxes_quantity => 'Cantidad de cajas empacadas (producto terminado)';

  @override
  String get ai_unpackaged_boxes_quantity => 'Cantidad de cajas sin empacar (producto terminado)';

  @override
  String get ai_sample_from_packaged => 'Muestreo desde cajas empacadas';

  @override
  String get ai_sample_from_unpackaged => 'Muestreo desde cajas sin empacar';

  @override
  String get ai_spare_parts_quantity => 'Cantidad de piezas de repuesto';

  @override
  String get ai_sampling_packaging_number => 'Número de empaquetado de muestreo';

  @override
  String get ai_sampling_packaging_number_record => 'Registro de número de empaquetado de muestreo';

  @override
  String get ai_sampling_packaging_number_list => 'Registra los números de las cajas externas de muestreo';

  @override
  String get ai_judgment => 'Juicio';

  @override
  String get ai_judgment_item => 'Elemento de juicio';

  @override
  String get ai_standard => 'Estandar';

  @override
  String get ai_result => 'Resultado';

  @override
  String get ai_conclusion => 'Conclusión';

  @override
  String get ai_overall_conclusion => 'Conclusión general';

  @override
  String get ai_consistency => '¿Es consistente?';

  @override
  String get ai_yes => 'Sí';

  @override
  String get ai_no => 'No';

  @override
  String get ai_remarks => 'Comentarios';

  @override
  String get ai_numerical => 'Número';

  @override
  String get ai_recommended_test_items => 'Elementos de prueba recomendados';

  @override
  String get ai_test_item => 'Elemento de prueba';

  @override
  String get ai_add_all => 'Añadir todo';

  @override
  String get ai_add_plus => '+ Añadir';

  @override
  String get ai_add => 'Añadir';

  @override
  String ai_confirm_delete(Object name) {
    return '¿Está seguro de que desea eliminar $name?';
  }

  @override
  String get ai_enter_test_item => 'Ingrese el elemento de prueba';

  @override
  String get ai_defect_record => 'Registro de defectos';

  @override
  String get ai_defect_photo => 'Foto de defecto';

  @override
  String get ai_defect_description => 'Descripción del defecto';

  @override
  String get ai_defect_level => 'Nivel de defecto';

  @override
  String get ai_found_quantity => 'Cantidad encontrada';

  @override
  String get ai_handling_method => 'Método de manejo';

  @override
  String get ai_edit => 'Editar';

  @override
  String get ai_delete => 'Eliminar';

  @override
  String get ai_pick_out => 'Separar';

  @override
  String get ai_replace => 'Reemplazar';

  @override
  String get ai_rework => 'Rehacer';

  @override
  String get ai_edit_description => 'Editar descripción';

  @override
  String get ai_critical => 'Crítico';

  @override
  String get ai_important => 'Importante';

  @override
  String get ai_minor => 'Menor';

  @override
  String get ai_defect_list => 'Lista de defectos';

  @override
  String get ai_test_level => 'Nivel de prueba';

  @override
  String get ai_sampling_sample => 'Muestra de muestreo';

  @override
  String get ai_sampling_level => 'Nivel de muestreo';

  @override
  String get ai_additional_information => 'Información adicional';

  @override
  String get ai_inspection_record => 'Registro de inspección';

  @override
  String get ai_sample_count => 'Conteo de muestras';

  @override
  String get ai_maximum_allowable_value => 'Valor máximo permitido';

  @override
  String get ai_test_item_name => 'Nombre del elemento de prueba';

  @override
  String get ai_test_result => 'Resultado de la prueba';

  @override
  String get ai_basic_information => 'Información básica';

  @override
  String get ai_new_test_item => 'Nuevo elemento de prueba';

  @override
  String get ai_test_project => 'Proyecto de prueba';

  @override
  String get ai_measurement_project => 'Proyecto de medición';

  @override
  String get ai_measure_need_num => 'Número requerido';

  @override
  String get ai_measurement_unit => 'Unidad de medida';

  @override
  String get ai_measurement_method => 'Método de medición';

  @override
  String get ai_measurement_record => 'Registro de medición';

  @override
  String get ai_measured => 'Medido';

  @override
  String get ai_unit_of_measurement => 'Unidad de medida';

  @override
  String get ai_measured_value => 'Valor medido';

  @override
  String get ai_product_number => 'Número de producto';

  @override
  String get ai_number => 'Número';

  @override
  String get ai_new_measurement_item => 'Nuevo elemento de medición';

  @override
  String get ai_length_width_height => 'Largo, ancho, alto';

  @override
  String get ai_dimensions_length => 'Largo';

  @override
  String get ai_dimensions_width => 'Ancho';

  @override
  String get ai_dimensions_height => 'Alto';

  @override
  String get ai_length_width => 'Largo y ancho';

  @override
  String get ai_other => 'Otro';

  @override
  String get ai_allowable_error => 'Error permitido';

  @override
  String get ai_report_summary => 'Resumen del informe';

  @override
  String get ai_special_note => 'Nota especial';

  @override
  String get ai_overall_conclusion_2 => 'Conclusión general';

  @override
  String get ai_summary => 'Resumen';

  @override
  String get ai_category_name_table => 'Tabla de nombres de categorías';

  @override
  String get ai_compliance => '¿Cumple con normas?';

  @override
  String get ai_remarks_2 => 'Comentarios';

  @override
  String get ai_defect_summary => 'Resumen de defectos';

  @override
  String get ai_no_guidance_instructions => 'No hay instrucciones de guía disponibles';

  @override
  String get ai_no_standard_instructions => 'No hay instrucciones estándar disponibles';

  @override
  String get ai_please_fill_in => 'Por favor complete';

  @override
  String ai_please_supplement_level_or_sample(Object level, Object sample) {
    return 'Por favor suplemente $level o $sample';
  }

  @override
  String get ai_please_add => 'Por favor añada';

  @override
  String get ai_please_input => 'Por favor ingrese';

  @override
  String get ai_please_select => 'Por favor seleccione';

  @override
  String ai_name_not_filled(Object name) {
    return '$name no completado';
  }

  @override
  String get ai_addition_successful => 'Añadido con éxito';

  @override
  String get ai_confirm_action => 'Confirmar';

  @override
  String get ai_cancel_action => 'Cancelar';

  @override
  String get ai_submit => 'Enviar';

  @override
  String get ai_next_item => 'Siguiente elemento';

  @override
  String get ai_complete => 'Completo';

  @override
  String get ai_change_description => 'Cambiar descripción';

  @override
  String get ai_action_guidance_instructions => 'Instrucciones de guía de acción';

  @override
  String get ai_action_standard_instructions => 'Instrucciones estándar de acción';

  @override
  String get ai_add_description => 'Añadir descripción';

  @override
  String get ai_change_description_note => 'Nota: Abajo se encuentran los defectos ya descubiertos. Si se modifican, los datos históricos también adoptarán la nueva descripción!';

  @override
  String get ai_packing_completion_rate => 'Tasa de completación de empaque';

  @override
  String get ai_unprocessed_quantity => 'Cantidad sin procesar';

  @override
  String get ai_sample_level_type_0 => 'Nivel I';

  @override
  String get ai_sample_level_type_1 => 'Nivel II';

  @override
  String get ai_sample_level_type_2 => 'Nivel III';

  @override
  String get ai_sample_level_type_s1 => 'S-1';

  @override
  String get ai_sample_level_type_s2 => 'S-2';

  @override
  String get ai_sample_level_type_s3 => 'S-3';

  @override
  String get ai_sample_level_type_s4 => 'S-4';

  @override
  String get ai_sample_level_type_nothing => 'Ninguno';

  @override
  String get ai_inspection_image => 'Imagen';

  @override
  String get ai_photo_confirm => 'Confirmar foto';

  @override
  String get ai_add_product_ask_save => '¿Desea guardar los cambios realizados?';

  @override
  String get ai_add_product_save => 'Guardar';

  @override
  String get ai_add_product_edit_model => 'Edición de modelo';

  @override
  String get ai_add_product_model_name => 'Nombre del modelo';

  @override
  String get ai_add_product_input_model => 'Ingrese el nombre del modelo';

  @override
  String get ai_add_product_num => 'Cantidad';

  @override
  String get ai_add_product_input_num => 'Ingrese la cantidad del modelo';

  @override
  String get ai_add_product_unit => 'Unidad';

  @override
  String get ai_add_product_ask_delete => '¿Desea eliminar este modelo?';

  @override
  String get ai_add_product_edit_product => 'Edición de producto';

  @override
  String get ai_add_product_product_name => 'Nombre del producto';

  @override
  String get ai_add_product_model => 'Modelo';

  @override
  String get ai_add_product_input_product_name => 'Ingrese el nombre del producto';

  @override
  String get ai_add_product_new_model => 'Nuevo modelo';

  @override
  String get ai_add_product_ask_product => '¿Desea eliminar este producto y todos sus modelos?';

  @override
  String get ai_add_product_picture_lost => 'Falta la imagen';

  @override
  String ai_add_product_lost_des(Object lostStr) {
    return 'Actualmente falta $lostStr, por favor complete toda la información antes de realizar la inspección';
  }

  @override
  String get ai_add_product_model_full => 'Nombre completo del modelo del producto';

  @override
  String get ai_add_product_model_title => 'Modelo del producto';

  @override
  String get ai_add_product_new => 'Añadir producto';

  @override
  String get ai_model_unit_piece => 'piece';

  @override
  String get ai_model_unit_only => 'piece';

  @override
  String get ai_model_unit_item => 'piece';

  @override
  String get ai_model_unit_pair => 'pair';

  @override
  String get ai_model_unit_set => 'set';

  @override
  String get ai_model_unit_dozen => 'dozen';

  @override
  String get ai_model_unit_roll => 'roll';

  @override
  String get ai_model_unit_vehicle => 'vehicle';

  @override
  String get ai_model_unit_head => 'head';

  @override
  String get ai_model_unit_bag => 'bag';

  @override
  String get ai_model_unit_box => 'box';

  @override
  String get ai_model_unit_pack => 'pack';

  @override
  String get ai_model_unit_yard => 'yard';

  @override
  String get ai_model_unit_meter => 'meter';

  @override
  String get ai_model_unit_kilogram => 'kilogram';

  @override
  String get ai_model_unit_metric_ton => 'metric ton';

  @override
  String get ai_model_unit_liter => 'liter';

  @override
  String get ai_model_unit_gallon => 'gallon';

  @override
  String get ai_model_unit_other => 'other';

  @override
  String get ai_default_config_des => 'There are currently no detection templates for the product. You can choose the template below or call (+86) to configure the template.';

  @override
  String get ai_default_config_category_all => 'clasificación (todos)';

  @override
  String get ai_default_config_select_template => 'por favor, elija la plantilla';

  @override
  String get ai_default_config_template_selection => 'selección de plantilla';

  @override
  String get ai_default_config_search_template => 'buscar plantilla';

  @override
  String get ai_default_config_classify => 'clasificado';

  @override
  String get ai_default_config_preview => 'preview';

  @override
  String get ai_default_config_use => 'usar';

  @override
  String get ai_default_config_current_use_button => 'Aplicar a este producto';

  @override
  String get ai_default_config_more_use_button => 'Aplicar a más productos';

  @override
  String get ai_default_config_product_list => 'Lista de productos';

  @override
  String get ai_default_config_use_warning => 'Note: 【Mod】Product template already loaded; 【Ops】Template already configured by operations. Loading a new template will overwrite previous data.';

  @override
  String get ai_default_config_tag_default => 'Oper';

  @override
  String get ai_default_config_tag_manual => 'Mod';

  @override
  String get ai_default_config_load_progress => 'Progreso de carga';

  @override
  String ai_default_config_template_progress(Object name) {
    return 'Plantilla cargada completada $name.';
  }

  @override
  String ai_default_config_template_fail_count(Object name) {
    return 'Falló $name, haga clic para reintentar';
  }

  @override
  String get ai_default_config_load => 'Cargar';

  @override
  String get ai_default_config_success => 'Éxito';

  @override
  String get ai_default_config_fail => 'Fallo';

  @override
  String get ai_default_config_template => 'template';

  @override
  String get ai_add_model_count_warning => 'The quantity must be greater than 0';

  @override
  String get ai_default_config_product_edit => '编辑产品';

  @override
  String get ai_wait => 'espera';

  @override
  String get ai_product_info => 'Información del producto';

  @override
  String get ai_product_category => 'Categoría del producto';

  @override
  String get ai_product_unit => 'Unidad del producto';

  @override
  String get ai_package_num_done => 'Cajas empacadas';

  @override
  String get ai_product_full => 'Información adicional';

  @override
  String get ai_product_full_tip => 'La información del producto en el pedido está incompleta. Por favor complétela según la información real en el lugar de inspección.';

  @override
  String get ai_each_box => 'Por caja';

  @override
  String get ai_simple_count => 'Número de muestras';

  @override
  String get ai_simple_level => 'Estándar de muestreo';

  @override
  String get ai_simple_num => 'Cantidad de muestreo';

  @override
  String get ai_simple_no => 'Número de caja muestreada';

  @override
  String get ai_simple_result => 'Resultado de evaluación';

  @override
  String get ai_simple_project => 'Ítem de inspección';

  @override
  String get ai_simple_project_manage => 'Gestionar ítems de inspección';

  @override
  String get ai_simple_project_edit => 'Editar ítem de inspección';

  @override
  String get ai_simple_project_recmend => 'Recomendación inteligente';

  @override
  String get ai_simple_project_input => 'Registro de inspección';

  @override
  String get ai_simple_help => 'Ayuda';

  @override
  String get ai_simple_project_record => 'Registro de inspección';

  @override
  String get ai_simple_require => 'Requisitos del cliente';

  @override
  String get ai_simple_record => 'Registro';

  @override
  String get ai_simple_dsec => 'Descripción';

  @override
  String get ai_simple_before => 'Ítem anterior';

  @override
  String get ai_simple_add => 'Agregar grupo';

  @override
  String get ai_simple_add_desc => 'Agregar descripción para fotos';

  @override
  String get ai_simple_add_citations => 'Citas';

  @override
  String get ai_no_more => 'No hay más datos';

  @override
  String get ai_wrong_tip => 'La cantidad no puede exceder el total';

  @override
  String get ai_defect_records => 'Registros de defectos';

  @override
  String get ai_check_require => 'Requisitos de muestreo';

  @override
  String get ai_find_defect => 'Defecto encontrado';

  @override
  String get ai_defect_question => 'Problema de defecto';

  @override
  String get ai_modify_level => 'Modificar nivel de muestreo';

  @override
  String get ai_defect_quick => 'Agregar defecto de proceso rápido';

  @override
  String get ai_defect_self => 'Nombre de defecto personalizado';

  @override
  String get ai_defect_record_list => 'Lista de registros de defectos';

  @override
  String get ai_measure_require => 'Requisitos de medición';

  @override
  String get ai_measurement_item => 'Ítem de medición';

  @override
  String get ai_measurement_error => 'Error';

  @override
  String get ai_measurement_standard => 'Estándar de medición';

  @override
  String get ai_measurement_value_standard => 'Valor estándar';

  @override
  String get ai_measurement_camera => 'Foto de medición';

  @override
  String get ai_measurement_add => 'Agregar estándar de medición rápido';

  @override
  String get ai_product_first => 'Imagen principal del producto';

  @override
  String get ai_product_report => 'Generar informe';

  @override
  String get ai_product_report_tip => 'Seleccione la imagen principal del producto';

  @override
  String get ai_product_report_special => 'Ingrese contenido que requiera atención especial';

  @override
  String get ai_product_report_sign => 'Firma';

  @override
  String get ai_product_report_sign_done => 'Firma completada';

  @override
  String get ai_defect_names => 'Nombres de defectos';

  @override
  String get ai_input_tip => 'Ingrese el nombre';

  @override
  String get ai_add_measure_tip => 'Agregue estándar de medición primero';

  @override
  String get ai_wrong_num => 'Cantidad incorrecta';

  @override
  String get ai_wrong_name => 'Ingrese nombre del producto';

  @override
  String get ai_wrong_sample_num => 'No puede exceder el número de muestras';

  @override
  String get ai_per_box => 'Cantidad por caja';

  @override
  String get ai_wrong_sample_num_cal => 'Muestras empacadas + no empacadas deben igualar el número de muestras';

  @override
  String get ai_sure_delete => '¿Confirmar eliminación?';

  @override
  String get ai_choose_tip => 'Seleccione método y cantidad de manejo de defectos';

  @override
  String get ai_weight => 'Peso bruto';

  @override
  String get sampling_plan => 'Plan de muestreo';

  @override
  String get single => 'Individual';

  @override
  String get normal => 'Normal';

  @override
  String get summarize => 'Summarize';

  @override
  String get po_number => 'Número de PO';

  @override
  String get product_quantity => 'Cantidad de producto';

  @override
  String get customer_name => 'Nombre del cliente';

  @override
  String get supplier_name => 'Nombre del proveedor';

  @override
  String get inspection_date => 'Fecha de inspección';

  @override
  String get arrival_time => 'Hora de llegada';

  @override
  String get completion_time => 'Hora de finalización';

  @override
  String get inspection_address => 'Dirección de inspección';

  @override
  String get inspector => 'Inspector';

  @override
  String get inspection_report_note => 'Este informe de inspección es solo de referencia. La aprobación final está sujeta a la confirmación del cliente.';

  @override
  String get remark_toast => 'Please fill in the remarks first';

  @override
  String get process_appearance_judgment => 'Process appearance judgment';

  @override
  String get test_validation_judgment => 'Test validation judgment';

  @override
  String check_save(Object name) {
    return 'Are you sure you want to save $name?';
  }

  @override
  String get select_template_config_tip => 'If you need to configure the inspection template, please contact your order follower';
}
