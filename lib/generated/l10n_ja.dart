import 'package:intl/intl.dart' as intl;

import 'l10n.dart';

// ignore_for_file: type=lint

/// The translations for Japanese (`ja`).
class SJa extends S {
  SJa([String locale = 'ja']) : super(locale);

  @override
  String get app_name => '検品オンライン';

  @override
  String get search => '検索';

  @override
  String get shortcut_tab_name => 'ショートカット';

  @override
  String get loading => '読み込み中...';

  @override
  String get nomore => 'これ以上のコンテンツはありません';

  @override
  String get confirm => '確認';

  @override
  String get more_replies => 'さらに返信';

  @override
  String get purchase_paid_publish_information_title => '顧客がお金を払って見るものは次のとおりです';

  @override
  String get purchase_set_fee => '視聴料金を設定する';

  @override
  String get purchase_comment_paid_supplier_hint => 'サプライヤー名を入力してください';

  @override
  String get purchase_comment_paid_contact_hint => '連絡担当者名を入力してください';

  @override
  String get purchase_comment_paid_phone_hint => '電話番号を入力してください';

  @override
  String get purchase_comment_paid_email_hint => 'メールアドレスを入力してください';

  @override
  String get purchase_comment_paid_address_hint => '工場住所を入力してください';

  @override
  String get purchase_comment_paid_other_hint => 'その他の情報（任意）';

  @override
  String get purchase_comment_paid_low_price_hint => '最低価格を入力してください（任意）';

  @override
  String get purchase_reply_paid_title => '有料返信';

  @override
  String get purchase_reply_paid_desc => '（サプライヤー情報と商品参考価格）';

  @override
  String purchase_reply_paid_evaluated_people(Object people_num) {
    return '（$people_num人が評価済み）';
  }

  @override
  String get language_setting => '言語設定';

  @override
  String get public_and => 'と';

  @override
  String get public_publish => '公開';

  @override
  String get public_distance => '距離';

  @override
  String get public_deny => '拒否';

  @override
  String get public_see_more => 'もっと見る';

  @override
  String get general_select => '選択';

  @override
  String get language_page_title => '多言語';

  @override
  String get language_chinese => '中国語';

  @override
  String get language_english => '英語';

  @override
  String get public_seconds_ago => '秒前';

  @override
  String get public_minutes_ago => '分前';

  @override
  String get public_hours_ago => '時間前';

  @override
  String get public_status_applied => '申請済み';

  @override
  String get public_status_refused => '拒否されました';

  @override
  String get public_status_approved => '承認されました';

  @override
  String get public_status_canceled => 'キャンセルされました';

  @override
  String get public_app_name => 'Inspector';

  @override
  String get public_send => '送信';

  @override
  String get public_ok => 'OK';

  @override
  String get public_price => '価格';

  @override
  String get public_cancel => 'キャンセル';

  @override
  String get public_manage => '管理';

  @override
  String get public_finish => '完了';

  @override
  String get public_reset => 'リセット';

  @override
  String get public_leave_message => 'メッセージを残す';

  @override
  String get public_share => '共有';

  @override
  String get login_submit => 'ログイン';

  @override
  String get login_mobile_login => '携帯電話でログイン';

  @override
  String get login_mobile_tips => '携帯電話番号を入力してください';

  @override
  String get login_email_login => 'メールでログイン';

  @override
  String get login_email_tips => 'メールアドレスを入力してください';

  @override
  String get registry_email_phone_tips => 'メールアドレスまたは電話番号を入力してください';

  @override
  String get login_verify_tips => '認証コードを入力してください';

  @override
  String get login_password_tips => 'パスワードを入力してください';

  @override
  String get login_password_login => 'パスワードでログイン';

  @override
  String get login_verify_login => '認証コードでログイン';

  @override
  String get login_register => '登録';

  @override
  String get login_take_code => '認証コードを取得';

  @override
  String get login_forget_password => 'パスワードを忘れた';

  @override
  String get login_agreement => 'Inspector.Itd協約に同意する';

  @override
  String get login_area_selected => '国を選択';

  @override
  String get tab_home => 'ホーム';

  @override
  String get tab_order => '注文';

  @override
  String get tab_shortcut => 'ショートカット';

  @override
  String get tab_purchase => '購入';

  @override
  String get tab_message => 'メッセージ';

  @override
  String get tab_mine => 'マイページ';

  @override
  String get supplement_title => '個人情報を完成させてください';

  @override
  String get supplement_next => '完成させる';

  @override
  String get home_title => '検品広場';

  @override
  String get home_record => '申請記録';

  @override
  String get home_newest => '最新の検品';

  @override
  String get home_nearest => '近くの検品';

  @override
  String home_recommend(Object money) {
    return '推薦報酬 RMB$money元';
  }

  @override
  String get home_sampling => 'サンプリング検査';

  @override
  String get home_word => 'WORD報告書';

  @override
  String home_unit(Object day, Object people) {
    return '$people人/$day日';
  }

  @override
  String get home_product_tip => '製品：';

  @override
  String get home_person_apply => '人が申請';

  @override
  String get home_know_tip => '検品注意事項';

  @override
  String get home_inspection_tip => '検品料金はデフォルトで交渉価格です。変更可能で、低料金の場合優先的に割り当てられる可能性があります';

  @override
  String get home_reviewed => '確認し、遵守しました';

  @override
  String get home_apply => '申請';

  @override
  String get home_apply_price => '金額を入力してください￥';

  @override
  String get home_apply_check => '検品注意事項をご確認ください';

  @override
  String get home_apply_tips => 'あなたは検品員ではありません。1年以上の外国貿易検品経験がある場合は、関連資格証明を提出してください';

  @override
  String get home_complete_profile_tips => '検品員プロフィールなどの関連情報を完成させると、検品申請の承認率が上がります';

  @override
  String get home_apply_sure => '審査を提出';

  @override
  String get home_complete_profile_sure => '完成させる';

  @override
  String get home_apply_cancel => '申請をキャンセル';

  @override
  String get home_update => '修正';

  @override
  String get home_navi => 'ナビゲーション';

  @override
  String get mine_unauth => '未認証';

  @override
  String get mine_checking => '審査待ち';

  @override
  String get mine_check_failed => '審査失敗';

  @override
  String get mine_vip_level => 'VIPレベル';

  @override
  String get mine_credit_quota => '信用枠';

  @override
  String get mine_authed => '認証情報を修正';

  @override
  String get mine_authed_inspector => '検品員情報を修正';

  @override
  String get mine_amount => 'アカウント残高';

  @override
  String get mine_cash => 'チャージ/引き出し';

  @override
  String get mine_order => '私の注文';

  @override
  String get mine_purchase => '私の購入';

  @override
  String get mine_check => '私の検品';

  @override
  String get mine_address => 'アドレス帳（サプライヤー）';

  @override
  String get mine_recommend => '推薦';

  @override
  String get mine_setting => '設定';

  @override
  String get mine_header_inspect => '検品管理';

  @override
  String get mine_header_purchase => '購入管理';

  @override
  String get mine_header_other => 'その他';

  @override
  String get mine_inspect_mine => '私の検品';

  @override
  String get mine_inspect_order => '注文管理';

  @override
  String get mine_inspect_history => '申請履歴';

  @override
  String get mine_purchase_mine => '私の購入';

  @override
  String get mine_purchase_reply => '返信記録';

  @override
  String get mine_purchase_appeal => '申し立て管理';

  @override
  String get mine_other_recommend => '推薦';

  @override
  String get mine_other_address => 'アドレス帳（サプライヤー）';

  @override
  String get mine_other_settings => '設定';

  @override
  String get profile_title => '個人情報';

  @override
  String get profile_avatar => 'アバター';

  @override
  String get profile_name => 'ニックネーム';

  @override
  String get profile_mobile => '携帯電話番号';

  @override
  String get profile_country => '国';

  @override
  String get profile_real_name => '本名';

  @override
  String get profile_city => '都市';

  @override
  String get profile_email => 'メール';

  @override
  String get profile_wechat => 'WeChat';

  @override
  String get profile_bind_manage => 'アカウント連携管理';

  @override
  String get profile_info_failed => '情報更新失敗';

  @override
  String get apply_title => '検品員資格申請';

  @override
  String get apply_nick => 'ニックネーム';

  @override
  String get apply_sex => '性別';

  @override
  String get apply_birthday => '誕生日';

  @override
  String get apply_education => '学歴';

  @override
  String get apply_address => '居住地';

  @override
  String get apply_price => '最低検品料金';

  @override
  String get apply_shebao => '社会保険';

  @override
  String get apply_id_card => '身分証番号';

  @override
  String get apply_file => '履歴書を編集';

  @override
  String get apply_file_tip => '基本情報と経歴を入力してください';

  @override
  String get apply_upload_file => '履歴書をアップロード';

  @override
  String get apply_upload_file_failed => '履歴書のアップロードに失敗しました';

  @override
  String get apply_upload_card => '身分証写真をアップロード';

  @override
  String get apply_card_front => '表面写真（顔面）';

  @override
  String get apply_card_back => '裏面写真（国章面）';

  @override
  String get apply_submit => '提出';

  @override
  String get apply_enter => '入力してください';

  @override
  String get apply_next_tip => '情報を確認し、完成させてから提出してください';

  @override
  String get apply_auth_failed => '身分証認証に失敗しました。正しい写真をアップロードしてください';

  @override
  String get apply_checking => '身分証審査待ち';

  @override
  String get apply_check_success => '身分証審査通過';

  @override
  String get apply_check_failed => '審査失敗。内容を修正して再提出してください';

  @override
  String get order_title => '私の注文';

  @override
  String get order_input => '私の検品';

  @override
  String get order_output => '注文を発行';

  @override
  String get order_all => 'すべて';

  @override
  String get order_wait_pay => '支払い待ち';

  @override
  String get order_cancelled => 'キャンセル済み';

  @override
  String get order_status => '注文状態';

  @override
  String get order_status_unknown => '不明';

  @override
  String get order_refund_pending => '返金審査待ち';

  @override
  String get order_cancelled_refund_pending => 'キャンセル済み、返金審査待ち';

  @override
  String get order_refund_partial => '一部返金';

  @override
  String get order_refund_denied => '返金拒否';

  @override
  String get order_wait_dispatch => '割り当て待ち';

  @override
  String get order_ready_inspect => '検品準備中';

  @override
  String get order_need_pay => '支払い';

  @override
  String get order_wait => '検品準備中';

  @override
  String get order_confirm => '割り当て確認';

  @override
  String get order_doing => '検品中';

  @override
  String get order_comment => '評価待ち';

  @override
  String get order_finished => '完了';

  @override
  String get order_goods_info => '製品情報';

  @override
  String get order_goods_name => '製品名';

  @override
  String get order_goods_model => '製品モデル';

  @override
  String get order_goods_count => '数量';

  @override
  String get order_goods_unit => '単位';

  @override
  String get order_order_time => '注文時間';

  @override
  String get order_order_amount => '注文金額';

  @override
  String get order_detail_title => '注文詳細';

  @override
  String get order_applying => '申請済み';

  @override
  String get order_apply_expired => '期限切れ';

  @override
  String get order_apply_dispatched => '割り当て済み';

  @override
  String get order_create_time => '注文時間';

  @override
  String get order_look => '検品報告書を確認';

  @override
  String get order_report_next => '検品報告書を提出';

  @override
  String get order_detail_inspection_info => '検品情報';

  @override
  String get order_inspection_status_unpaid => '未払い';

  @override
  String get order_inspection_status_returned => '返品済み';

  @override
  String get order_inspection_status_waiting_start => '開始待ち';

  @override
  String get order_detail_related_info => '関連サブオーダー';

  @override
  String get order_detail_inspection_product => '製品名';

  @override
  String get order_detail_inspection_time => '検品時間';

  @override
  String get order_detail_inspection_city => '検品都市';

  @override
  String get order_detail_inspection_factory => '検品工場';

  @override
  String get order_detail_inspection_address => '検品住所';

  @override
  String get order_detail_inspection_person => '連絡担当者';

  @override
  String get order_detail_inspection_phone => '連絡担当者電話';

  @override
  String get order_detail_inspection_email => '連絡担当者メール';

  @override
  String get order_detail_inspection_amount => '価格情報';

  @override
  String get order_detail_inspection_sample => 'サンプル';

  @override
  String get order_detail_inspection_standard => 'サンプリングレベル';

  @override
  String get order_detail_inspection_critical => 'AQL';

  @override
  String get order_detail_inspection_major => 'Major';

  @override
  String get order_detail_inspection_minor => 'Minor';

  @override
  String get order_detail_inspection_day => '検品日数';

  @override
  String get order_detail_inspection_number => '検品人数';

  @override
  String get order_detail_inspection_price => '検品価格';

  @override
  String get order_detail_inspection_price_apply => '申請価格';

  @override
  String get order_detail_inspection_template => '検品テンプレート';

  @override
  String get order_detail_inspection_file => '添付ファイル';

  @override
  String get order_detail_inspection_image => '画像';

  @override
  String get order_detail_inspection_type => '検品タイプ';

  @override
  String get order_tips => 'サンプルと注意事項';

  @override
  String get order_apply => '検品を申請';

  @override
  String get order_cancel => 'キャンセル';

  @override
  String get order_canceled => 'キャンセル済み';

  @override
  String get order_dispatch_accept => '同意';

  @override
  String get order_dispatch_refuse => '拒否';

  @override
  String get order_inspection => '検品開始';

  @override
  String get order_sure_cancel => '注文をキャンセルしますか';

  @override
  String get order_sure_refuse => 'この注文を拒否しますか？';

  @override
  String get order_sure_confirm => 'この注文を受け入れますか？';

  @override
  String get publish_title => '注文を発行';

  @override
  String get publish_edit_title => '注文を編集';

  @override
  String get publish_welcome => 'ご注文いただき、ありがとうございます。価格は自由に設定でき、検品員と自由にコミュニケーションを取ることができます';

  @override
  String get publish_sampling => 'サンプリング検査';

  @override
  String get publish_sampling_content => '製品が100％完成し、少なくとも80％の製品が梱包済みで、出荷準備ができている場合に適用されます。国際サンプリング計画ANSI/ASQZ1.4（MIL-STD-105E）に基づき、お客様の特別な要求を参考にしてランダムサンプリング検査を行います。サンプリング検査報告書では、製品の完成数量、梱包状況、およびAQL（受入品質水準）要求を満たしているかどうかを総合的に反映し、出荷前に全ロットの製品品質を総合的に理解し、注文にリスクが生じるのを防ぎます';

  @override
  String get publish_point => '検査ポイント';

  @override
  String get publish_sampling_point => '● 顧客資料/サンプル照合\n● 完成数量確認\n● 製品サイズ、スタイル、色の確認\n● 外観品質検査\n● 製品機能と安全性テスト\n● 箱マーク確認\n● 梱包の完全性\n● 具体的な梱包詳細\n● 顧客特別要求';

  @override
  String get publish_all => '全数検査';

  @override
  String get publish_all_content => '全数検査は梱包前または梱包後に行うことができます。顧客の要求に応じて、各製品の外観、サイズ、品質、機能、安全性などを検査し、良品と不良品を区別し、検査結果を速やかに顧客に報告します';

  @override
  String get publish_online => 'オンライン検査';

  @override
  String get publish_online_content => 'オンライン検査は生産過程中または全ての生産が完了し梱包する前に行われます。製品の品質、機能、外観、およびその他の要素が生産プロセス全体を通じてお客様の仕様要求と一致しているかどうかを適時確認し、同時に不適合点を早期に発見し、工場の納期遅延リスクを低減するのに役立ちます';

  @override
  String get publish_online_point => '● 生産状況のフォローアップ\n● 生産ライン評価と生産進捗確認\n● 半製品と完成品のサンプル検査\n● 梱包情報と梱包材料の確認\n● 欠陥のある製品の改善\n● 納期の評価';

  @override
  String get publish_factory => '工場監査';

  @override
  String get publish_factory_content => '工場監査は主に客観的判断法を採用し、事前に設定された基準や規準に基づいて工場を定量的に評価・審査し、現場での採点評価および工場の総合審査結果などに基づいて評価報告書を作成し、顧客がその工場を適格なサプライヤーとして判断するための根拠を提供します';

  @override
  String get publish_factory_point_title => '監査内容';

  @override
  String get publish_factory_point => '● 工場概要（基本情報）\n● 組織構造\n● 生産プロセス\n● 生産能力\n● 研究開発技術能力\n● 機械設備と施設';

  @override
  String get publish_factory_review => '総合評価';

  @override
  String get publish_factory_review_content => '● 各監査項目について、相互の重要性を考慮し、それぞれ異なるスコアを与え、監査調査表および実地調査のデータに基づいて資格評価表を作成します';

  @override
  String get publish_watch => 'コンテナ積込み監督';

  @override
  String get publish_watch_content => 'コンテナ積込み監督は主にコンテナの状態評価、製品情報の確認、積込み箱数のカウント、梱包情報の確認、および積込み全過程の監督を含みます。積込み後の商品の入れ替えリスクを低減するため、検査員が現場で監督し、お支払いいただいた製品が安全に積み込まれることを確認します';

  @override
  String get publish_watch_point => '● コンテナ番号とトラック番号の記録\n● コンテナの損傷、湿気、異臭の有無を確認し、空のコンテナを撮影\n● 積込み予定の箱数と外装状態を確認し、ランダムにいくつかの箱を抽出して実際に積み込まれる製品を確認\n● 積込み過程を監督し、損傷を最小限に抑え、スペースの利用を最大化\n● コンテナのシール状態、コンテナのシール番号、パッキングリストを撮影して記録し、コンテナの出発時間を記録';

  @override
  String get publish_watch_inspection => '検品+積込み監督';

  @override
  String get publish_watch_inspection_content => '製品の最終品質と完成状況を確認するため、出荷前に国際サンプリング計画に基づいて完成品からランダムにサンプルを抽出してサンプル検査を行い、顧客提供の資料と照合し、コンテナ積込み全過程を全面的に監督します';

  @override
  String get publish_watch_inspection_point => '● コンテナ到着前に顧客資料/サンプルとの照合、製品の外観品質、機能、安全性、および製品梱包、箱マークなどのサンプル検査\n● 不良品を発見した場合、速やかに工場と連絡を取り、交換または再加工\n● コンテナの損傷、湿気、異臭の有無を確認し、空のコンテナを撮影\n● 積込み過程を監督し、損傷を最小限に抑え、スペースの利用を最大化\n● コンテナのシール状態、コンテナのシール番号、パッキングリストを撮影して記録し、コンテナの出発時間を記録';

  @override
  String get publish_next => '次へ';

  @override
  String get publish_inspection_time => '検品時間';

  @override
  String get publish_inspection_time_selected => '検品時間を選択';

  @override
  String get publish_inspection_time_tip => '選択してください';

  @override
  String get publish_inspection_people => '検品人数';

  @override
  String get publish_people => '人';

  @override
  String get publish_day => '日';

  @override
  String get publish_inspection_factory => '検品工場';

  @override
  String get publish_factory_tips => '検品工場を入力';

  @override
  String get publish_address_book => 'アドレス帳';

  @override
  String get publish_goods_name => '製品名';

  @override
  String get publish_name_tips => '複数の製品がある場合は1〜2つの代表的な名前を入力してください';

  @override
  String get publish_po_tips => 'P.O番号を入力してください';

  @override
  String get publish_file_tips => '添付ファイルをアップロード';

  @override
  String get publish_camera => '写真をアップロード';

  @override
  String get publish_file => 'ファイルをアップロード';

  @override
  String get publish_purchase => '購入注文を発行';

  @override
  String get publish_inspection => '検品注文を発行';

  @override
  String get publish_factory_tip => 'まず検品住所などの情報を選択してください';

  @override
  String get publish_attention => '注意事項';

  @override
  String get publish_attention_tips => '検品員が重点的に注意すべき問題を入力してください';

  @override
  String get publish_stand_price => '固定価格';

  @override
  String get publish_click_price => '切り替え';

  @override
  String get publish_vip_price => 'VIP価格';

  @override
  String get publish_vip_tips => '人的フォローアップサービスなどを提供';

  @override
  String get publish_total => '合計';

  @override
  String get publish_submit => '提出';

  @override
  String get publish_only_price_failed => '固定価格の権限がありません';

  @override
  String get publish_price_tip => '価格を選択してください';

  @override
  String get publish_date_tips => '日付を選択してください';

  @override
  String get date_title => '検品日';

  @override
  String get date_save => '保存';

  @override
  String get address_title => '工場情報を編集';

  @override
  String get address_auto_tips => 'テキストを貼り付けるか入力し、「認識」をクリックすると工場名、氏名、電話番号、住所などが自動的に識別されます';

  @override
  String get address_paste => '貼り付け';

  @override
  String get address_ocr => '認識';

  @override
  String get address_name => '工場名';

  @override
  String get address_name_tip => '検品工場情報を入力してください';

  @override
  String get address_person => '連絡担当者';

  @override
  String get address_person_tip => '連絡担当者を入力してください';

  @override
  String get address_mobile => '携帯電話番号';

  @override
  String get address_mobile_tip => '携帯電話番号を入力してください';

  @override
  String get address_email => 'メールアドレス';

  @override
  String get address_email_tip => 'メールアドレスを入力してください';

  @override
  String get address_area => '都道府県市区';

  @override
  String get address_area_tip => '都道府県-市区を選択してください 〉';

  @override
  String get address_detail => '詳細住所';

  @override
  String get address_detail_tip => '通り、建物番号などの情報を入力してください';

  @override
  String get address_location => '位置情報';

  @override
  String get address_save_tip => 'アドレス帳に保存';

  @override
  String get address_clear => 'クリア';

  @override
  String get address_submit => '提出';

  @override
  String get address_recent => '最近使用した住所';

  @override
  String get address_more => 'その他の住所';

  @override
  String get address_list_title => '住所管理';

  @override
  String get address_insert => '住所を追加';

  @override
  String get address_delete => '削除';

  @override
  String get address_delete_result => '削除に失敗しました';

  @override
  String get address_edit => '編集';

  @override
  String get address_delete_tips => '住所を削除しますか？';

  @override
  String get address_detected_paste => '住所情報が検出されました。この住所を使用しますか';

  @override
  String get pay_title => '注文支払い';

  @override
  String get pay_time => '支払い残り時間';

  @override
  String get pay_paypal => 'PayPal';

  @override
  String get pay_zfb => 'Alipay';

  @override
  String get pay_usd => 'ドル口座';

  @override
  String get pay_rmb => '人民元口座';

  @override
  String get pay_pay => '支払う';

  @override
  String get pay_result_success => '支払い成功';

  @override
  String get pay_result_success_wait => '支払い成功、注文処理中。';

  @override
  String get pay_result_failed => '支払い失敗';

  @override
  String get pay_keep => '記帳可能';

  @override
  String get check_title => '検品報告書';

  @override
  String get check_picture => '検品写真';

  @override
  String get check_file => '廉潔承諾書';

  @override
  String get check_report => '廉潔承諾書+手書き報告書';

  @override
  String get check_draft => '下書き報告書';

  @override
  String get check_template => '報告書テンプレート';

  @override
  String get check_submit => '提出';

  @override
  String get check_hint => '画像の説明を入力してください';

  @override
  String get check_checking => '報告書審査中';

  @override
  String get check_check_success => '報告書審査通過';

  @override
  String get check_check_failed => '報告書審査失敗、内容を修正して再提出してください';

  @override
  String get review_title => 'サービス評価';

  @override
  String get review_next => '評価する';

  @override
  String get contact_bnt => '今すぐ連絡';

  @override
  String get review_score => 'サービスレベル';

  @override
  String get review_score1 => '失望';

  @override
  String get review_score2 => '不満';

  @override
  String get review_score3 => '普通';

  @override
  String get review_score4 => '満足';

  @override
  String get review_score5 => '驚喜';

  @override
  String get review_tips => '多角的に評価し、検品員の業務能力をさらに理解するのに役立ちます';

  @override
  String get review_picture => '画像';

  @override
  String get review_submit => '提出';

  @override
  String get setting_title => '設定';

  @override
  String get setting_address => '住所管理';

  @override
  String get setting_clear_cache => 'キャッシュをクリア';

  @override
  String get setting_clear_success => 'クリア成功';

  @override
  String get setting_about_us => '私たちについて';

  @override
  String get setting_receive_msg => 'メッセージ通知を受け取る';

  @override
  String get setting_version => 'バージョン番号';

  @override
  String get setting_check_update => '更新を確認';

  @override
  String get setting_login_out => 'ログアウト';

  @override
  String get setting_login_out_tips => 'ログアウトしますか？';

  @override
  String get setting_delete_account_tips => 'アカウントを削除しますか？';

  @override
  String get setting_policy_title => 'プライバシーポリシー承認提示';

  @override
  String get setting_policy_sub_title => '次のステップに進む前に、まず読んで同意してください';

  @override
  String get setting_privacy_policy => 'プライバシー契約';

  @override
  String get setting_user_agreement => 'ユーザー契約';

  @override
  String get setting_privacy_content => '検品オンラインAppをご利用いただきありがとうございます\n私たちはあなたのプライバシーと個人情報保護を非常に重視しています。本Appの使用中に、私たちはあなたの一部の個人情報を収集し使用します\n1. Appプライバシーポリシーに同意いただいた後、統合SDKの初期化作業を行います。デバイスのMACアドレス、IMSI、Android ID、IPアドレス、ハードウェアモデル、OSバージョン番号、一意のデバイス識別子（IMEIなど）、ネットワークデバイスのハードウェアアドレス（MAC）、ソフトウェアバージョン番号、ネットワークアクセス方法、タイプ、状態、ネットワーク品質データ、操作ログ、ハードウェアシリアル番号、サービスログ情報などを収集し、Appの正常なデータ統計とセキュリティリスク管理を確保します。\n2. あなたの同意なしに、第三者からあなたの情報を取得したり、共有したり、外部に提供したりすることはありません。\n3. あなたは個人情報にアクセス、修正、削除することができ、私たちも退会、苦情申し立ての方法を提供します。\n';

  @override
  String get setting_ihavereadandagreed => '読んで同意しました';

  @override
  String get setting_policy_tips2 => '注意深く読んで理解してください';

  @override
  String get wallet_title => 'ウォレット';

  @override
  String get wallet_bill => '請求書';

  @override
  String get wallet_rmb_account => '人民元口座';

  @override
  String get wallet_usd_account => 'ドル口座';

  @override
  String get wallet_account_heading => '出金口座と設定';

  @override
  String get wallet_bank => '銀行カード';

  @override
  String get wallet_wechat => 'WeChat';

  @override
  String get wallet_alipay => 'Alipay';

  @override
  String get wallet_charge => 'チャージ';

  @override
  String get wallet_cash => '出金';

  @override
  String get wallet_balance => '残高';

  @override
  String get wallet_default_account => 'デフォルト口座';

  @override
  String get wallet_set_default_account => 'デフォルト口座に設定';

  @override
  String get bill_title => '請求書';

  @override
  String get bill_out => '支出';

  @override
  String get bill_in => '収入';

  @override
  String get bill_month => '月';

  @override
  String get bill_fenxi => '収支分析';

  @override
  String get bill_unfreeze => '凍結解除';

  @override
  String get bill_all => '全ての請求書';

  @override
  String get bill_income => '収入';

  @override
  String get bill_outcome => '支出';

  @override
  String get bill_freeze => '凍結';

  @override
  String get bill_withdraw => '出金';

  @override
  String get bank_title => '私の銀行カード';

  @override
  String get bank_add => '銀行カードを追加';

  @override
  String get add_bank_title => '銀行カードを追加';

  @override
  String get add_bank_name => '銀行カード名';

  @override
  String get add_bank_branch => '支店名';

  @override
  String get add_bank_card => 'カード番号';

  @override
  String get add_bank_real_name => '氏名';

  @override
  String get add_bank_address => '開設支店住所';

  @override
  String bind_title(Object bind) {
    return '$bindを連携';
  }

  @override
  String get bind_account => 'アカウント';

  @override
  String get bind_image => '受取コード';

  @override
  String get bind_name => '氏名';

  @override
  String get bind_hint => '入力してください';

  @override
  String get charge_title => 'チャージ';

  @override
  String get charge_account => 'チャージ口座';

  @override
  String get charge_money => 'チャージ金額';

  @override
  String get charge_deposit_type_title => 'チャージ方法';

  @override
  String get charge_deposit_type_online => 'オンラインチャージ';

  @override
  String get charge_deposit_type_offline => 'オフライン振込';

  @override
  String get charge_offline_nopic_hint => 'チャージ証明をアップロードしてください';

  @override
  String get charge_upload_proof => '振込証明をアップロードしてください';

  @override
  String get withdraw_list_title => '出金履歴';

  @override
  String get withdraw_rmb => '人民元出金';

  @override
  String get withdraw_usd => 'ドル出金';

  @override
  String get withdraw_status_checking => '審査中';

  @override
  String get withdraw_status_approved => '審査通過';

  @override
  String get withdraw_status_denied => '審査不通過';

  @override
  String get withdraw_cash_status_unfinished => '未送金';

  @override
  String get withdraw_cash_status_done => '送金済み';

  @override
  String get withdraw_cash_status_refused => '却下';

  @override
  String get charge_hint => 'チャージ金額を入力してください';

  @override
  String get charge_submit => '確定';

  @override
  String get charge_rmb => '人民元チャージ';

  @override
  String get charge_usd => 'ドルチャージ';

  @override
  String get charge_history_title => 'チャージ履歴';

  @override
  String get cash_title => '出金';

  @override
  String get cash_account => '出金口座を選択';

  @override
  String get cash_money => '出金額';

  @override
  String get cash_invoice_money => '請求書金額';

  @override
  String get cash_invoice_money_hint => '請求書金額を入力してください';

  @override
  String get cash_invoice_upload => '請求書をアップロード';

  @override
  String get cash_account_list_title => '申請が承認された後、出金はこれらの口座にランダムに振り込まれます：';

  @override
  String get cash_hint => '出金額を入力してください';

  @override
  String get cash_withdraw_tips1 => 'あなたは';

  @override
  String get cash_withdraw_tips2 => 'に出金しています。出金額は';

  @override
  String get cash_amount => '入金額';

  @override
  String get cash_other => '手数料';

  @override
  String get cash_submit => '確定';

  @override
  String get location_permission => '位置情報の権限が必要です。有効にしてください';

  @override
  String get location_cancel => 'キャンセル';

  @override
  String get location_author => '許可する';

  @override
  String get group_title => 'グループメンバー';

  @override
  String get unknown_error => '不明なエラー';

  @override
  String get data_parsing_exception => 'データ解析エラー';

  @override
  String get edit => '編集';

  @override
  String get no_data => 'データなし';

  @override
  String get note => '備考説明';

  @override
  String get msg_locating => '位置情報取得中';

  @override
  String get failed_to_download => '更新のダウンロードに失敗しました';

  @override
  String get pick_address => '工場の住所を入力するにはタップしてください';

  @override
  String get update_now => '今すぐ更新';

  @override
  String get message => 'メッセージ';

  @override
  String get view_order => '注文を表示';

  @override
  String get today => '今日';

  @override
  String get yesterday => '昨日';

  @override
  String get send_file => 'ファイルを送信';

  @override
  String get login_expired => 'ログインの有効期限が切れました。再度ログインしてください';

  @override
  String get exit_group_chat_confirm => 'グループチャットを退出しますか？';

  @override
  String get exit_group_chat_success => 'グループチャットを退出しました';

  @override
  String get exit_group_chat_page_title => 'チャット情報';

  @override
  String get exit_group_chat_button_title => 'グループチャットを退出';

  @override
  String get group_chat_setting_view_more => 'さらにグループメンバーを表示';

  @override
  String get group_chat_setting_name => 'グループチャット名';

  @override
  String get group_chat_setting_owner_update => 'グループ管理者のみがグループ名を変更できます';

  @override
  String get group_chat_name_page_title => 'グループチャット名を変更';

  @override
  String get group_chat_name_page_required => 'グループチャット名を入力してください';

  @override
  String get group_chat_name_save => '保存';

  @override
  String get group_chat_name_saved => 'グループチャット名が変更されました';

  @override
  String get conversation_manage_view_please => '操作するセッションを選択してください';

  @override
  String get conversation_manage_view_list => '会話リスト';

  @override
  String get group_manage_select => '操作するグループを選択してください';

  @override
  String get group_manage_list => 'グループリスト';

  @override
  String get please_enter => '入力してください';

  @override
  String get address_keyword => '住所のキーワードを入力してください';

  @override
  String get inspector_min_fee => '最低検品料金を入力してください';

  @override
  String get inspector_id_card_required => '身分証番号を入力してください';

  @override
  String get inspector_id_card_upload => '身分証写真をアップロードしてください';

  @override
  String get inspector_id_card_upload_fail => '身分証写真のアップロードに失敗しました。再度アップロードしてください';

  @override
  String get inspector_revoke => '検品員資格を取り消しますか？';

  @override
  String get inspector_revoke_completed => '検品員資格を取り消しました';

  @override
  String get male => '男性';

  @override
  String get female => '女性';

  @override
  String get elementary => '小学校';

  @override
  String get junior => '中学校';

  @override
  String get technical => '専門学校';

  @override
  String get senior => '高等学校';

  @override
  String get college => '短期大学';

  @override
  String get bachelor => '学士';

  @override
  String get master => '修士';

  @override
  String get doctor => '博士';

  @override
  String get yes => 'はい';

  @override
  String get no => 'いいえ';

  @override
  String get upload_image => '画像をアップロード';

  @override
  String get upload_file => 'ファイルをアップロード';

  @override
  String get revoke_inspector => '検品員資格を取り消す';

  @override
  String get deposit_card => '普通預金';

  @override
  String get withdrawal_balance => '出金額は口座残高を超えることはできません';

  @override
  String get failed_get_payment_info => '支払い情報の取得に失敗しました';

  @override
  String get recommended_order => '注文をおすすめ';

  @override
  String get withdrawal_method => '少なくとも1つの出金方法を提供してください';

  @override
  String get withdrawal_bind_alipay => '先にAlipayを連携してください';

  @override
  String get enabled_camera => 'カメラの使用を許可してください';

  @override
  String get valid_email_mobile => '正しいメールアドレスまたは電話番号を入力してください';

  @override
  String get apple_map => 'Appleマップ';

  @override
  String get baidu_map => 'Baiduマップ';

  @override
  String get amap => '高徳マップ';

  @override
  String get google_map => 'Googleマップ';

  @override
  String get tencent_map => 'テンセントマップ';

  @override
  String get image_format => '画像形式はpng、jpg、jpegである必要があります';

  @override
  String get enable_location_service => '位置情報の権限を有効にする必要があります';

  @override
  String get enable_location_service_tips => '位置情報の権限を有効にすると、周辺の検品注文をより正確に検索できます';

  @override
  String get enable_permission_not_now => '今はしない';

  @override
  String get enable_permission_goto_setting => '設定に移動';

  @override
  String get failed_location_service => '位置情報の取得に失敗しました';

  @override
  String get turn_on_location_service => '携帯電話の位置情報サービスをオンにしてください';

  @override
  String get no_install_map => 'マップアプリがインストールされていません';

  @override
  String get camera => 'カメラ';

  @override
  String get photo_album => 'フォトアルバム';

  @override
  String get new_version => '新バージョンがリリースされました';

  @override
  String get invalid_mail => 'ユーザーメールが存在しません';

  @override
  String get invalid_password => 'パスワードが間違っています';

  @override
  String get invalid_mobile => '電話番号が存在しません';

  @override
  String get invalid_auth_code => '認証コードが正しくありません';

  @override
  String get invalid_login => 'ログインに失敗しました。再試行してください';

  @override
  String get grabbing => '注文獲得中';

  @override
  String get hour_ago => '時間前に公開';

  @override
  String get minute_ago => '分前に公開';

  @override
  String get report_type => '報告書タイプ';

  @override
  String get fri => 'サンプリング検査';

  @override
  String get fui => '';

  @override
  String get oli => '';

  @override
  String get fat => '';

  @override
  String get cls => 'コンテナ監督';

  @override
  String get fri_cls => '';

  @override
  String get order_payment => '注文支払い';

  @override
  String get order_refund => '注文返金';

  @override
  String get expend_withdrawal => '支出-出金';

  @override
  String get incoming_refund => '収入-注文返金';

  @override
  String get incoming_recharge => '収入-チャージ';

  @override
  String get chat_not_member => 'あなたはもうグループメンバーではないため、メッセージを送信できません';

  @override
  String get admins => 'カスタマーサポートに連絡';

  @override
  String get theme_title => 'テーマ';

  @override
  String get theme_light => 'ライトテーマ';

  @override
  String get theme_dark => 'ダークテーマ';

  @override
  String get theme_auto => 'システムに従う';

  @override
  String get amount_total => '合計';

  @override
  String get amount_available => '利用可能';

  @override
  String get amount_blocked => '凍結';

  @override
  String get download => 'ダウンロードをクリック';

  @override
  String get downloading => 'ダウンロード中';

  @override
  String get saved => '保存しました';

  @override
  String get order_number => '注文番号';

  @override
  String get order_detail_inspection_cost => '検品費用';

  @override
  String get delete_account => 'アカウントを削除';

  @override
  String get delete_account_confirm => '全ての情報が保持されません。\n削除しますか？';

  @override
  String get delete_account_result => 'アカウントが削除されました';

  @override
  String get not_exist_account => 'アカウントが存在しません';

  @override
  String get new_password => '新しいパスワードを入力';

  @override
  String get supervisor => 'フォローアップ担当者';

  @override
  String get downloadFiles => 'ダウンロードしたファイル';

  @override
  String get home_search_hint_inspector => '都市/製品名で注文を検索';

  @override
  String get home_search_hint_admin => '都市/製品名で注文を検索';

  @override
  String get search_recent_history => '最近の検索';

  @override
  String get assign => '割り当て';

  @override
  String get assigned => '割り当て済み';

  @override
  String get approve => '承認';

  @override
  String get assign_inspector => '検品員を割り当て';

  @override
  String get unassigned => '未割り当て';

  @override
  String get general_all => 'すべて';

  @override
  String get general_date => '日付';

  @override
  String get general_desc => '説明';

  @override
  String get general_amount => '金額';

  @override
  String get assign_search_hint => 'ニックネーム/氏名/メール/電話番号を入力してください';

  @override
  String get assign_cancel_message => 'この検品員の割り当てをキャンセルしますか';

  @override
  String get assign_inspect_times => '検品回数';

  @override
  String get assign_leave_message_batch => '一括メッセージ';

  @override
  String get assign_price_zero_tips => '検品費用は0にできません';

  @override
  String get assign_applied => '申請済み';

  @override
  String get is_auth_forbidden => 'Forbidden';

  @override
  String get apply_time => '申請日時';

  @override
  String get assign_message => 'メッセージ';

  @override
  String get chat_send_message => 'メッセージを送信';

  @override
  String get chat_send_order => '注文を送信';

  @override
  String get chat_panel_album => 'アルバム';

  @override
  String get chat_panel_camera => 'カメラ';

  @override
  String get chat_panel_file => 'ファイル';

  @override
  String get chat_toolbar_custom_service => '専属カスタマーサービス';

  @override
  String get chat_toolbar_submit_order => '検品注文';

  @override
  String get home_navigation => 'ナビゲーションをクリック';

  @override
  String get price_input_error_zero => '注文は0元から100万元の間である必要があります';

  @override
  String get filter_all => '全てのフィルター';

  @override
  String get filter_heading_order_status => '注文状態で';

  @override
  String get filter_heading_insp_date => '検品日で';

  @override
  String get filter_heading_order_date => '発行日で';

  @override
  String get filter_heading_area => '地域で';

  @override
  String get filter_date_start => '開始日';

  @override
  String get filter_date_end => '終了日';

  @override
  String get filter_date_today => '今日の注文';

  @override
  String get filter_date_tomorrow => '明日の注文';

  @override
  String get filter_date_2days_later => '2日以内の注文';

  @override
  String get filter_date_3days_later => '3日以内の注文';

  @override
  String get sort_by_order_date => '注文日でソート';

  @override
  String get sort_by_insp_date => '検品日でソート';

  @override
  String get sort_by_distance => '距離でソート';

  @override
  String get purchase_all_replies => '全ての返信';

  @override
  String get purchase_replies_count => '件の返信';

  @override
  String get purchase_no_more_replies => 'これ以上の返信はありません';

  @override
  String get purchase_save_draft_title => '下書きを保存しますか';

  @override
  String get purchase_save_draft_choice => '下書きとして保存';

  @override
  String get purchase_save_draft_quit => '直接終了';

  @override
  String get purchase_search_hint => '購入注文を検索';

  @override
  String get purchase_reply_hint => '投稿への返信内容';

  @override
  String get purchase_reply_reason_hint => 'Reason why recommend';

  @override
  String get purchase_complaint_hint => 'より多くの情報を提供すると、報告がより迅速に処理されます';

  @override
  String get purchase_reply_paid_hint => '懸賞内容を入力';

  @override
  String get purchase_edit => '投稿を編集';

  @override
  String get purchase_publish => '懸賞購入';

  @override
  String get purchase_publish_product_label => '製品名';

  @override
  String get purchase_publish_title_label => '懸賞タイトル';

  @override
  String get purchase_publish_quantity => 'Quantity';

  @override
  String get purchase_publish_content_label => '詳細説明';

  @override
  String get purchase_publish_product_hint => '製品名を入力してください';

  @override
  String get purchase_publish_title_hint => '名称、モデル、地域、数量などの情報を記入できます';

  @override
  String get end_date => 'End date';

  @override
  String get purchase_area => 'Area';

  @override
  String get purchase_permission_author_only => 'Only author of post can see replies';

  @override
  String get purchase_publish_quantity_hint => 'Please input quantity';

  @override
  String get purchase_publish_content_hint => '詳細説明を入力してください';

  @override
  String get purchase_publish_price => '懸賞価格';

  @override
  String get purchase_publish_choose_category => 'カテゴリを選択';

  @override
  String get purchase_publish_choose_category_hint => 'カテゴリを選択してください';

  @override
  String get purchase_paid_publish_switch => '有料表示';

  @override
  String get purchase_paid_publish_set_price => '価格を設定';

  @override
  String get purchase_detail_response_all => '全ての返信';

  @override
  String get purchase_detail_response_author_only => '投稿者のみ表示';

  @override
  String get purchase_detail_response_asc => '昇順';

  @override
  String get purchase_detail_response_desc => '降順';

  @override
  String get purchase_detail_more_reply => '返信';

  @override
  String get purchase_detail_more_up => 'いいね';

  @override
  String get purchase_detail_more_cancel_up => 'いいねを取り消す';

  @override
  String get purchase_my_posts => '投稿';

  @override
  String get purchase_my_replies => '返信';

  @override
  String get purchase_my_appeals => '申し立て';

  @override
  String get purchase_appeal_detail => '申し立て詳細';

  @override
  String get purchase_appeal_submit => '申し立てを提出';

  @override
  String get purchase_appeal_cancel => '申し立てをキャンセル';

  @override
  String get purchase_appeal_approve => '申し立てが承認されました';

  @override
  String get purchase_appeal_denied => '申し立てが却下されました';

  @override
  String get purchase_paid_content_owner_tips => '有料コンテンツ';

  @override
  String get purchase_paid_content_tips => '有料コンテンツ、支払いで閲覧可能';

  @override
  String get purchase_paid_content_paid_tips => '有料コンテンツがロック解除されました';

  @override
  String get purchase_review_leave => '評価する';

  @override
  String get purchase_review_my_score => '私の評価';

  @override
  String get purchase_my_replies_original_header => '元の投稿';

  @override
  String get purchase_publish_bounty_tips => '注：懸賞金額は自由に選択できます。高い懸賞金額はより多くの検品員があなたの必要な情報を積極的に提供するよう促します。';

  @override
  String get purchase_reply_to => '返信する';

  @override
  String get purchase_modify_bounty => '懸賞を修正';

  @override
  String get purchase_bounty_money => '懸賞金';

  @override
  String get purchase_evaluated_person => '人が評価済み';

  @override
  String get purchase_comment_paid_supplier => 'サプライヤー';

  @override
  String get purchase_comment_paid_contact => '連絡担当者';

  @override
  String get purchase_comment_paid_phone => '連絡先電話';

  @override
  String get purchase_comment_paid_email => 'メールアドレス';

  @override
  String get purchase_comment_paid_address => '工場住所';

  @override
  String get purchase_comment_paid_other => 'その他';

  @override
  String get purchase_comment_paid_low_price => '製品最低価格';

  @override
  String get purchase_appeal_title => '返金申請';

  @override
  String get purchase_appeal_reason => '申し立て理由を入力してください';

  @override
  String get purchase_appeal_request_price => '申し立て金額：';

  @override
  String get purchase_appeal_request_reason => '申し立て理由：';

  @override
  String get purchase_post_status_draft => '下書き';

  @override
  String get purchase_post_status_reviewing => '審査中';

  @override
  String get purchase_post_status_published => '審査通過';

  @override
  String get purchase_post_status_denied => '未通過';

  @override
  String get purchase_post_publish => '投稿を公開';

  @override
  String get purchase_complaint_type_leading => '報告タイプを選択してください';

  @override
  String get purchase_complaint_type_1 => 'ポルノグラフィーや卑猥な内容';

  @override
  String get purchase_complaint_type_2 => 'スパム広告';

  @override
  String get purchase_complaint_type_3 => '誹謗中傷';

  @override
  String get purchase_complaint_type_4 => '違法行為';

  @override
  String get purchase_complaint_type_5 => '政治的な虚偽情報';

  @override
  String get purchase_complaint_type_6 => '権利侵害';

  @override
  String get purchase_complaint_type_7 => 'その他';

  @override
  String get shop_goods_detail_title => '商品詳細';

  @override
  String get mall_buy_immediate => '今すぐ購入';

  @override
  String get mall_goods_count => '数量';

  @override
  String get mall_confirm_pay => '支払いを確認';

  @override
  String get mall_order_confirm => '注文確認';

  @override
  String get mall_submit_order => '注文を提出';

  @override
  String get mall_goods_price => '商品金額';

  @override
  String get mall_express_price => '配送料';

  @override
  String get mall_price_total => '合計：';

  @override
  String get mall_payment => '支払い';

  @override
  String get mall_payment_methods => '支払い方法';

  @override
  String get mall_pay_succeed => '支払い成功';

  @override
  String get mall_check_order_detail => '注文詳細を確認';

  @override
  String get mall_order_remark => '注文備考';

  @override
  String get mall_order_remark_input => '備考を入力';

  @override
  String get purchase_detail_more_report => '報告';

  @override
  String get purchase_reply_paid_content_tips => '注：正確な情報を入力してください。有料の返信は投稿後に審査が必要で、審査後に他のユーザーが閲覧できるようになります';

  @override
  String get public_ip_address => 'IPの所在地:';

  @override
  String get inspection_widget_suit_tips => '検品時には身分証やユニフォームの着用が必要です。お持ちでない場合はホームページから購入できます';

  @override
  String get purchase_paid_content_appeal => '申し立て';

  @override
  String get report_success => '報告成功';

  @override
  String tabbar_tab_names(String names) {
    String _temp0 = intl.Intl.selectLogic(
      names,
      {
        'tab_home': 'ホーム',
        'tab_shortcut': 'ショートカット',
        'tab_message': 'メッセージ',
        'tab_mine': 'マイページ',
        'tab_publish': '',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String order_status_desc(String status) {
    String _temp0 = intl.Intl.selectLogic(
      status,
      {
        'order_status_unknown': '不明',
        'order_wait_pay': '支払い待ち',
        'order_cancelled': 'キャンセル済み',
        'order_cancelled_refund_pending': 'キャンセル済み,返金審査待ち',
        'order_refund_pending': '返金審査待ち',
        'order_refund_partial': '一部返金',
        'order_refund_denied': '返金拒否',
        'order_wait_dispatch': '割り当て待ち',
        'order_doing': '検品中',
        'order_finished': '完了',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String pay_type(String type) {
    String _temp0 = intl.Intl.selectLogic(
      type,
      {
        'pay_rmb': '人民元口座',
        'pay_usd': 'ドル口座',
        'pay_zfb': 'Alipay',
        'pay_paypal': 'PayPal',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String purchase_complaint_type(String type) {
    String _temp0 = intl.Intl.selectLogic(
      type,
      {
        'type_1': 'ポルノグラフィーや卑猥な内容',
        'type_2': 'スパム広告',
        'type_3': '誹謗中傷',
        'type_4': '違法行為',
        'type_5': '政治的な虚偽情報',
        'type_6': '権利侵害',
        'type_7': 'その他',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String get purchase_reply_paid_my_evaluate => '（私の評価）';

  @override
  String get bind_now => '今すぐ連携';

  @override
  String get cancel_register => '登録をキャンセル';

  @override
  String register_and_bind_email0(Object email) {
    return '$emailを主要メールアドレスとして登録し連携する';
  }

  @override
  String register_and_bind_email1(Object email) {
    return 'あなたのメールアドレス$emailは既に登録されています。連携を続行';
  }

  @override
  String get register_and_bind_email2 => '主要メールアドレスを登録し連携する';

  @override
  String get new_register_bind_title => '主要ログインメールアドレスを設定して登録';

  @override
  String get new_register_bind_field_account_tips => 'メールアドレス/電話番号を連携';

  @override
  String get register => '登録';

  @override
  String get switch_account => 'アカウントを切り替える';

  @override
  String get switch_account_confirm_tips => 'アカウントを切り替えますか？';

  @override
  String get password_must_have => 'パスワードには以下が含まれる必要があります：';

  @override
  String get password_must_have_1 => '8〜32文字の長さ';

  @override
  String get password_must_have_2 => '1つの小文字 (a-z)';

  @override
  String get password_must_have_3 => '1つの数字';

  @override
  String get password_must_have_4 => '1つの記号 (例：!@#\$%^&*)';

  @override
  String get password_login => 'ログインパスワードを入力';

  @override
  String get password_login_again => '新しいパスワードを再入力';

  @override
  String get choose_account_to_login => 'アカウントを選択してクイックログイン';

  @override
  String get finish => '完了';

  @override
  String get done => '完了';

  @override
  String get account_apple => 'Appleログイン';

  @override
  String get account_google => 'Googleログイン';

  @override
  String get account_wechat => 'WeChatログイン';

  @override
  String get account_facebook => 'Facebookログイン';

  @override
  String get third_account_unbind => '連携解除';

  @override
  String get third_account_bind => '連携';

  @override
  String get confirm_unbind => '連携を解除しますか？';

  @override
  String get inspection_requirement => '検品注意事項';

  @override
  String get liveroom_entrance => 'ライブルーム注文';

  @override
  String get add_account => 'アカウントを追加';

  @override
  String get message_order => 'Orders';

  @override
  String get message_email => 'Email';

  @override
  String get message_wallet => 'Wallet';

  @override
  String get message_user => 'User info';

  @override
  String get salesman => 'Salesman';

  @override
  String get public_continue => 'Continue';

  @override
  String get camera_permission_tips => 'In order to facilitate you to upload pictures, files or photos when using the App, you need to allow the use of camera and storage permissions.';

  @override
  String get ai_category_inspector => '検査';

  @override
  String get ai_nothing_category => '検査対象なし';

  @override
  String get ai_category_name => '検査項目名';

  @override
  String get ai_quantity => '数量';

  @override
  String get ai_packaging => 'パッキング';

  @override
  String get ai_shipping_mark => '輸送マーク';

  @override
  String get ai_product_style => '商品スタイル';

  @override
  String get ai_test => 'テスト';

  @override
  String get ai_craftsmanship => '製造技術';

  @override
  String get ai_test_verification => 'テスト確認';

  @override
  String get ai_category_measure => '測定項目';

  @override
  String get ai_spare_parts => 'スペアパーツ';

  @override
  String get ai_sampling_number => 'サンプリング番号';

  @override
  String ai_input_range_number(Object range) {
    return '$range内に数字を入力してください';
  }

  @override
  String ai_enter_range_number(Object range) {
    return '$range内に数字を入力してください';
  }

  @override
  String get ai_selected => '選択済み';

  @override
  String get ai_selected_status => '選択状態';

  @override
  String get ai_order_quantity => '注文数量';

  @override
  String get ai_packaged_boxes_quantity => 'パック済み箱数量（製品）';

  @override
  String get ai_unpackaged_boxes_quantity => '未パック箱数量（製品）';

  @override
  String get ai_sample_from_packaged => 'パック済みからサンプリング';

  @override
  String get ai_sample_from_unpackaged => '未パックからサンプリング';

  @override
  String get ai_spare_parts_quantity => 'スペアパーツ数量';

  @override
  String get ai_sampling_packaging_number => 'サンプリングパッキング番号';

  @override
  String get ai_sampling_packaging_number_record => 'サンプリング番号記録';

  @override
  String get ai_sampling_packaging_number_list => 'サンプリング外箱番号の記録';

  @override
  String get ai_judgment => '判断';

  @override
  String get ai_judgment_item => '判断項目';

  @override
  String get ai_standard => '基準';

  @override
  String get ai_result => '結果';

  @override
  String get ai_conclusion => '結論';

  @override
  String get ai_overall_conclusion => '全体的な結論';

  @override
  String get ai_consistency => '一貫性';

  @override
  String get ai_yes => 'はい';

  @override
  String get ai_no => 'いいえ';

  @override
  String get ai_remarks => '備考';

  @override
  String get ai_numerical => '番号';

  @override
  String get ai_recommended_test_items => '推奨検査項目';

  @override
  String get ai_test_item => '検査項目';

  @override
  String get ai_add_all => 'すべて追加';

  @override
  String get ai_add_plus => '+ 追加';

  @override
  String get ai_add => '追加';

  @override
  String ai_confirm_delete(Object name) {
    return '$nameを削除しますか？';
  }

  @override
  String get ai_enter_test_item => '検査項目を入力してください';

  @override
  String get ai_defect_record => '欠陥記録';

  @override
  String get ai_defect_photo => '欠陥写真';

  @override
  String get ai_defect_description => '欠陥説明';

  @override
  String get ai_defect_level => '欠陥レベル';

  @override
  String get ai_found_quantity => '発見数量';

  @override
  String get ai_handling_method => '処理方法';

  @override
  String get ai_edit => '編集';

  @override
  String get ai_delete => '削除';

  @override
  String get ai_pick_out => '選別';

  @override
  String get ai_replace => '交換';

  @override
  String get ai_rework => '再作業';

  @override
  String get ai_edit_description => '説明の編集';

  @override
  String get ai_critical => '重大';

  @override
  String get ai_important => '重要';

  @override
  String get ai_minor => '軽微';

  @override
  String get ai_defect_list => '欠陥リスト';

  @override
  String get ai_test_level => 'テストレベル';

  @override
  String get ai_sampling_sample => 'サンプリングサンプル';

  @override
  String get ai_sampling_level => 'サンプリングレベル';

  @override
  String get ai_additional_information => '補足情報';

  @override
  String get ai_inspection_record => '検査記録';

  @override
  String get ai_sample_count => 'サンプル数';

  @override
  String get ai_maximum_allowable_value => '許容最大値';

  @override
  String get ai_test_item_name => 'テスト項目名';

  @override
  String get ai_test_result => 'テスト結果';

  @override
  String get ai_basic_information => '基本情報';

  @override
  String get ai_new_test_item => '新しいテスト項目';

  @override
  String get ai_test_project => 'テストプロジェクト';

  @override
  String get ai_measurement_project => '測定プロジェクト';

  @override
  String get ai_measure_need_num => '必要個数';

  @override
  String get ai_measurement_unit => '測定単位';

  @override
  String get ai_measurement_method => '測定方法';

  @override
  String get ai_measurement_record => '測定記録';

  @override
  String get ai_measured => '測定済み';

  @override
  String get ai_unit_of_measurement => '計量単位';

  @override
  String get ai_measured_value => '測定値';

  @override
  String get ai_product_number => '製品番号';

  @override
  String get ai_number => '番号';

  @override
  String get ai_new_measurement_item => '新しい測定項目';

  @override
  String get ai_length_width_height => '長さ幅高さ';

  @override
  String get ai_dimensions_length => '長さ';

  @override
  String get ai_dimensions_width => '幅';

  @override
  String get ai_dimensions_height => '高さ';

  @override
  String get ai_length_width => '長さ幅';

  @override
  String get ai_other => 'その他';

  @override
  String get ai_allowable_error => '許容誤差';

  @override
  String get ai_report_summary => 'レポート要約';

  @override
  String get ai_special_note => '特別な注意';

  @override
  String get ai_overall_conclusion_2 => '総合的な結論';

  @override
  String get ai_summary => '要約';

  @override
  String get ai_category_name_table => '検査項目名テーブル';

  @override
  String get ai_compliance => '適合性';

  @override
  String get ai_remarks_2 => '備考';

  @override
  String get ai_defect_summary => '欠陥要約';

  @override
  String get ai_no_guidance_instructions => '現在の指導指針はありません';

  @override
  String get ai_no_standard_instructions => '現在の基準指針はありません';

  @override
  String get ai_please_fill_in => 'ご記入ください';

  @override
  String ai_please_supplement_level_or_sample(Object level, Object sample) {
    return '$levelまたは$sampleを追加してください';
  }

  @override
  String get ai_please_add => '追加してください';

  @override
  String get ai_please_input => '入力してください';

  @override
  String get ai_please_select => '選択してください';

  @override
  String ai_name_not_filled(Object name) {
    return '$nameが入力されていません';
  }

  @override
  String get ai_addition_successful => '追加に成功しました';

  @override
  String get ai_confirm_action => '確定';

  @override
  String get ai_cancel_action => 'キャンセル';

  @override
  String get ai_submit => '提出';

  @override
  String get ai_next_item => '次の項目';

  @override
  String get ai_complete => '完了';

  @override
  String get ai_change_description => '説明の変更';

  @override
  String get ai_action_guidance_instructions => 'アクションガイドライン';

  @override
  String get ai_action_standard_instructions => 'アクション標準指針';

  @override
  String get ai_add_description => '説明を追加';

  @override
  String get ai_change_description_note => '注意：下記は既に発見された欠陥であり、これを修正すると歴史データも新しい説明に変わります！';

  @override
  String get ai_packing_completion_rate => 'パッキング完了率';

  @override
  String get ai_unprocessed_quantity => '未加工数量';

  @override
  String get ai_sample_level_type_0 => 'I級';

  @override
  String get ai_sample_level_type_1 => 'II級';

  @override
  String get ai_sample_level_type_2 => 'III級';

  @override
  String get ai_sample_level_type_s1 => 'S-1';

  @override
  String get ai_sample_level_type_s2 => 'S-2';

  @override
  String get ai_sample_level_type_s3 => 'S-3';

  @override
  String get ai_sample_level_type_s4 => 'S-4';

  @override
  String get ai_sample_level_type_nothing => 'なし';

  @override
  String get ai_inspection_image => '画像';

  @override
  String get ai_photo_confirm => '確認';

  @override
  String get ai_add_product_ask_save => '編集を保存しますか？';

  @override
  String get ai_add_product_save => '保存';

  @override
  String get ai_add_product_edit_model => 'モデル編集';

  @override
  String get ai_add_product_model_name => 'モデル名';

  @override
  String get ai_add_product_input_model => 'モデル名を入力してください';

  @override
  String get ai_add_product_num => '数量';

  @override
  String get ai_add_product_input_num => 'モデル数量を入力してください';

  @override
  String get ai_add_product_unit => '単位';

  @override
  String get ai_add_product_ask_delete => 'このモデルを削除しますか？';

  @override
  String get ai_add_product_edit_product => '製品編集';

  @override
  String get ai_add_product_product_name => '製品名';

  @override
  String get ai_add_product_model => 'モデル';

  @override
  String get ai_add_product_input_product_name => '製品名を入力してください';

  @override
  String get ai_add_product_new_model => '新しいモデル';

  @override
  String get ai_add_product_ask_product => 'この製品とすべてのモデルを削除しますか？';

  @override
  String get ai_add_product_picture_lost => '画像がありません';

  @override
  String ai_add_product_lost_des(Object lostStr) {
    return '現在$lostStrが不足しています。すべての情報を補完してから検査を行ってください。';
  }

  @override
  String get ai_add_product_model_full => '製品モデル名';

  @override
  String get ai_add_product_model_title => '製品モデル';

  @override
  String get ai_add_product_new => '製品を追加';

  @override
  String get ai_model_unit_piece => 'piece';

  @override
  String get ai_model_unit_only => 'piece';

  @override
  String get ai_model_unit_item => 'piece';

  @override
  String get ai_model_unit_pair => 'pair';

  @override
  String get ai_model_unit_set => 'set';

  @override
  String get ai_model_unit_dozen => 'dozen';

  @override
  String get ai_model_unit_roll => 'roll';

  @override
  String get ai_model_unit_vehicle => 'vehicle';

  @override
  String get ai_model_unit_head => 'head';

  @override
  String get ai_model_unit_bag => 'bag';

  @override
  String get ai_model_unit_box => 'box';

  @override
  String get ai_model_unit_pack => 'pack';

  @override
  String get ai_model_unit_yard => 'yard';

  @override
  String get ai_model_unit_meter => 'meter';

  @override
  String get ai_model_unit_kilogram => 'kilogram';

  @override
  String get ai_model_unit_metric_ton => 'metric ton';

  @override
  String get ai_model_unit_liter => 'liter';

  @override
  String get ai_model_unit_gallon => 'gallon';

  @override
  String get ai_model_unit_other => 'other';

  @override
  String get ai_default_config_des => '現在の製品には検査テンプレートがありません。下記のテンプレートを選択するか、（+86）に電話してテンプレートの設定を行ってください。';

  @override
  String get ai_default_config_category_all => 'カテゴリ（すべて）';

  @override
  String get ai_default_config_select_template => 'テンプレートを選択してください';

  @override
  String get ai_default_config_template_selection => 'テンプレート選択';

  @override
  String get ai_default_config_search_template => 'テンプレート検索';

  @override
  String get ai_default_config_classify => '分類';

  @override
  String get ai_default_config_preview => 'プレビュー';

  @override
  String get ai_default_config_use => '適用';

  @override
  String get ai_default_config_current_use_button => '本製品に適用';

  @override
  String get ai_default_config_more_use_button => 'より多くの製品に適用';

  @override
  String get ai_default_config_product_list => '製品リスト';

  @override
  String get ai_default_config_use_warning => 'Note: 【Mod】Product template already loaded; 【Ops】Template already configured by operations. Loading a new template will overwrite previous data.';

  @override
  String get ai_default_config_tag_default => '運';

  @override
  String get ai_default_config_tag_manual => '模';

  @override
  String get ai_default_config_load_progress => 'ロード進捗';

  @override
  String ai_default_config_template_progress(Object name) {
    return '$nameのテンプレートロード完了。';
  }

  @override
  String ai_default_config_template_fail_count(Object name) {
    return '$name個失敗、再試行をクリック';
  }

  @override
  String get ai_default_config_load => 'ロード';

  @override
  String get ai_default_config_success => '成功';

  @override
  String get ai_default_config_fail => '失敗';

  @override
  String get ai_default_config_template => 'template';

  @override
  String get ai_add_model_count_warning => 'The quantity must be greater than 0';

  @override
  String get ai_default_config_product_edit => '编辑产品';

  @override
  String get ai_wait => '待ちます';

  @override
  String get ai_product_info => '製品情報';

  @override
  String get ai_product_category => '製品カテゴリー';

  @override
  String get ai_product_unit => '製品単位';

  @override
  String get ai_package_num_done => '梱包済み箱数';

  @override
  String get ai_product_full => '追加情報';

  @override
  String get ai_product_full_tip => '注文の製品情報が不完全です。検査現場の実際の製品情報に基づいて補充してください。';

  @override
  String get ai_each_box => '箱ごと';

  @override
  String get ai_simple_count => 'サンプリング数';

  @override
  String get ai_simple_level => 'サンプリング基準';

  @override
  String get ai_simple_num => 'サンプリング数量';

  @override
  String get ai_simple_no => 'サンプリング箱番号';

  @override
  String get ai_simple_result => '結果判定';

  @override
  String get ai_simple_project => '検査項目';

  @override
  String get ai_simple_project_manage => '検査項目管理';

  @override
  String get ai_simple_project_edit => '検査項目編集';

  @override
  String get ai_simple_project_recmend => 'スマート推薦';

  @override
  String get ai_simple_project_input => '検査記録入力';

  @override
  String get ai_simple_help => 'ヘルプ';

  @override
  String get ai_simple_project_record => '検査記録';

  @override
  String get ai_simple_require => '顧客要求';

  @override
  String get ai_simple_record => '記録';

  @override
  String get ai_simple_dsec => '説明';

  @override
  String get ai_simple_before => '前の項目';

  @override
  String get ai_simple_add => 'グループ追加';

  @override
  String get ai_simple_add_desc => '写真に説明を追加';

  @override
  String get ai_simple_add_citations => '引用回数';

  @override
  String get ai_no_more => 'これ以上データなし';

  @override
  String get ai_wrong_tip => '数量は総数を超えられません';

  @override
  String get ai_defect_records => '欠陥記録';

  @override
  String get ai_check_require => 'サンプリング要求';

  @override
  String get ai_find_defect => '欠陥発見';

  @override
  String get ai_defect_question => '欠陥問題';

  @override
  String get ai_modify_level => 'サンプリングレベル変更';

  @override
  String get ai_defect_quick => '工程欠陥を迅速に追加';

  @override
  String get ai_defect_self => 'カスタム欠陥名';

  @override
  String get ai_defect_record_list => '欠陥記録リスト';

  @override
  String get ai_measure_require => '測定要求';

  @override
  String get ai_measurement_item => '測定項目';

  @override
  String get ai_measurement_error => '誤差';

  @override
  String get ai_measurement_standard => '測定基準';

  @override
  String get ai_measurement_value_standard => '標準値';

  @override
  String get ai_measurement_camera => '測定写真';

  @override
  String get ai_measurement_add => '測定基準を迅速に追加';

  @override
  String get ai_product_first => '製品メイン画像';

  @override
  String get ai_product_report => 'レポート生成';

  @override
  String get ai_product_report_tip => '製品メイン画像を選択してください';

  @override
  String get ai_product_report_special => '特記事項を入力';

  @override
  String get ai_product_report_sign => '署名';

  @override
  String get ai_product_report_sign_done => '署名完了';

  @override
  String get ai_defect_names => '欠陥名';

  @override
  String get ai_input_tip => '名前を入力してください';

  @override
  String get ai_add_measure_tip => 'まず測定基準を追加してください';

  @override
  String get ai_wrong_num => '数量エラー';

  @override
  String get ai_wrong_name => '製品名を入力してください';

  @override
  String get ai_wrong_sample_num => 'サンプリング数を超えられません';

  @override
  String get ai_per_box => '箱あたり数量';

  @override
  String get ai_wrong_sample_num_cal => '梱包サンプル+未梱包サンプルはサンプリング数と等しくなければなりません';

  @override
  String get ai_sure_delete => '削除しますか？';

  @override
  String get ai_choose_tip => '欠陥処理方法と数量を選択してください';

  @override
  String get ai_weight => '総重量';

  @override
  String get sampling_plan => 'サンプリング計画';

  @override
  String get single => '単一';

  @override
  String get normal => '通常';

  @override
  String get summarize => 'Summarize';

  @override
  String get po_number => 'PO番号';

  @override
  String get product_quantity => '製品数量';

  @override
  String get customer_name => '顧客名';

  @override
  String get supplier_name => 'サプライヤー名';

  @override
  String get inspection_date => '検品日';

  @override
  String get arrival_time => '到着時間';

  @override
  String get completion_time => '完了時間';

  @override
  String get inspection_address => '検査場所';

  @override
  String get inspector => '検査員';

  @override
  String get inspection_report_note => '本検査レポートは参考用です。最終的な合格可否は客先の確認を待つ必要があります。';

  @override
  String get remark_toast => 'Please fill in the remarks first';

  @override
  String get process_appearance_judgment => 'Process appearance judgment';

  @override
  String get test_validation_judgment => 'Test validation judgment';

  @override
  String check_save(Object name) {
    return 'Are you sure you want to save $name?';
  }

  @override
  String get select_template_config_tip => 'If you need to configure the inspection template, please contact your order follower';
}
