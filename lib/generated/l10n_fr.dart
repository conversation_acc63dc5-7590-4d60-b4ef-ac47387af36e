import 'package:intl/intl.dart' as intl;

import 'l10n.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class SFr extends S {
  SFr([String locale = 'fr']) : super(locale);

  @override
  String get app_name => 'Inspection en ligne';

  @override
  String get search => 'Rechercher';

  @override
  String get shortcut_tab_name => 'Navigation rapide';

  @override
  String get loading => 'Chargement...';

  @override
  String get nomore => 'Plus de contenu';

  @override
  String get confirm => 'Confirmer';

  @override
  String get more_replies => 'Plus de r&eacute;ponses';

  @override
  String get purchase_paid_publish_information_title => 'Voici ce que les clients paient pour voir';

  @override
  String get purchase_set_fee => 'Fixer les frais de visionnage';

  @override
  String get purchase_comment_paid_supplier_hint => 'Veuillez entrer le nom du fournisseur';

  @override
  String get purchase_comment_paid_contact_hint => 'Veuillez entrer le nom du contact';

  @override
  String get purchase_comment_paid_phone_hint => 'Veuillez entrer le num&eacute;ro de t&eacute;l&eacute;phone du contact';

  @override
  String get purchase_comment_paid_email_hint => 'Veuillez entrer l&#39;e-mail du contact';

  @override
  String get purchase_comment_paid_address_hint => 'Veuillez entrer l&#39;adresse de l&#39;usine';

  @override
  String get purchase_comment_paid_other_hint => 'Autres informations (facultatif)';

  @override
  String get purchase_comment_paid_low_price_hint => 'Veuillez entrer le prix de base du produit (facultatif)';

  @override
  String get purchase_reply_paid_title => 'R&eacute;ponse payante';

  @override
  String get purchase_reply_paid_desc => '(Informations sur le fournisseur et prix de r&eacute;f&eacute;rence du produit)';

  @override
  String purchase_reply_paid_evaluated_people(Object people_num) {
    return '($people_num personnes ont &eacute;valu&eacute;)';
  }

  @override
  String get language_setting => 'Param&egrave;tres de langue';

  @override
  String get public_and => 'et';

  @override
  String get public_publish => 'Publier';

  @override
  String get public_distance => 'Distance';

  @override
  String get public_deny => 'Refuser';

  @override
  String get public_see_more => 'Voir plus';

  @override
  String get general_select => 'S&eacute;lectionner';

  @override
  String get language_page_title => 'Multilingue';

  @override
  String get language_chinese => 'Chinois';

  @override
  String get language_english => 'Anglais';

  @override
  String get public_seconds_ago => 'il y a secondes';

  @override
  String get public_minutes_ago => 'il y a minutes';

  @override
  String get public_hours_ago => 'il y a heures';

  @override
  String get public_status_applied => 'Appliqu&eacute;';

  @override
  String get public_status_refused => 'Refus&eacute;';

  @override
  String get public_status_approved => 'Approuv&eacute;';

  @override
  String get public_status_canceled => 'Annul&eacute;';

  @override
  String get public_app_name => 'Inspecteur';

  @override
  String get public_send => 'Envoyer';

  @override
  String get public_ok => 'OK';

  @override
  String get public_price => 'Prix';

  @override
  String get public_cancel => 'Annuler';

  @override
  String get public_manage => 'G&eacute;rer';

  @override
  String get public_finish => 'Terminer';

  @override
  String get public_reset => 'R&eacute;initialiser';

  @override
  String get public_leave_message => 'Laisser un message';

  @override
  String get public_share => 'Partager';

  @override
  String get login_submit => 'Se connecter';

  @override
  String get login_mobile_login => 'Connexion mobile';

  @override
  String get login_mobile_tips => 'Veuillez entrer le num&eacute;ro de t&eacute;l&eacute;phone';

  @override
  String get login_email_login => 'Connexion par e-mail';

  @override
  String get login_email_tips => 'Veuillez entrer l&#39;e-mail';

  @override
  String get registry_email_phone_tips => 'Veuillez entrer l&#39;e-mail ou le num&eacute;ro de t&eacute;l&eacute;phone';

  @override
  String get login_verify_tips => 'Entrez le code de v&eacute;rification';

  @override
  String get login_password_tips => 'Entrez le mot de passe';

  @override
  String get login_password_login => 'Connexion par mot de passe';

  @override
  String get login_verify_login => 'Connexion par code de v&eacute;rification';

  @override
  String get login_register => 'S&#39;inscrire';

  @override
  String get login_take_code => 'Obtenir le code de v&eacute;rification';

  @override
  String get login_forget_password => 'Mot de passe oubli&eacute;';

  @override
  String get login_agreement => 'J&#39;accepte l&#39;accord inspector.Itd';

  @override
  String get login_area_selected => 'S&eacute;lectionner la r&eacute;gion du pays';

  @override
  String get tab_home => 'Accueil';

  @override
  String get tab_order => 'Commandes';

  @override
  String get tab_shortcut => 'Raccourci';

  @override
  String get tab_purchase => 'Achats';

  @override
  String get tab_message => 'Messages';

  @override
  String get tab_mine => 'Mon compte';

  @override
  String get supplement_title => 'Veuillez d&#39;abord compl&eacute;ter vos informations personnelles';

  @override
  String get supplement_next => 'Compl&eacute;ter';

  @override
  String get home_title => 'Place de l&#39;inspection';

  @override
  String get home_record => 'Historique des demandes';

  @override
  String get home_newest => 'Inspections les plus r&eacute;centes';

  @override
  String get home_nearest => 'Inspections &agrave; proximit&eacute;';

  @override
  String home_recommend(Object money) {
    return 'R&eacute;compense de recommandation RMB $money';
  }

  @override
  String get home_sampling => 'Inspection par &eacute;chantillonnage';

  @override
  String get home_word => 'Rapport WORD';

  @override
  String home_unit(Object day, Object people) {
    return '$people personnes/$day jours';
  }

  @override
  String get home_product_tip => 'Produit :';

  @override
  String get home_person_apply => 'personnes ont postul&eacute;';

  @override
  String get home_know_tip => 'Guide d&#39;inspection';

  @override
  String get home_inspection_tip => 'Les frais d&#39;inspection par d&eacute;faut sont le prix convenu, peuvent &ecirc;tre modifi&eacute;s, des frais bas peuvent avoir la priorit&eacute; pour l&#39;attribution';

  @override
  String get home_reviewed => 'J&#39;ai lu et je respecterai';

  @override
  String get home_apply => 'Postuler';

  @override
  String get home_apply_price => 'Veuillez entrer le montant &yen;';

  @override
  String get home_apply_check => 'Veuillez v&eacute;rifier le guide d&#39;inspection';

  @override
  String get home_apply_tips => 'Vous n&#39;&ecirc;tes pas inspecteur, si vous avez plus d&#39;un an d&#39;exp&eacute;rience en inspection du commerce ext&eacute;rieur, veuillez fournir des preuves de qualification pertinentes';

  @override
  String get home_complete_profile_tips => 'Compl&eacute;ter le profil d&#39;inspecteur et d&#39;autres informations pertinentes peut augmenter le taux d&#39;approbation de la demande d&#39;inspection';

  @override
  String get home_apply_sure => 'Soumettre pour examen';

  @override
  String get home_complete_profile_sure => 'Compl&eacute;ter';

  @override
  String get home_apply_cancel => 'Annuler la demande';

  @override
  String get home_update => 'Modifier';

  @override
  String get home_navi => 'Navigation';

  @override
  String get mine_unauth => 'Non v&eacute;rifi&eacute;';

  @override
  String get mine_checking => 'En cours de v&eacute;rification';

  @override
  String get mine_check_failed => 'V&eacute;rification &eacute;chou&eacute;e';

  @override
  String get mine_vip_level => 'Niveau VIP';

  @override
  String get mine_credit_quota => 'Limite de cr&eacute;dit';

  @override
  String get mine_authed => 'Modifier les informations de v&eacute;rification';

  @override
  String get mine_authed_inspector => 'Modifier les informations de l&#39;inspecteur';

  @override
  String get mine_amount => 'Montant du compte';

  @override
  String get mine_cash => 'Recharger/Retirer';

  @override
  String get mine_order => 'Mes commandes';

  @override
  String get mine_purchase => 'Mes achats';

  @override
  String get mine_check => 'Mes inspections';

  @override
  String get mine_address => 'Carnet d&#39;adresses (Fournisseur)';

  @override
  String get mine_recommend => 'Recommander';

  @override
  String get mine_setting => 'Param&egrave;tres';

  @override
  String get mine_header_inspect => 'Gestion de l&#39;inspection';

  @override
  String get mine_header_purchase => 'Gestion des achats';

  @override
  String get mine_header_other => 'Autres';

  @override
  String get mine_inspect_mine => 'Mes inspections';

  @override
  String get mine_inspect_order => 'Gestion des commandes';

  @override
  String get mine_inspect_history => 'Historique des demandes';

  @override
  String get mine_purchase_mine => 'Mes achats';

  @override
  String get mine_purchase_reply => 'Historique des r&eacute;ponses';

  @override
  String get mine_purchase_appeal => 'Gestion des appels';

  @override
  String get mine_other_recommend => 'Recommander';

  @override
  String get mine_other_address => 'Carnet d&#39;adresses (Fournisseur)';

  @override
  String get mine_other_settings => 'Param&egrave;tres';

  @override
  String get profile_title => 'Informations personnelles';

  @override
  String get profile_avatar => 'Avatar';

  @override
  String get profile_name => 'Pseudo';

  @override
  String get profile_mobile => 'Num&eacute;ro de t&eacute;l&eacute;phone';

  @override
  String get profile_country => 'Pays';

  @override
  String get profile_real_name => 'Nom r&eacute;el';

  @override
  String get profile_city => 'Ville';

  @override
  String get profile_email => 'E-mail';

  @override
  String get profile_wechat => 'WeChat';

  @override
  String get profile_bind_manage => 'Gestion de la liaison des comptes';

  @override
  String get profile_info_failed => '&Eacute;chec de la mise &agrave; jour des informations';

  @override
  String get apply_title => 'Demande de qualification d&#39;inspecteur';

  @override
  String get apply_nick => 'Pseudo';

  @override
  String get apply_sex => 'Sexe';

  @override
  String get apply_birthday => 'Date de naissance';

  @override
  String get apply_education => '&Eacute;ducation';

  @override
  String get apply_address => 'Adresse de r&eacute;sidence';

  @override
  String get apply_price => 'Frais d&#39;inspection minimum';

  @override
  String get apply_shebao => 'S&eacute;curit&eacute; sociale';

  @override
  String get apply_id_card => 'Num&eacute;ro de carte d&#39;identit&eacute;';

  @override
  String get apply_file => 'Modifier le CV';

  @override
  String get apply_file_tip => 'Veuillez entrer vos informations de base et votre exp&eacute;rience';

  @override
  String get apply_upload_file => 'T&eacute;l&eacute;charger le CV';

  @override
  String get apply_upload_file_failed => '&Eacute;chec du t&eacute;l&eacute;chargement du CV';

  @override
  String get apply_upload_card => 'T&eacute;l&eacute;charger la photo de la carte d&#39;identit&eacute;';

  @override
  String get apply_card_front => 'Photo recto (c&ocirc;t&eacute; visage)';

  @override
  String get apply_card_back => 'Photo verso (c&ocirc;t&eacute; embl&egrave;me national)';

  @override
  String get apply_submit => 'Soumettre';

  @override
  String get apply_enter => 'Veuillez entrer';

  @override
  String get apply_next_tip => 'Veuillez confirmer que les informations sont compl&egrave;tes avant de soumettre';

  @override
  String get apply_auth_failed => '&Eacute;chec de la v&eacute;rification d&#39;identit&eacute;, veuillez t&eacute;l&eacute;charger la bonne photo';

  @override
  String get apply_checking => 'V&eacute;rification d&#39;identit&eacute; en cours';

  @override
  String get apply_check_success => 'V&eacute;rification d&#39;identit&eacute; r&eacute;ussie';

  @override
  String get apply_check_failed => 'V&eacute;rification &eacute;chou&eacute;e, veuillez modifier le contenu et soumettre &agrave; nouveau';

  @override
  String get order_title => 'Mes commandes';

  @override
  String get order_input => 'Mes inspections';

  @override
  String get order_output => 'Publier une commande';

  @override
  String get order_all => 'Toutes';

  @override
  String get order_wait_pay => 'En attente de paiement';

  @override
  String get order_cancelled => 'Annul&eacute;e';

  @override
  String get order_status => 'Statut de la commande';

  @override
  String get order_status_unknown => 'Inconnu';

  @override
  String get order_refund_pending => 'Remboursement en attente de v&eacute;rification';

  @override
  String get order_cancelled_refund_pending => 'Annul&eacute;e, remboursement en attente de v&eacute;rification';

  @override
  String get order_refund_partial => 'Remboursement partiel';

  @override
  String get order_refund_denied => 'Remboursement refus&eacute;';

  @override
  String get order_wait_dispatch => 'En attente d&#39;attribution';

  @override
  String get order_ready_inspect => 'Pr&ecirc;t pour l&#39;inspection';

  @override
  String get order_need_pay => 'Payer';

  @override
  String get order_wait => 'Pr&ecirc;t pour l&#39;inspection';

  @override
  String get order_confirm => 'Confirmer l&#39;attribution';

  @override
  String get order_doing => 'En cours d&#39;inspection';

  @override
  String get order_comment => 'En attente d&#39;&eacute;valuation';

  @override
  String get order_finished => 'Termin&eacute;e';

  @override
  String get order_goods_info => 'Informations sur le produit';

  @override
  String get order_goods_name => 'Nom du produit';

  @override
  String get order_goods_model => 'Mod&egrave;le du produit';

  @override
  String get order_goods_count => 'Quantit&eacute;';

  @override
  String get order_goods_unit => 'Unit&eacute;';

  @override
  String get order_order_time => 'Heure de la commande';

  @override
  String get order_order_amount => 'Montant de la commande';

  @override
  String get order_detail_title => 'D&eacute;tails de la commande';

  @override
  String get order_applying => 'Candidature soumise';

  @override
  String get order_apply_expired => 'Expir&eacute;';

  @override
  String get order_apply_dispatched => 'Attribu&eacute;';

  @override
  String get order_create_time => 'Heure de cr&eacute;ation de la commande';

  @override
  String get order_look => 'Voir le rapport d&#39;inspection';

  @override
  String get order_report_next => 'Soumettre le rapport d&#39;inspection';

  @override
  String get order_detail_inspection_info => 'Informations d&#39;inspection';

  @override
  String get order_inspection_status_unpaid => 'Non pay&eacute;';

  @override
  String get order_inspection_status_returned => 'Retourn&eacute;';

  @override
  String get order_inspection_status_waiting_start => 'En attente de d&eacute;but';

  @override
  String get order_detail_related_info => 'Informations sur les sous-commandes associ&eacute;es';

  @override
  String get order_detail_inspection_product => 'Nom du produit';

  @override
  String get order_detail_inspection_time => 'Heure d&#39;inspection';

  @override
  String get order_detail_inspection_city => 'Ville d&#39;inspection';

  @override
  String get order_detail_inspection_factory => 'Usine d&#39;inspection';

  @override
  String get order_detail_inspection_address => 'Adresse d&#39;inspection';

  @override
  String get order_detail_inspection_person => 'Contact';

  @override
  String get order_detail_inspection_phone => 'T&eacute;l&eacute;phone du contact';

  @override
  String get order_detail_inspection_email => 'E-mail du contact';

  @override
  String get order_detail_inspection_amount => 'Informations sur le prix';

  @override
  String get order_detail_inspection_sample => '&Eacute;chantillon';

  @override
  String get order_detail_inspection_standard => 'Niveau d&#39;&eacute;chantillonnage';

  @override
  String get order_detail_inspection_critical => 'AQL';

  @override
  String get order_detail_inspection_major => 'Majeur';

  @override
  String get order_detail_inspection_minor => 'Mineur';

  @override
  String get order_detail_inspection_day => 'Jours d&#39;inspection';

  @override
  String get order_detail_inspection_number => 'Nombre d&#39;inspecteurs';

  @override
  String get order_detail_inspection_price => 'Prix de l&#39;inspection';

  @override
  String get order_detail_inspection_price_apply => 'Prix de la demande';

  @override
  String get order_detail_inspection_template => 'Mod&egrave;le d&#39;inspection';

  @override
  String get order_detail_inspection_file => 'Pi&egrave;ce jointe';

  @override
  String get order_detail_inspection_image => 'Image';

  @override
  String get order_detail_inspection_type => 'Type d&#39;inspection';

  @override
  String get order_tips => '&Eacute;chantillons et notes';

  @override
  String get order_apply => 'Demander une inspection';

  @override
  String get order_cancel => 'Annuler';

  @override
  String get order_canceled => 'Annul&eacute;';

  @override
  String get order_dispatch_accept => 'Accepter';

  @override
  String get order_dispatch_refuse => 'Refuser';

  @override
  String get order_inspection => 'Commencer l&#39;inspection';

  @override
  String get order_sure_cancel => 'Voulez-vous vraiment annuler la commande ?';

  @override
  String get order_sure_refuse => 'Voulez-vous vraiment refuser cette commande ?';

  @override
  String get order_sure_confirm => 'Voulez-vous vraiment accepter cette commande ?';

  @override
  String get publish_title => 'Publier une commande';

  @override
  String get publish_edit_title => 'Modifier la commande';

  @override
  String get publish_welcome => 'Bienvenue pour passer des commandes, prix libres, communication libre avec les inspecteurs';

  @override
  String get publish_sampling => 'Inspection par &eacute;chantillonnage';

  @override
  String get publish_sampling_content => 'Applicable aux produits termin&eacute;s &agrave; 100%, avec au moins 80% des produits d&eacute;j&agrave; emball&eacute;s, pr&ecirc;ts &agrave; l&#39;exp&eacute;dition. Nous effectuons une inspection par &eacute;chantillonnage al&eacute;atoire bas&eacute;e sur le plan d&#39;&eacute;chantillonnage international ANSI/ASQZ1.4(MIL-STD-105E) ainsi que sur vos exigences sp&eacute;ciales. Dans le rapport d&#39;inspection par &eacute;chantillonnage, nous refl&eacute;terons compl&egrave;tement la quantit&eacute; de produits termin&eacute;s, les conditions d&#39;emballage et la conformit&eacute; aux exigences AQL (Niveau de Qualit&eacute; Acceptable), vous donnant une compr&eacute;hension compl&egrave;te de la qualit&eacute; du lot entier de produits avant l&#39;exp&eacute;dition, &eacute;vitant tout risque pour votre commande';

  @override
  String get publish_point => 'Points d&#39;inspection';

  @override
  String get publish_sampling_point => '● V&eacute;rification des donn&eacute;es/&eacute;chantillons du client\n● V&eacute;rification de la quantit&eacute; termin&eacute;e\n● V&eacute;rification de la taille, du style, de la couleur du produit\n● Inspection de l&#39;apparence et de la fabrication\n● Test de fonctionnalit&eacute; et de s&eacute;curit&eacute; du produit\n● Inspection des &eacute;tiquettes d&#39;exp&eacute;dition\n● Int&eacute;grit&eacute; de l&#39;emballage\n● D&eacute;tails sp&eacute;cifiques de l&#39;emballage\n● Exigences sp&eacute;ciales du client';

  @override
  String get publish_all => 'Inspection compl&egrave;te';

  @override
  String get publish_all_content => 'L&#39;inspection compl&egrave;te peut &ecirc;tre effectu&eacute;e avant ou apr&egrave;s l&#39;emballage. Selon les exigences du client, chaque produit est inspect&eacute; pour l&#39;apparence, la taille, la fabrication, la fonctionnalit&eacute; et la s&eacute;curit&eacute;, s&eacute;parant les bons produits des d&eacute;fectueux, et rapportant les r&eacute;sultats de l&#39;inspection au client en temps opportun';

  @override
  String get publish_online => 'Inspection en ligne';

  @override
  String get publish_online_content => 'L&#39;inspection en ligne est effectu&eacute;e pendant le processus de production ou avant que toute la production ne soit termin&eacute;e et emball&eacute;e. Elle peut vous aider &agrave; confirmer que la qualit&eacute;, la fonctionnalit&eacute;, l&#39;apparence et d&#39;autres &eacute;l&eacute;ments du produit restent conformes &agrave; vos sp&eacute;cifications tout au long du processus de production, aide &eacute;galement &agrave; d&eacute;couvrir toute non-conformit&eacute; t&ocirc;t, r&eacute;duisant ainsi le risque de retards de livraison de l&#39;usine';

  @override
  String get publish_online_point => '● Suivi de la situation de production\n● &Eacute;valuation de la ligne de production et confirmation de l&#39;avancement de la production\n● Inspection des &eacute;chantillons de produits semi-finis et finis\n● V&eacute;rification des informations d&#39;emballage et des mat&eacute;riaux d&#39;emballage\n● Am&eacute;lioration des produits d&eacute;fectueux\n● &Eacute;valuation du d&eacute;lai de livraison';

  @override
  String get publish_factory => 'Audit d&#39;usine';

  @override
  String get publish_factory_content => 'L&#39;audit d&#39;usine utilise principalement une m&eacute;thode d&#39;&eacute;valuation objective, &eacute;valuant et auditant l&#39;usine quantitativement selon des normes ou des crit&egrave;res pr&eacute;d&eacute;finis. Un rapport d&#39;&eacute;valuation est g&eacute;n&eacute;r&eacute; sur la base de la notation sur place et des r&eacute;sultats de l&#39;audit global de l&#39;usine, comme base pour que le client d&eacute;termine si l&#39;usine peut &ecirc;tre un fournisseur qualifi&eacute;';

  @override
  String get publish_factory_point_title => 'Contenu de l&#39;audit';

  @override
  String get publish_factory_point => '● Aper&ccedil;u de l&#39;usine (informations de base)\n● Structure organisationnelle\n● Processus de production\n● Capacit&eacute; de production\n● Capacit&eacute; de recherche et d&eacute;veloppement\n● &Eacute;quipements et installations';

  @override
  String get publish_factory_review => '&Eacute;valuation globale';

  @override
  String get publish_factory_review_content => '● Pour chaque &eacute;l&eacute;ment d&#39;audit, consid&eacute;rez son importance relative, attribuez diff&eacute;rents scores, puis g&eacute;n&eacute;rez un tableau de notation de qualification bas&eacute; sur le formulaire d&#39;enqu&ecirc;te d&#39;audit et les donn&eacute;es de l&#39;enqu&ecirc;te sur le terrain';

  @override
  String get publish_watch => 'Supervision du chargement';

  @override
  String get publish_watch_content => 'La supervision du chargement du conteneur comprend principalement l&#39;&eacute;valuation de l&#39;&eacute;tat du conteneur, la v&eacute;rification des informations sur le produit, le comptage des bo&icirc;tes charg&eacute;es, l&#39;inspection des informations d&#39;emballage et la supervision de l&#39;ensemble du processus de chargement. Pour r&eacute;duire le risque &eacute;lev&eacute; de substitution de marchandises apr&egrave;s le chargement, l&#39;inspecteur supervise sur le site de chargement pour s&#39;assurer que les produits que vous payez sont charg&eacute;s en toute s&eacute;curit&eacute;';

  @override
  String get publish_watch_point => '● Enregistrer le num&eacute;ro du conteneur et le num&eacute;ro du camion\n● Inspecter le conteneur pour les dommages, l&#39;humidit&eacute; et les odeurs &eacute;tranges, prendre des photos du conteneur vide\n● V&eacute;rifier la quantit&eacute; de bo&icirc;tes &agrave; charger et l&#39;&eacute;tat de l&#39;emballage ext&eacute;rieur, v&eacute;rifier al&eacute;atoirement quelques bo&icirc;tes pour confirmer les produits r&eacute;ellement charg&eacute;s\n● Superviser le processus de chargement pour assurer un dommage minimal et une utilisation maximale de l&#39;espace\n● Prendre des photos de l&#39;&eacute;tat de fermeture du conteneur, du num&eacute;ro de sceau du conteneur et de la liste de colisage, enregistrer l&#39;heure de d&eacute;part du conteneur';

  @override
  String get publish_watch_inspection => 'Inspection + Supervision du chargement';

  @override
  String get publish_watch_inspection_content => 'Pour garantir la qualit&eacute; finale et l&#39;int&eacute;grit&eacute; du produit, avant d&#39;&ecirc;tre pr&ecirc;t pour l&#39;exp&eacute;dition, nous pr&eacute;levons des &eacute;chantillons al&eacute;atoires des produits finis pour l&#39;inspection par &eacute;chantillonnage bas&eacute;e sur le plan d&#39;&eacute;chantillonnage international, et v&eacute;rifions les donn&eacute;es fournies par le client, tout en supervisant l&#39;ensemble du processus de chargement du conteneur';

  @override
  String get publish_watch_inspection_point => '● Avant l&#39;arriv&eacute;e du conteneur, v&eacute;rifier les donn&eacute;es/&eacute;chantillons du client, effectuer une inspection par &eacute;chantillonnage sur l&#39;apparence, la fabrication, la fonctionnalit&eacute; et la s&eacute;curit&eacute; du produit, ainsi que l&#39;emballage, les &eacute;tiquettes d&#39;exp&eacute;dition, etc.\n● Communiquer imm&eacute;diatement avec l&#39;usine si des produits d&eacute;fectueux sont trouv&eacute;s, pour remplacement ou retraitement\n● Inspecter le conteneur pour les dommages, l&#39;humidit&eacute; et les odeurs &eacute;tranges, prendre des photos du conteneur vide\n● Superviser le processus de chargement pour assurer un dommage minimal et une utilisation maximale de l&#39;espace\n● Prendre des photos de l&#39;&eacute;tat de fermeture du conteneur, du num&eacute;ro de sceau du conteneur et de la liste de colisage, enregistrer l&#39;heure de d&eacute;part du conteneur';

  @override
  String get publish_next => 'Suivant';

  @override
  String get publish_inspection_time => 'Heure d&#39;inspection';

  @override
  String get publish_inspection_time_selected => 'S&eacute;lectionner l&#39;heure d&#39;inspection';

  @override
  String get publish_inspection_time_tip => 'Veuillez s&eacute;lectionner';

  @override
  String get publish_inspection_people => 'Nombre d&#39;inspecteurs';

  @override
  String get publish_people => 'personnes';

  @override
  String get publish_day => 'jours';

  @override
  String get publish_inspection_factory => 'Usine d&#39;inspection';

  @override
  String get publish_factory_tips => 'Entrez l&#39;usine d&#39;inspection';

  @override
  String get publish_address_book => 'Carnet d&#39;adresses';

  @override
  String get publish_goods_name => 'Nom du produit';

  @override
  String get publish_name_tips => 'Entrez un ou deux noms repr&eacute;sentatifs s&#39;il y a plusieurs produits';

  @override
  String get publish_po_tips => 'Veuillez entrer le num&eacute;ro de P.O';

  @override
  String get publish_file_tips => 'T&eacute;l&eacute;charger la pi&egrave;ce jointe';

  @override
  String get publish_camera => 'T&eacute;l&eacute;charger une photo';

  @override
  String get publish_file => 'T&eacute;l&eacute;charger un fichier';

  @override
  String get publish_purchase => 'Publier une commande d&#39;achat';

  @override
  String get publish_inspection => 'Publier une commande d&#39;inspection';

  @override
  String get publish_factory_tip => 'Veuillez d&#39;abord s&eacute;lectionner les informations d&#39;adresse d&#39;inspection';

  @override
  String get publish_attention => 'Notes';

  @override
  String get publish_attention_tips => 'Veuillez indiquer les probl&egrave;mes sur lesquels l&#39;inspecteur doit se concentrer';

  @override
  String get publish_stand_price => 'Prix fixe';

  @override
  String get publish_click_price => 'Changer';

  @override
  String get publish_vip_price => 'Prix VIP';

  @override
  String get publish_vip_tips => 'Fournit un service de suivi manuel tout au long du processus';

  @override
  String get publish_total => 'Total';

  @override
  String get publish_submit => 'Soumettre';

  @override
  String get publish_only_price_failed => 'Pas d&#39;autorisation de prix fixe';

  @override
  String get publish_price_tip => 'Veuillez s&eacute;lectionner le prix';

  @override
  String get publish_date_tips => 'Veuillez s&eacute;lectionner la date';

  @override
  String get date_title => 'Date d&#39;inspection';

  @override
  String get date_save => 'Enregistrer';

  @override
  String get address_title => 'Modifier les informations de l&#39;usine';

  @override
  String get address_auto_tips => 'Veuillez coller ou saisir le texte, cliquez sur &quot;Identifier&quot; pour identifier automatiquement le nom de l&#39;usine, le nom et le t&eacute;l&eacute;phone, l&#39;adresse, etc.';

  @override
  String get address_paste => 'Coller';

  @override
  String get address_ocr => 'Identifier';

  @override
  String get address_name => 'Nom de l&#39;usine';

  @override
  String get address_name_tip => 'Veuillez entrer les informations de l&#39;usine d&#39;inspection';

  @override
  String get address_person => 'Contact';

  @override
  String get address_person_tip => 'Veuillez entrer le contact';

  @override
  String get address_mobile => 'Num&eacute;ro de t&eacute;l&eacute;phone mobile';

  @override
  String get address_mobile_tip => 'Veuillez entrer le num&eacute;ro de t&eacute;l&eacute;phone mobile';

  @override
  String get address_email => 'E-mail';

  @override
  String get address_email_tip => 'Veuillez entrer l&#39;adresse e-mail';

  @override
  String get address_area => 'Province-Ville-District';

  @override
  String get address_area_tip => 'Veuillez s&eacute;lectionner Province-Ville-District  〉';

  @override
  String get address_detail => 'Adresse d&eacute;taill&eacute;e';

  @override
  String get address_detail_tip => 'Entrez les informations de rue, num&eacute;ro, etc.';

  @override
  String get address_location => 'Emplacement';

  @override
  String get address_save_tip => 'Enregistrer dans le carnet d&#39;adresses';

  @override
  String get address_clear => 'Effacer';

  @override
  String get address_submit => 'Soumettre';

  @override
  String get address_recent => 'Adresses r&eacute;cemment utilis&eacute;es';

  @override
  String get address_more => 'Plus d&#39;adresses';

  @override
  String get address_list_title => 'Gestion des adresses';

  @override
  String get address_insert => 'Ajouter une adresse';

  @override
  String get address_delete => 'Supprimer';

  @override
  String get address_delete_result => 'Échec de la suppression';

  @override
  String get address_edit => 'Modifier';

  @override
  String get address_delete_tips => 'Confirmer la suppression de l&#39;adresse ?';

  @override
  String get address_detected_paste => 'Informations d&#39;adresse d&eacute;tect&eacute;es, utiliser cette adresse ?';

  @override
  String get pay_title => 'Payer la commande';

  @override
  String get pay_time => 'Temps restant pour le paiement';

  @override
  String get pay_paypal => 'PayPal';

  @override
  String get pay_zfb => 'Alipay';

  @override
  String get pay_usd => 'Compte USD';

  @override
  String get pay_rmb => 'Compte RMB';

  @override
  String get pay_pay => 'Payer';

  @override
  String get pay_result_success => 'Paiement r&eacute;ussi';

  @override
  String get pay_result_success_wait => 'Paiement r&eacute;ussi, en attente du traitement de la commande.';

  @override
  String get pay_result_failed => 'Échec du paiement';

  @override
  String get pay_keep => 'Peut &ecirc;tre comptabilis&eacute;';

  @override
  String get check_title => 'Rapport d&#39;inspection';

  @override
  String get check_picture => 'Photos d&#39;inspection';

  @override
  String get check_file => 'Lettre d&#39;engagement d&#39;int&eacute;grit&eacute;';

  @override
  String get check_report => 'Lettre d&#39;engagement d&#39;int&eacute;grit&eacute; + Rapport manuscrit';

  @override
  String get check_draft => 'Brouillon du rapport';

  @override
  String get check_template => 'Mod&egrave;le de rapport';

  @override
  String get check_submit => 'Soumettre';

  @override
  String get check_hint => 'Veuillez entrer la description de l&#39;image';

  @override
  String get check_checking => 'Rapport en cours d&#39;examen';

  @override
  String get check_check_success => 'Examen du rapport r&eacute;ussi';

  @override
  String get check_check_failed => 'Échec de l&#39;examen du rapport, veuillez modifier le contenu et soumettre &agrave; nouveau';

  @override
  String get review_title => '&Eacute;valuation du service';

  @override
  String get review_next => '&Eacute;valuer';

  @override
  String get contact_bnt => 'Contacter maintenant';

  @override
  String get review_score => 'Niveau de service';

  @override
  String get review_score1 => 'D&eacute;cevant';

  @override
  String get review_score2 => 'Insatisfaisant';

  @override
  String get review_score3 => 'Normal';

  @override
  String get review_score4 => 'Satisfaisant';

  @override
  String get review_score5 => 'Excellent';

  @override
  String get review_tips => '&Eacute;valuez sous plusieurs angles pour nous aider &agrave; mieux comprendre les capacit&eacute;s de travail de l&#39;inspecteur';

  @override
  String get review_picture => 'Image';

  @override
  String get review_submit => 'Soumettre';

  @override
  String get setting_title => 'Param&egrave;tres';

  @override
  String get setting_address => 'Gestion des adresses';

  @override
  String get setting_clear_cache => 'Vider le cache';

  @override
  String get setting_clear_success => 'Nettoyage r&eacute;ussi';

  @override
  String get setting_about_us => '&Agrave; propos de nous';

  @override
  String get setting_receive_msg => 'Recevoir des notifications de messages';

  @override
  String get setting_version => 'Num&eacute;ro de version';

  @override
  String get setting_check_update => 'V&eacute;rifier les mises &agrave; jour';

  @override
  String get setting_login_out => 'D&eacute;connexion';

  @override
  String get setting_login_out_tips => '&Ecirc;tes-vous s&ucirc;r de vouloir vous d&eacute;connecter ?';

  @override
  String get setting_delete_account_tips => '&Ecirc;tes-vous s&ucirc;r de vouloir supprimer le compte ?';

  @override
  String get setting_policy_title => 'Avis d&#39;autorisation de politique de confidentialit&eacute;';

  @override
  String get setting_policy_sub_title => 'Avant de continuer, veuillez lire et accepter';

  @override
  String get setting_privacy_policy => 'Politique de confidentialit&eacute;';

  @override
  String get setting_user_agreement => 'Accord utilisateur';

  @override
  String get setting_privacy_content => 'Bienvenue dans l&#39;application d&#39;Inspection en ligne\nNous accordons une grande importance &agrave; votre vie priv&eacute;e et &agrave; la protection de vos informations personnelles. Lors de votre utilisation de cette application, nous collecterons et utiliserons une partie de vos informations personnelles\n1. Apr&egrave;s avoir accept&eacute; la politique de confidentialit&eacute; de l&#39;application, nous proc&eacute;derons &agrave; l&#39;initialisation du SDK int&eacute;gr&eacute;, qui collectera l&#39;adresse MAC de votre appareil, l&#39;IMSI, l&#39;ID Android, l&#39;adresse IP, le mod&egrave;le de mat&eacute;riel, le num&eacute;ro de version du syst&egrave;me d&#39;exploitation, l&#39;identifiant unique de l&#39;appareil (comme l&#39;IMEI), l&#39;adresse mat&eacute;rielle du r&eacute;seau (MAC), le num&eacute;ro de version du logiciel, la m&eacute;thode d&#39;acc&egrave;s au r&eacute;seau, le type, l&#39;&eacute;tat, les donn&eacute;es de qualit&eacute; du r&eacute;seau, les journaux d&#39;op&eacute;ration, le num&eacute;ro de s&eacute;rie du mat&eacute;riel, les informations du journal de service, etc. pour assurer des statistiques de donn&eacute;es normales et un contr&ocirc;le de s&eacute;curit&eacute; de l&#39;application.\n2. Sans votre consentement, nous n&#39;obtiendrons pas, ne partagerons pas et ne fournirons pas vos informations &agrave; des tiers.\n3. Vous pouvez acc&eacute;der, corriger et supprimer vos informations personnelles, et nous fournirons &eacute;galement des moyens d&#39;annuler et de d&eacute;poser des plaintes.';

  @override
  String get setting_ihavereadandagreed => 'J&#39;ai lu et j&#39;accepte';

  @override
  String get setting_policy_tips2 => 'Veuillez lire et comprendre attentivement';

  @override
  String get wallet_title => 'Portefeuille';

  @override
  String get wallet_bill => 'Facture';

  @override
  String get wallet_rmb_account => 'Compte RMB';

  @override
  String get wallet_usd_account => 'Compte USD';

  @override
  String get wallet_account_heading => 'Compte de retrait et param&egrave;tres';

  @override
  String get wallet_bank => 'Carte bancaire';

  @override
  String get wallet_wechat => 'WeChat';

  @override
  String get wallet_alipay => 'Alipay';

  @override
  String get wallet_charge => 'Recharger';

  @override
  String get wallet_cash => 'Retirer';

  @override
  String get wallet_balance => 'Solde';

  @override
  String get wallet_default_account => 'Compte par d&eacute;faut';

  @override
  String get wallet_set_default_account => 'D&eacute;finir comme compte par d&eacute;faut';

  @override
  String get bill_title => 'Facture';

  @override
  String get bill_out => 'D&eacute;pense';

  @override
  String get bill_in => 'Revenu';

  @override
  String get bill_month => 'Mois';

  @override
  String get bill_fenxi => 'Analyse des revenus et d&eacute;penses';

  @override
  String get bill_unfreeze => 'D&eacute;bloquer';

  @override
  String get bill_all => 'Toutes les factures';

  @override
  String get bill_income => 'Revenu';

  @override
  String get bill_outcome => 'D&eacute;pense';

  @override
  String get bill_freeze => 'Bloquer';

  @override
  String get bill_withdraw => 'Retirer';

  @override
  String get bank_title => 'Mes cartes bancaires';

  @override
  String get bank_add => 'Ajouter une carte bancaire';

  @override
  String get add_bank_title => 'Ajouter une carte bancaire';

  @override
  String get add_bank_name => 'Nom de la carte bancaire';

  @override
  String get add_bank_branch => 'Succursale bancaire';

  @override
  String get add_bank_card => 'Num&eacute;ro de carte';

  @override
  String get add_bank_real_name => 'Nom';

  @override
  String get add_bank_address => 'Adresse d&#39;ouverture du compte';

  @override
  String bind_title(Object bind) {
    return 'Lier $bind';
  }

  @override
  String get bind_account => 'Compte';

  @override
  String get bind_image => 'Code de paiement';

  @override
  String get bind_name => 'Nom';

  @override
  String get bind_hint => 'Veuillez entrer';

  @override
  String get charge_title => 'Recharger';

  @override
  String get charge_account => 'Compte de recharge';

  @override
  String get charge_money => 'Montant de la recharge';

  @override
  String get charge_deposit_type_title => 'M&eacute;thode de recharge';

  @override
  String get charge_deposit_type_online => 'Recharge en ligne';

  @override
  String get charge_deposit_type_offline => 'Transfert hors ligne';

  @override
  String get charge_offline_nopic_hint => 'Veuillez t&eacute;l&eacute;charger la preuve de recharge';

  @override
  String get charge_upload_proof => 'Veuillez t&eacute;l&eacute;charger la preuve de transfert';

  @override
  String get withdraw_list_title => 'Historique des retraits';

  @override
  String get withdraw_rmb => 'Retrait RMB';

  @override
  String get withdraw_usd => 'Retrait USD';

  @override
  String get withdraw_status_checking => 'En cours d&#39;examen';

  @override
  String get withdraw_status_approved => 'Examen approuv&eacute;';

  @override
  String get withdraw_status_denied => 'Examen refus&eacute;';

  @override
  String get withdraw_cash_status_unfinished => 'Non transf&eacute;r&eacute;';

  @override
  String get withdraw_cash_status_done => 'Transf&eacute;r&eacute;';

  @override
  String get withdraw_cash_status_refused => 'Refus&eacute;';

  @override
  String get charge_hint => 'Veuillez entrer le montant de la recharge';

  @override
  String get charge_submit => 'Confirmer';

  @override
  String get charge_rmb => 'Recharge RMB';

  @override
  String get charge_usd => 'Recharge USD';

  @override
  String get charge_history_title => 'Historique des recharges';

  @override
  String get cash_title => 'Retirer';

  @override
  String get cash_account => 'S&eacute;lectionner le compte de retrait';

  @override
  String get cash_money => 'Montant du retrait';

  @override
  String get cash_invoice_money => 'Montant de la facture';

  @override
  String get cash_invoice_money_hint => 'Veuillez entrer le montant de la facture';

  @override
  String get cash_invoice_upload => 'T&eacute;l&eacute;charger la facture';

  @override
  String get cash_account_list_title => 'Apr&egrave;s l&#39;approbation de la demande, les fonds de retrait seront transf&eacute;r&eacute;s al&eacute;atoirement sur l&#39;un des comptes suivants :';

  @override
  String get cash_hint => 'Veuillez entrer le montant du retrait';

  @override
  String get cash_withdraw_tips1 => 'Vous retirez vers';

  @override
  String get cash_withdraw_tips2 => ', le montant du retrait est';

  @override
  String get cash_amount => 'Montant &agrave; recevoir';

  @override
  String get cash_other => 'Frais de service';

  @override
  String get cash_submit => 'Confirmer';

  @override
  String get location_permission => 'L&#39;autorisation de localisation est n&eacute;cessaire, veuillez l&#39;activer';

  @override
  String get location_cancel => 'Annuler';

  @override
  String get location_author => 'Autoriser';

  @override
  String get group_title => 'Membres du groupe';

  @override
  String get unknown_error => 'Erreur inconnue';

  @override
  String get data_parsing_exception => 'Exception d&#39;analyse des donn&eacute;es';

  @override
  String get edit => 'Modifier';

  @override
  String get no_data => 'Pas de donn&eacute;es';

  @override
  String get note => 'Note explicative';

  @override
  String get msg_locating => 'Localisation en cours';

  @override
  String get failed_to_download => '&Eacute;chec du t&eacute;l&eacute;chargement de la mise &agrave; jour';

  @override
  String get pick_address => 'Cliquez pour entrer l&#39;adresse de l&#39;usine';

  @override
  String get update_now => 'Mettre &agrave; jour maintenant';

  @override
  String get message => 'Message';

  @override
  String get view_order => 'Voir la commande';

  @override
  String get today => 'Aujourd&#39;hui';

  @override
  String get yesterday => 'Hier';

  @override
  String get send_file => 'Envoyer un fichier';

  @override
  String get login_expired => 'La session a expir&eacute;, veuillez vous reconnecter';

  @override
  String get exit_group_chat_confirm => '&Ecirc;tes-vous s&ucirc;r de vouloir quitter le chat de groupe ?';

  @override
  String get exit_group_chat_success => 'Vous avez quitt&eacute; le chat de groupe';

  @override
  String get exit_group_chat_page_title => 'Informations sur le chat';

  @override
  String get exit_group_chat_button_title => 'Quitter le chat de groupe';

  @override
  String get group_chat_setting_view_more => 'Voir plus de membres du groupe';

  @override
  String get group_chat_setting_name => 'Nom du chat de groupe';

  @override
  String get group_chat_setting_owner_update => 'Seul le propri&eacute;taire du groupe peut modifier le nom du groupe';

  @override
  String get group_chat_name_page_title => 'Modifier le nom du chat de groupe';

  @override
  String get group_chat_name_page_required => 'Veuillez entrer le nom du chat de groupe';

  @override
  String get group_chat_name_save => 'Enregistrer';

  @override
  String get group_chat_name_saved => 'Nom du chat de groupe modifi&eacute;';

  @override
  String get conversation_manage_view_please => 'Veuillez s&eacute;lectionner la conversation &agrave; op&eacute;rer';

  @override
  String get conversation_manage_view_list => 'Liste des conversations';

  @override
  String get group_manage_select => 'Veuillez s&eacute;lectionner le groupe &agrave; op&eacute;rer';

  @override
  String get group_manage_list => 'Liste des groupes';

  @override
  String get please_enter => 'Veuillez entrer';

  @override
  String get address_keyword => 'Veuillez entrer des mots-cl&eacute;s d&#39;adresse';

  @override
  String get inspector_min_fee => 'Veuillez entrer les frais d&#39;inspection minimum';

  @override
  String get inspector_id_card_required => 'Veuillez entrer le num&eacute;ro de carte d&#39;identit&eacute;';

  @override
  String get inspector_id_card_upload => 'Veuillez t&eacute;l&eacute;charger la photo de la carte d&#39;identit&eacute;';

  @override
  String get inspector_id_card_upload_fail => '&Eacute;chec du t&eacute;l&eacute;chargement de la photo de la carte d&#39;identit&eacute;, veuillez r&eacute;essayer';

  @override
  String get inspector_revoke => '&Ecirc;tes-vous s&ucirc;r de vouloir r&eacute;voquer la qualification d&#39;inspecteur ?';

  @override
  String get inspector_revoke_completed => 'Qualification d&#39;inspecteur r&eacute;voqu&eacute;e';

  @override
  String get male => 'Homme';

  @override
  String get female => 'Femme';

  @override
  String get elementary => '&Eacute;cole primaire';

  @override
  String get junior => 'Coll&egrave;ge';

  @override
  String get technical => 'Technique';

  @override
  String get senior => 'Lyc&eacute;e';

  @override
  String get college => 'Universit&eacute;';

  @override
  String get bachelor => 'Licence';

  @override
  String get master => 'Master';

  @override
  String get doctor => 'Doctorat';

  @override
  String get yes => 'Oui';

  @override
  String get no => 'Non';

  @override
  String get upload_image => 'T&eacute;l&eacute;charger une image';

  @override
  String get upload_file => 'T&eacute;l&eacute;charger un fichier';

  @override
  String get revoke_inspector => 'R&eacute;voquer la qualification d&#39;inspecteur';

  @override
  String get deposit_card => 'Carte de d&eacute;p&ocirc;t';

  @override
  String get withdrawal_balance => 'Le montant du retrait ne peut pas d&eacute;passer le solde du compte';

  @override
  String get failed_get_payment_info => '&Eacute;chec de l&#39;obtention des informations de paiement';

  @override
  String get recommended_order => 'Commande recommand&eacute;e';

  @override
  String get withdrawal_method => 'Veuillez fournir au moins une m&eacute;thode de retrait';

  @override
  String get withdrawal_bind_alipay => 'Veuillez d&#39;abord lier Alipay';

  @override
  String get enabled_camera => 'Veuillez autoriser l&#39;utilisation de l&#39;appareil photo pour prendre des photos';

  @override
  String get valid_email_mobile => 'Veuillez entrer une adresse e-mail ou un num&eacute;ro de t&eacute;l&eacute;phone valide';

  @override
  String get apple_map => 'Plan d&#39;Apple';

  @override
  String get baidu_map => 'Carte Baidu';

  @override
  String get amap => 'Carte Amap';

  @override
  String get google_map => 'Google Maps';

  @override
  String get tencent_map => 'Carte Tencent';

  @override
  String get image_format => 'Le format d&#39;image doit &ecirc;tre png, jpg, jpeg';

  @override
  String get enable_location_service => 'L&#39;autorisation de localisation doit &ecirc;tre activ&eacute;e';

  @override
  String get enable_location_service_tips => 'Activez l&#39;autorisation de localisation pour rechercher pr&eacute;cis&eacute;ment les commandes d&#39;inspection &agrave; proximit&eacute;';

  @override
  String get enable_permission_not_now => 'Pas maintenant';

  @override
  String get enable_permission_goto_setting => 'Aller aux param&egrave;tres';

  @override
  String get failed_location_service => '&Eacute;chec de l&#39;obtention des informations de localisation';

  @override
  String get turn_on_location_service => 'Veuillez activer le service de localisation du t&eacute;l&eacute;phone';

  @override
  String get no_install_map => 'Vous n&#39;avez pas install&eacute; de carte';

  @override
  String get camera => 'Appareil photo';

  @override
  String get photo_album => 'Album photo';

  @override
  String get new_version => 'Nouvelle version lanc&eacute;e';

  @override
  String get invalid_mail => 'L&#39;e-mail de l&#39;utilisateur n&#39;existe pas';

  @override
  String get invalid_password => 'Mot de passe incorrect';

  @override
  String get invalid_mobile => 'Le num&eacute;ro de t&eacute;l&eacute;phone n&#39;existe pas';

  @override
  String get invalid_auth_code => 'Code de v&eacute;rification incorrect';

  @override
  String get invalid_login => '&Eacute;chec de la connexion, veuillez r&eacute;essayer';

  @override
  String get grabbing => 'Saisie de la commande';

  @override
  String get hour_ago => 'heures publi&eacute;';

  @override
  String get minute_ago => 'minutes publi&eacute;';

  @override
  String get report_type => 'Type de rapport';

  @override
  String get fri => 'Inspection par &eacute;chantillonnage';

  @override
  String get fui => '';

  @override
  String get oli => '';

  @override
  String get fat => '';

  @override
  String get cls => 'Supervision du conteneur';

  @override
  String get fri_cls => '';

  @override
  String get order_payment => 'Paiement de la commande';

  @override
  String get order_refund => 'Remboursement de la commande';

  @override
  String get expend_withdrawal => 'D&eacute;pense - Retrait';

  @override
  String get incoming_refund => 'Revenu - Remboursement de commande';

  @override
  String get incoming_recharge => 'Revenu - Recharge';

  @override
  String get chat_not_member => 'Vous n&#39;&ecirc;tes plus membre du groupe, vous ne pouvez pas envoyer de messages';

  @override
  String get admins => 'Contacter le service client';

  @override
  String get theme_title => 'Th&egrave;me';

  @override
  String get theme_light => 'Th&egrave;me clair';

  @override
  String get theme_dark => 'Th&egrave;me sombre';

  @override
  String get theme_auto => 'Suivre le syst&egrave;me';

  @override
  String get amount_total => 'Total';

  @override
  String get amount_available => 'Disponible';

  @override
  String get amount_blocked => 'Bloqu&eacute;';

  @override
  String get download => 'Cliquer pour t&eacute;l&eacute;charger';

  @override
  String get downloading => 'T&eacute;l&eacute;chargement en cours';

  @override
  String get saved => 'Enregistr&eacute;';

  @override
  String get order_number => 'Num&eacute;ro de commande';

  @override
  String get order_detail_inspection_cost => 'Co&ucirc;t d&#39;inspection';

  @override
  String get delete_account => 'Supprimer le compte';

  @override
  String get delete_account_confirm => 'Toutes les informations ne seront pas conserv&eacute;es.\n&Ecirc;tes-vous s&ucirc;r de vouloir supprimer ?';

  @override
  String get delete_account_result => 'Compte supprim&eacute;';

  @override
  String get not_exist_account => 'Le compte n&#39;existe pas';

  @override
  String get new_password => 'Entrez un nouveau mot de passe';

  @override
  String get supervisor => 'Superviseur';

  @override
  String get downloadFiles => 'Fichiers t&eacute;l&eacute;charg&eacute;s';

  @override
  String get home_search_hint_inspector => 'Rechercher des commandes par ville/nom de produit';

  @override
  String get home_search_hint_admin => 'Rechercher des commandes par ville/nom de produit';

  @override
  String get search_recent_history => 'Recherches r&eacute;centes';

  @override
  String get assign => 'Attribuer';

  @override
  String get assigned => 'Attribu&eacute;';

  @override
  String get approve => 'Approuver';

  @override
  String get assign_inspector => 'Attribuer un inspecteur';

  @override
  String get unassigned => 'Non attribu&eacute;';

  @override
  String get general_all => 'Tous';

  @override
  String get general_date => 'Date';

  @override
  String get general_desc => 'Description';

  @override
  String get general_amount => 'Montant';

  @override
  String get assign_search_hint => 'Veuillez entrer le pseudo/nom/e-mail/t&eacute;l&eacute;phone';

  @override
  String get assign_cancel_message => 'Confirmer l&#39;annulation de l&#39;attribution de cet inspecteur';

  @override
  String get assign_inspect_times => 'Nombre d&#39;inspections';

  @override
  String get assign_leave_message_batch => 'Laisser un message en lot';

  @override
  String get assign_price_zero_tips => 'Les frais d&#39;inspection ne peuvent pas &ecirc;tre 0';

  @override
  String get assign_applied => 'Postul&eacute;';

  @override
  String get is_auth_forbidden => 'Forbidden';

  @override
  String get apply_time => 'Postul&eacute; le';

  @override
  String get assign_message => 'Message';

  @override
  String get chat_send_message => 'Envoyer un message';

  @override
  String get chat_send_order => 'Envoyer une commande';

  @override
  String get chat_panel_album => 'Album';

  @override
  String get chat_panel_camera => 'Appareil photo';

  @override
  String get chat_panel_file => 'Fichier';

  @override
  String get chat_toolbar_custom_service => 'Service client exclusif';

  @override
  String get chat_toolbar_submit_order => 'Passer une commande d&#39;inspection';

  @override
  String get home_navigation => 'Cliquer pour naviguer';

  @override
  String get price_input_error_zero => 'La commande doit &ecirc;tre entre 0 et 1 million';

  @override
  String get filter_all => 'Tous les filtres';

  @override
  String get filter_heading_order_status => 'Par statut de commande';

  @override
  String get filter_heading_insp_date => 'Par date d&#39;inspection';

  @override
  String get filter_heading_order_date => 'Par date de publication';

  @override
  String get filter_heading_area => 'Par zone';

  @override
  String get filter_date_start => 'Date de d&eacute;but';

  @override
  String get filter_date_end => 'Date de fin';

  @override
  String get filter_date_today => 'Commandes d&#39;aujourd&#39;hui';

  @override
  String get filter_date_tomorrow => 'Commandes de demain';

  @override
  String get filter_date_2days_later => 'Commandes dans 2 jours';

  @override
  String get filter_date_3days_later => 'Commandes dans 3 jours';

  @override
  String get sort_by_order_date => 'Trier par date de commande';

  @override
  String get sort_by_insp_date => 'Trier par date d&#39;inspection';

  @override
  String get sort_by_distance => 'Trier par distance';

  @override
  String get purchase_all_replies => 'Toutes les r&eacute;ponses';

  @override
  String get purchase_replies_count => 'r&eacute;ponses';

  @override
  String get purchase_no_more_replies => 'Plus de r&eacute;ponses';

  @override
  String get purchase_save_draft_title => 'Enregistrer comme brouillon ?';

  @override
  String get purchase_save_draft_choice => 'Enregistrer comme brouillon';

  @override
  String get purchase_save_draft_quit => 'Quitter directement';

  @override
  String get purchase_search_hint => 'Rechercher des commandes d&#39;achat';

  @override
  String get purchase_reply_hint => 'R&eacute;pondre au contenu du post';

  @override
  String get purchase_reply_reason_hint => 'Reason why recommend';

  @override
  String get purchase_complaint_hint => 'Fournir plus d&#39;informations aidera &agrave; traiter le rapport plus rapidement';

  @override
  String get purchase_reply_paid_hint => 'Entrez le contenu de la r&eacute;compense';

  @override
  String get purchase_edit => 'Modifier le post';

  @override
  String get purchase_publish => 'Publier une demande de r&eacute;compense';

  @override
  String get purchase_publish_product_label => 'Nom du produit';

  @override
  String get purchase_publish_title_label => 'Titre de la r&eacute;compense';

  @override
  String get purchase_publish_quantity => 'Quantity';

  @override
  String get purchase_publish_content_label => 'Description d&eacute;taill&eacute;e';

  @override
  String get purchase_publish_product_hint => 'Veuillez entrer le nom du produit';

  @override
  String get purchase_publish_title_hint => 'Peut inclure le nom du mod&egrave;le, la r&eacute;gion, la quantit&eacute;, etc.';

  @override
  String get end_date => 'End date';

  @override
  String get purchase_area => 'Area';

  @override
  String get purchase_permission_author_only => 'Only author of post can see replies';

  @override
  String get purchase_publish_quantity_hint => 'Please input quantity';

  @override
  String get purchase_publish_content_hint => 'Veuillez entrer une description d&eacute;taill&eacute;e';

  @override
  String get purchase_publish_price => 'Prix de la r&eacute;compense';

  @override
  String get purchase_publish_choose_category => 'S&eacute;lectionner une cat&eacute;gorie';

  @override
  String get purchase_publish_choose_category_hint => 'Veuillez s&eacute;lectionner une cat&eacute;gorie';

  @override
  String get purchase_paid_publish_switch => 'Vue payante';

  @override
  String get purchase_paid_publish_set_price => 'D&eacute;finir le prix';

  @override
  String get purchase_detail_response_all => 'Toutes les r&eacute;ponses';

  @override
  String get purchase_detail_response_author_only => 'Voir uniquement l&#39;auteur';

  @override
  String get purchase_detail_response_asc => 'Ordre croissant';

  @override
  String get purchase_detail_response_desc => 'Ordre d&eacute;croissant';

  @override
  String get purchase_detail_more_reply => 'R&eacute;pondre';

  @override
  String get purchase_detail_more_up => 'J&#39;aime';

  @override
  String get purchase_detail_more_cancel_up => 'Annuler le j&#39;aime';

  @override
  String get purchase_my_posts => 'Mes publications';

  @override
  String get purchase_my_replies => 'Mes r&eacute;ponses';

  @override
  String get purchase_my_appeals => 'Mes appels';

  @override
  String get purchase_appeal_detail => 'D&eacute;tails de l&#39;appel';

  @override
  String get purchase_appeal_submit => 'Soumettre l&#39;appel';

  @override
  String get purchase_appeal_cancel => 'Annuler l&#39;appel';

  @override
  String get purchase_appeal_approve => 'Appel approuv&eacute;';

  @override
  String get purchase_appeal_denied => 'Appel refus&eacute;';

  @override
  String get purchase_paid_content_owner_tips => 'Contenu payant';

  @override
  String get purchase_paid_content_tips => 'Contenu payant, payez pour voir';

  @override
  String get purchase_paid_content_paid_tips => 'Contenu payant d&eacute;bloqu&eacute;';

  @override
  String get purchase_review_leave => 'Laisser un avis';

  @override
  String get purchase_review_my_score => 'Mon &eacute;valuation';

  @override
  String get purchase_my_replies_original_header => 'Publication originale';

  @override
  String get purchase_publish_bounty_tips => 'Remarque : Le montant de la r&eacute;compense peut &ecirc;tre rempli librement, une r&eacute;compense plus &eacute;lev&eacute;e peut attirer plus d&#39;inspecteurs pour fournir activement les informations dont vous avez besoin.';

  @override
  String get purchase_reply_to => 'R&eacute;pondre &agrave;';

  @override
  String get purchase_modify_bounty => 'Modifier la r&eacute;compense';

  @override
  String get purchase_bounty_money => 'R&eacute;compense';

  @override
  String get purchase_evaluated_person => 'personnes ont &eacute;valu&eacute;';

  @override
  String get purchase_comment_paid_supplier => 'Fournisseur';

  @override
  String get purchase_comment_paid_contact => 'Contact';

  @override
  String get purchase_comment_paid_phone => 'Num&eacute;ro de t&eacute;l&eacute;phone';

  @override
  String get purchase_comment_paid_email => 'E-mail';

  @override
  String get purchase_comment_paid_address => 'Adresse de l&#39;usine';

  @override
  String get purchase_comment_paid_other => 'Autres';

  @override
  String get purchase_comment_paid_low_price => 'Prix de base du produit';

  @override
  String get purchase_appeal_title => 'Demander un remboursement';

  @override
  String get purchase_appeal_reason => 'Veuillez entrer la raison de l&#39;appel';

  @override
  String get purchase_appeal_request_price => 'Montant de l&#39;appel :';

  @override
  String get purchase_appeal_request_reason => 'Raison de l&#39;appel :';

  @override
  String get purchase_post_status_draft => 'Brouillon';

  @override
  String get purchase_post_status_reviewing => 'En cours d&#39;examen';

  @override
  String get purchase_post_status_published => 'Examin&eacute; et approuv&eacute;';

  @override
  String get purchase_post_status_denied => 'Non approuv&eacute;';

  @override
  String get purchase_post_publish => 'Publier le post';

  @override
  String get purchase_complaint_type_leading => 'Veuillez s&eacute;lectionner le type de rapport';

  @override
  String get purchase_complaint_type_1 => 'Contenu pornographique';

  @override
  String get purchase_complaint_type_2 => 'Publicit&eacute; ind&eacute;sirable';

  @override
  String get purchase_complaint_type_3 => 'Harc&egrave;lement/Attaque';

  @override
  String get purchase_complaint_type_4 => 'Activit&eacute; ill&eacute;gale';

  @override
  String get purchase_complaint_type_5 => 'Information politique inexacte';

  @override
  String get purchase_complaint_type_6 => 'Violation des droits';

  @override
  String get purchase_complaint_type_7 => 'Autres';

  @override
  String get shop_goods_detail_title => 'D&eacute;tails du produit';

  @override
  String get mall_buy_immediate => 'Acheter maintenant';

  @override
  String get mall_goods_count => 'Quantit&eacute;';

  @override
  String get mall_confirm_pay => 'Confirmer le paiement';

  @override
  String get mall_order_confirm => 'Confirmer la commande';

  @override
  String get mall_submit_order => 'Soumettre la commande';

  @override
  String get mall_goods_price => 'Prix du produit';

  @override
  String get mall_express_price => 'Frais d&#39;exp&eacute;dition';

  @override
  String get mall_price_total => 'Total :';

  @override
  String get mall_payment => 'Caisse';

  @override
  String get mall_payment_methods => 'M&eacute;thodes de paiement';

  @override
  String get mall_pay_succeed => 'Paiement r&eacute;ussi';

  @override
  String get mall_check_order_detail => 'Voir les d&eacute;tails de la commande';

  @override
  String get mall_order_remark => 'Remarque sur la commande';

  @override
  String get mall_order_remark_input => 'Entrer une remarque';

  @override
  String get purchase_detail_more_report => 'Signaler';

  @override
  String get purchase_reply_paid_content_tips => 'Remarque : Veuillez remplir des informations v&eacute;ridiques, les r&eacute;ponses payantes doivent attendre l&#39;examen apr&egrave;s la publication et ne peuvent &ecirc;tre vues par d&#39;autres qu&#39;apr&egrave;s examen';

  @override
  String get public_ip_address => 'Localisation IP :';

  @override
  String get inspection_widget_suit_tips => 'Lors de l&#39;inspection, veuillez porter un badge d&#39;identification ou un uniforme de travail. Si vous n&#39;en avez pas encore, vous pouvez en acheter un sur la page d&#39;accueil';

  @override
  String get purchase_paid_content_appeal => 'Faire appel';

  @override
  String get report_success => 'Rapport r&eacute;ussi';

  @override
  String tabbar_tab_names(String names) {
    String _temp0 = intl.Intl.selectLogic(
      names,
      {
        'tab_home': 'Accueil',
        'tab_shortcut': 'Raccourci',
        'tab_message': 'Messages',
        'tab_mine': 'Mon compte',
        'tab_publish': '',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String order_status_desc(String status) {
    String _temp0 = intl.Intl.selectLogic(
      status,
      {
        'order_status_unknown': 'Inconnu',
        'order_wait_pay': 'En attente de paiement',
        'order_cancelled': 'Annul&eacute;',
        'order_cancelled_refund_pending': 'Annul&eacute;, remboursement en attente d&#39;examen',
        'order_refund_pending': 'Remboursement en attente d&#39;examen',
        'order_refund_partial': 'Remboursement partiel',
        'order_refund_denied': 'Remboursement refus&eacute;',
        'order_wait_dispatch': 'En attente d&#39;attribution',
        'order_doing': 'En cours d&#39;inspection',
        'order_finished': 'Termin&eacute;',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String pay_type(String type) {
    String _temp0 = intl.Intl.selectLogic(
      type,
      {
        'pay_rmb': 'Compte RMB',
        'pay_usd': 'Compte USD',
        'pay_zfb': 'Alipay',
        'pay_paypal': 'PayPal',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String purchase_complaint_type(String type) {
    String _temp0 = intl.Intl.selectLogic(
      type,
      {
        'type_1': 'Contenu pornographique',
        'type_2': 'Publicit&eacute; ind&eacute;sirable',
        'type_3': 'Harc&egrave;lement/Attaque',
        'type_4': 'Activit&eacute; ill&eacute;gale',
        'type_5': 'Information politique inexacte',
        'type_6': 'Violation des droits',
        'type_7': 'Autres',
        'other': '',
      },
    );
    return '$_temp0';
  }

  @override
  String get purchase_reply_paid_my_evaluate => '(Mon &eacute;valuation)';

  @override
  String get bind_now => 'Lier maintenant';

  @override
  String get cancel_register => 'Annuler l&#39;inscription';

  @override
  String register_and_bind_email0(Object email) {
    return 'S&#39;inscrire et lier $email comme e-mail principal';
  }

  @override
  String register_and_bind_email1(Object email) {
    return 'Votre e-mail $email est d&eacute;j&agrave; enregistr&eacute;, continuer la liaison';
  }

  @override
  String get register_and_bind_email2 => 'S&#39;inscrire et lier l&#39;e-mail principal';

  @override
  String get new_register_bind_title => 'D&eacute;finir l&#39;e-mail de connexion principal et s&#39;inscrire';

  @override
  String get new_register_bind_field_account_tips => 'Lier e-mail/num&eacute;ro de t&eacute;l&eacute;phone';

  @override
  String get register => 'S&#39;inscrire';

  @override
  String get switch_account => 'Changer de compte';

  @override
  String get switch_account_confirm_tips => '&Ecirc;tes-vous s&ucirc;r de vouloir changer de compte ?';

  @override
  String get password_must_have => 'Votre mot de passe doit contenir :';

  @override
  String get password_must_have_1 => '8 &agrave; 32 caract&egrave;res';

  @override
  String get password_must_have_2 => '1 lettre minuscule (a-z)';

  @override
  String get password_must_have_3 => '1 chiffre';

  @override
  String get password_must_have_4 => '1 symbole (comme !@#\\\$%^&*)';

  @override
  String get password_login => 'Entrez le mot de passe de connexion';

  @override
  String get password_login_again => 'Entrez &agrave; nouveau le nouveau mot de passe';

  @override
  String get choose_account_to_login => 'Choisissez un compte pour une connexion rapide';

  @override
  String get finish => 'Terminer';

  @override
  String get done => 'Termin&eacute;';

  @override
  String get account_apple => 'Connexion Apple';

  @override
  String get account_google => 'Connexion Google';

  @override
  String get account_wechat => 'Connexion WeChat';

  @override
  String get account_facebook => 'Connexion Facebook';

  @override
  String get third_account_unbind => 'D&eacute;lier';

  @override
  String get third_account_bind => 'Lier';

  @override
  String get confirm_unbind => '&Ecirc;tes-vous s&ucirc;r de vouloir d&eacute;lier ?';

  @override
  String get inspection_requirement => 'Exigences d&#39;inspection';

  @override
  String get liveroom_entrance => 'File d&#39;attente de la salle en direct';

  @override
  String get add_account => 'Ajouter un nouveau compte';

  @override
  String get message_order => 'Orders';

  @override
  String get message_email => 'Email';

  @override
  String get message_wallet => 'Wallet';

  @override
  String get message_user => 'User info';

  @override
  String get salesman => 'Salesman';

  @override
  String get public_continue => 'Continue';

  @override
  String get camera_permission_tips => 'In order to facilitate you to upload pictures, files or photos when using the App, you need to allow the use of camera and storage permissions.';

  @override
  String get ai_category_inspector => 'Inspecteur de catégorie';

  @override
  String get ai_nothing_category => 'Aucune catégorie détectable';

  @override
  String get ai_category_name => 'Nom de la catégorie';

  @override
  String get ai_quantity => 'Quantité';

  @override
  String get ai_packaging => 'Emballage';

  @override
  String get ai_shipping_mark => 'Marque d\'expédition';

  @override
  String get ai_product_style => 'Style du produit';

  @override
  String get ai_test => 'Test';

  @override
  String get ai_craftsmanship => 'Qualité de fabrication';

  @override
  String get ai_test_verification => 'Vérification des tests';

  @override
  String get ai_category_measure => 'Mesure de catégorie';

  @override
  String get ai_spare_parts => 'Pièces de rechange';

  @override
  String get ai_sampling_number => 'Numéro d\'échantillonnage';

  @override
  String ai_input_range_number(Object range) {
    return 'Entrez un nombre dans la plage $range';
  }

  @override
  String ai_enter_range_number(Object range) {
    return 'Veuillez entrer un nombre dans la plage $range';
  }

  @override
  String get ai_selected => 'Sélectionné';

  @override
  String get ai_selected_status => 'Statut sélectionné';

  @override
  String get ai_order_quantity => 'Quantité de commande';

  @override
  String get ai_packaged_boxes_quantity => 'Quantité de boîtes emballées (produit fini)';

  @override
  String get ai_unpackaged_boxes_quantity => 'Quantité de boîtes non emballées (produit fini)';

  @override
  String get ai_sample_from_packaged => 'Échantillonage à partir des boîtes emballées';

  @override
  String get ai_sample_from_unpackaged => 'Échantillonage à partir des boîtes non emballées';

  @override
  String get ai_spare_parts_quantity => 'Quantité de pièces de rechange';

  @override
  String get ai_sampling_packaging_number => 'Numéro d\'emballage d\'échantillonnage';

  @override
  String get ai_sampling_packaging_number_record => 'Enregistrement du numéro d\'emballage d\'échantillonnage';

  @override
  String get ai_sampling_packaging_number_list => 'Liste des numéros d\'emballages externes d\'échantillonnage';

  @override
  String get ai_judgment => 'Jugement';

  @override
  String get ai_judgment_item => 'Élément de jugement';

  @override
  String get ai_standard => 'Norme';

  @override
  String get ai_result => 'Résultat';

  @override
  String get ai_conclusion => 'Conclusion';

  @override
  String get ai_overall_conclusion => 'Conclusion générale';

  @override
  String get ai_consistency => 'Consistance';

  @override
  String get ai_yes => 'Oui';

  @override
  String get ai_no => 'Non';

  @override
  String get ai_remarks => 'Remarques';

  @override
  String get ai_numerical => 'Numéro';

  @override
  String get ai_recommended_test_items => 'Éléments de test recommandés';

  @override
  String get ai_test_item => 'Élément de test';

  @override
  String get ai_add_all => 'Ajouter tout';

  @override
  String get ai_add_plus => '+ Ajouter';

  @override
  String get ai_add => 'Ajouter';

  @override
  String ai_confirm_delete(Object name) {
    return 'Êtes-vous sûr de vouloir supprimer $name ?';
  }

  @override
  String get ai_enter_test_item => 'Entrez l\'élément de test';

  @override
  String get ai_defect_record => 'Enregistrement de défauts';

  @override
  String get ai_defect_photo => 'Photo de défaut';

  @override
  String get ai_defect_description => 'Description du défaut';

  @override
  String get ai_defect_level => 'Niveau de défaut';

  @override
  String get ai_found_quantity => 'Quantité trouvée';

  @override
  String get ai_handling_method => 'Méthode de gestion';

  @override
  String get ai_edit => 'Modifier';

  @override
  String get ai_delete => 'Supprimer';

  @override
  String get ai_pick_out => 'Sélectionner';

  @override
  String get ai_replace => 'Remplacer';

  @override
  String get ai_rework => 'Rétravail';

  @override
  String get ai_edit_description => 'Modifier la description';

  @override
  String get ai_critical => 'Critique';

  @override
  String get ai_important => 'Important';

  @override
  String get ai_minor => 'Mineur';

  @override
  String get ai_defect_list => 'Liste de défauts';

  @override
  String get ai_test_level => 'Niveau de test';

  @override
  String get ai_sampling_sample => 'Échantillon d\'échantillonnage';

  @override
  String get ai_sampling_level => 'Niveau d\'échantillonnage';

  @override
  String get ai_additional_information => 'Informations supplémentaires';

  @override
  String get ai_inspection_record => 'Enregistrement d\'inspection';

  @override
  String get ai_sample_count => 'Comptage d\'échantillons';

  @override
  String get ai_maximum_allowable_value => 'Valeur maximale autorisée';

  @override
  String get ai_test_item_name => 'Nom de l\'élément de test';

  @override
  String get ai_test_result => 'Résultat du test';

  @override
  String get ai_basic_information => 'Informations de base';

  @override
  String get ai_new_test_item => 'Nouvel élément de test';

  @override
  String get ai_test_project => 'Projet de test';

  @override
  String get ai_measurement_project => 'Projet de mesure';

  @override
  String get ai_measure_need_num => 'Nombre nécessaire';

  @override
  String get ai_measurement_unit => 'Unité de mesure';

  @override
  String get ai_measurement_method => 'Méthode de mesure';

  @override
  String get ai_measurement_record => 'Enregistrement de mesure';

  @override
  String get ai_measured => 'Mesuré';

  @override
  String get ai_unit_of_measurement => 'Unité de mesure';

  @override
  String get ai_measured_value => 'Valeur mesurée';

  @override
  String get ai_product_number => 'Numéro de produit';

  @override
  String get ai_number => 'Numéro';

  @override
  String get ai_new_measurement_item => 'Nouvel élément de mesure';

  @override
  String get ai_length_width_height => 'Longueur, largeur, hauteur';

  @override
  String get ai_dimensions_length => 'Longueur';

  @override
  String get ai_dimensions_width => 'Largeur';

  @override
  String get ai_dimensions_height => 'Hauteur';

  @override
  String get ai_length_width => 'Longueur et largeur';

  @override
  String get ai_other => 'Autre';

  @override
  String get ai_allowable_error => 'Erreur permise';

  @override
  String get ai_report_summary => 'Résumé du rapport';

  @override
  String get ai_special_note => 'Note spéciale';

  @override
  String get ai_overall_conclusion_2 => 'Conclusion globale';

  @override
  String get ai_summary => 'Résumé';

  @override
  String get ai_category_name_table => 'Tableau des noms de catégories';

  @override
  String get ai_compliance => 'Conformité';

  @override
  String get ai_remarks_2 => 'Remarques';

  @override
  String get ai_defect_summary => 'Résumé des défauts';

  @override
  String get ai_no_guidance_instructions => 'Pas d\'instructions de guide pour le moment';

  @override
  String get ai_no_standard_instructions => 'Pas d\'instructions standards pour le moment';

  @override
  String get ai_please_fill_in => 'Veuillez remplir';

  @override
  String ai_please_supplement_level_or_sample(Object level, Object sample) {
    return 'Veuillez compléter $level ou $sample';
  }

  @override
  String get ai_please_add => 'Veuillez ajouter';

  @override
  String get ai_please_input => 'Veuillez saisir';

  @override
  String get ai_please_select => 'Veuillez sélectionner';

  @override
  String ai_name_not_filled(Object name) {
    return '$name non rempli';
  }

  @override
  String get ai_addition_successful => 'Ajout réussi';

  @override
  String get ai_confirm_action => 'Confirmer';

  @override
  String get ai_cancel_action => 'Annuler';

  @override
  String get ai_submit => 'Soumettre';

  @override
  String get ai_next_item => 'Article suivant';

  @override
  String get ai_complete => 'Complet';

  @override
  String get ai_change_description => 'Changer la description';

  @override
  String get ai_action_guidance_instructions => 'Instructions de guide d\'action';

  @override
  String get ai_action_standard_instructions => 'Instructions standard d\'action';

  @override
  String get ai_add_description => 'Ajouter une description';

  @override
  String get ai_change_description_note => 'Note : ci-dessous figurent les défauts déjà découverts. Si vous modifiez cela, les données historiques adopteront également la nouvelle description !';

  @override
  String get ai_packing_completion_rate => 'Taux de complétion d\'emballage';

  @override
  String get ai_unprocessed_quantity => 'Quantité non traitée';

  @override
  String get ai_sample_level_type_0 => 'Niveau I';

  @override
  String get ai_sample_level_type_1 => 'Niveau II';

  @override
  String get ai_sample_level_type_2 => 'Niveau III';

  @override
  String get ai_sample_level_type_s1 => 'S-1';

  @override
  String get ai_sample_level_type_s2 => 'S-2';

  @override
  String get ai_sample_level_type_s3 => 'S-3';

  @override
  String get ai_sample_level_type_s4 => 'S-4';

  @override
  String get ai_sample_level_type_nothing => 'Aucun';

  @override
  String get ai_inspection_image => 'Image';

  @override
  String get ai_photo_confirm => 'Confirmer la photo';

  @override
  String get ai_add_product_ask_save => 'Voulez-vous sauvegarder cette édition ?';

  @override
  String get ai_add_product_save => 'Sauvegarder';

  @override
  String get ai_add_product_edit_model => 'Édition du modèle';

  @override
  String get ai_add_product_model_name => 'Nom du modèle';

  @override
  String get ai_add_product_input_model => 'Entrez le nom du modèle';

  @override
  String get ai_add_product_num => 'Quantité';

  @override
  String get ai_add_product_input_num => 'Entrez la quantité du modèle';

  @override
  String get ai_add_product_unit => 'Unité';

  @override
  String get ai_add_product_ask_delete => 'Voulez-vous supprimer ce modèle ?';

  @override
  String get ai_add_product_edit_product => 'Édition du produit';

  @override
  String get ai_add_product_product_name => 'Nom du produit';

  @override
  String get ai_add_product_model => 'Modèle';

  @override
  String get ai_add_product_input_product_name => 'Entrez le nom du produit';

  @override
  String get ai_add_product_new_model => 'Nouveau modèle';

  @override
  String get ai_add_product_ask_product => 'Voulez-vous supprimer ce produit et tous ses modèles ?';

  @override
  String get ai_add_product_picture_lost => 'Image manquante';

  @override
  String ai_add_product_lost_des(Object lostStr) {
    return 'Actuellement, $lostStr est manquant, veuillez compléter toutes les informations avant de procéder à l\'inspection';
  }

  @override
  String get ai_add_product_model_full => 'Nom complet du modèle du produit';

  @override
  String get ai_add_product_model_title => 'Modèle du produit';

  @override
  String get ai_add_product_new => 'Ajouter un produit';

  @override
  String get ai_model_unit_piece => 'piece';

  @override
  String get ai_model_unit_only => 'piece';

  @override
  String get ai_model_unit_item => 'piece';

  @override
  String get ai_model_unit_pair => 'pair';

  @override
  String get ai_model_unit_set => 'set';

  @override
  String get ai_model_unit_dozen => 'dozen';

  @override
  String get ai_model_unit_roll => 'roll';

  @override
  String get ai_model_unit_vehicle => 'vehicle';

  @override
  String get ai_model_unit_head => 'head';

  @override
  String get ai_model_unit_bag => 'bag';

  @override
  String get ai_model_unit_box => 'box';

  @override
  String get ai_model_unit_pack => 'pack';

  @override
  String get ai_model_unit_yard => 'yard';

  @override
  String get ai_model_unit_meter => 'meter';

  @override
  String get ai_model_unit_kilogram => 'kilogram';

  @override
  String get ai_model_unit_metric_ton => 'metric ton';

  @override
  String get ai_model_unit_liter => 'liter';

  @override
  String get ai_model_unit_gallon => 'gallon';

  @override
  String get ai_model_unit_other => 'other';

  @override
  String get ai_default_config_des => 'There are currently no detection templates for the product. You can choose the template below or call (+86) to configure the template.';

  @override
  String get ai_default_config_category_all => 'catégories (toutes)';

  @override
  String get ai_default_config_select_template => 'Veuillez sélectionner un modèle';

  @override
  String get ai_default_config_template_selection => 'Sélection du modèle';

  @override
  String get ai_default_config_search_template => 'rechercher un modèle';

  @override
  String get ai_default_config_classify => 'Classification';

  @override
  String get ai_default_config_preview => 'Aperçu';

  @override
  String get ai_default_config_use => 'apply';

  @override
  String get ai_default_config_current_use_button => 'Appliquer au produit actuel';

  @override
  String get ai_default_config_more_use_button => 'Appliquer à plus de produits';

  @override
  String get ai_default_config_product_list => 'Liste de produits';

  @override
  String get ai_default_config_use_warning => 'Remarque: Le produit [Modèle] a déjà été chargé avec un modèle; [Opération] le modèle a déjà été configuré pour l\'opération. Si vous rechargez un nouveau modèle, cela remplacera les données précédentes.';

  @override
  String get ai_default_config_tag_default => 'Opération';

  @override
  String get ai_default_config_tag_manual => 'Modèle';

  @override
  String get ai_default_config_load_progress => 'Progression du chargement';

  @override
  String ai_default_config_template_progress(Object name) {
    return 'Modèle chargé avec succès $name.';
  }

  @override
  String ai_default_config_template_fail_count(Object name) {
    return '$name échecs, cliquez pour réessayer';
  }

  @override
  String get ai_default_config_load => 'Charger';

  @override
  String get ai_default_config_success => 'Succès';

  @override
  String get ai_default_config_fail => 'Échec';

  @override
  String get ai_default_config_template => 'template';

  @override
  String get ai_add_model_count_warning => 'The quantity must be greater than 0';

  @override
  String get ai_default_config_product_edit => '编辑产品';

  @override
  String get ai_wait => 'Dans l’attente';

  @override
  String get ai_product_info => 'Informations sur le produit';

  @override
  String get ai_product_category => 'Catégorie de produit';

  @override
  String get ai_product_unit => 'Unité de produit';

  @override
  String get ai_package_num_done => 'Cartons emballés';

  @override
  String get ai_product_full => 'Informations complémentaires';

  @override
  String get ai_product_full_tip => 'Les informations sur le produit dans la commande sont incomplètes. Veuillez les compléter selon les informations réelles du produit sur le site d\'inspection.';

  @override
  String get ai_each_box => 'Par carton';

  @override
  String get ai_simple_count => 'Nombre d\'échantillons';

  @override
  String get ai_simple_level => 'Norme d\'échantillonnage';

  @override
  String get ai_simple_num => 'Quantité d\'échantillonnage';

  @override
  String get ai_simple_no => 'Numéro de carton échantillonné';

  @override
  String get ai_simple_result => 'Résultat d\'évaluation';

  @override
  String get ai_simple_project => 'Article d\'inspection';

  @override
  String get ai_simple_project_manage => 'Gérer les articles d\'inspection';

  @override
  String get ai_simple_project_edit => 'Modifier l\'article d\'inspection';

  @override
  String get ai_simple_project_recmend => 'Recommandation intelligente';

  @override
  String get ai_simple_project_input => 'Enregistrement d\'inspection';

  @override
  String get ai_simple_help => 'Aide';

  @override
  String get ai_simple_project_record => 'Enregistrement d\'inspection';

  @override
  String get ai_simple_require => 'Exigences du client';

  @override
  String get ai_simple_record => 'Enregistrer';

  @override
  String get ai_simple_dsec => 'Description';

  @override
  String get ai_simple_before => 'Article précédent';

  @override
  String get ai_simple_add => 'Ajouter un groupe';

  @override
  String get ai_simple_add_desc => 'Ajouter une description pour les photos';

  @override
  String get ai_simple_add_citations => 'Citations';

  @override
  String get ai_no_more => 'Pas plus de données';

  @override
  String get ai_wrong_tip => 'La quantité ne peut pas dépasser le total';

  @override
  String get ai_defect_records => 'Enregistrements de défauts';

  @override
  String get ai_check_require => 'Exigences d\'échantillonnage';

  @override
  String get ai_find_defect => 'Défaut trouvé';

  @override
  String get ai_defect_question => 'Problème de défaut';

  @override
  String get ai_modify_level => 'Modifier le niveau d\'échantillonnage';

  @override
  String get ai_defect_quick => 'Ajout rapide de défaut de processus';

  @override
  String get ai_defect_self => 'Nom de défaut personnalisé';

  @override
  String get ai_defect_record_list => 'Liste d\'enregistrements de défauts';

  @override
  String get ai_measure_require => 'Exigences de mesure';

  @override
  String get ai_measurement_item => 'Article de mesure';

  @override
  String get ai_measurement_error => 'Erreur';

  @override
  String get ai_measurement_standard => 'Norme de mesure';

  @override
  String get ai_measurement_value_standard => 'Valeur standard';

  @override
  String get ai_measurement_camera => 'Photo de mesure';

  @override
  String get ai_measurement_add => 'Ajout rapide de norme de mesure';

  @override
  String get ai_product_first => 'Image principale du produit';

  @override
  String get ai_product_report => 'Générer un rapport';

  @override
  String get ai_product_report_tip => 'Veuillez sélectionner l\'image principale du produit';

  @override
  String get ai_product_report_special => 'Veuillez saisir le contenu nécessitant une attention particulière';

  @override
  String get ai_product_report_sign => 'Signature';

  @override
  String get ai_product_report_sign_done => 'Signature terminée';

  @override
  String get ai_defect_names => 'Noms des défauts';

  @override
  String get ai_input_tip => 'Veuillez saisir le nom';

  @override
  String get ai_add_measure_tip => 'Veuillez d\'abord ajouter une norme de mesure';

  @override
  String get ai_wrong_num => 'Quantité incorrecte';

  @override
  String get ai_wrong_name => 'Veuillez saisir le nom du produit';

  @override
  String get ai_wrong_sample_num => 'Ne peut pas dépasser le nombre d\'échantillons';

  @override
  String get ai_per_box => 'Quantité par carton';

  @override
  String get ai_wrong_sample_num_cal => 'Échantillons emballés + non emballés doivent égaler le nombre d\'échantillons';

  @override
  String get ai_sure_delete => 'Confirmer la suppression ?';

  @override
  String get ai_choose_tip => 'Veuillez sélectionner la méthode et la quantité de traitement des défauts';

  @override
  String get ai_weight => 'Poids brut';

  @override
  String get sampling_plan => 'Plan d\'échantillonnage';

  @override
  String get single => 'Unique';

  @override
  String get normal => 'Normal';

  @override
  String get summarize => 'Summarize';

  @override
  String get po_number => 'Numéro de PO';

  @override
  String get product_quantity => 'Quantité de produit';

  @override
  String get customer_name => 'Nom du client';

  @override
  String get supplier_name => 'Nom du fournisseur';

  @override
  String get inspection_date => 'Date d\'inspection';

  @override
  String get arrival_time => 'Heure d\'arrivée';

  @override
  String get completion_time => 'Heure d\'achèvement';

  @override
  String get inspection_address => 'Adresse d\'inspection';

  @override
  String get inspector => 'Inspecteur';

  @override
  String get inspection_report_note => 'Ce rapport d\'inspection est fourni à titre de référence uniquement. L\'approbation finale est soumise à la confirmation du client.';

  @override
  String get remark_toast => 'Please fill in the remarks first';

  @override
  String get process_appearance_judgment => 'Process appearance judgment';

  @override
  String get test_validation_judgment => 'Test validation judgment';

  @override
  String check_save(Object name) {
    return 'Are you sure you want to save $name?';
  }

  @override
  String get select_template_config_tip => 'If you need to configure the inspection template, please contact your order follower';
}
