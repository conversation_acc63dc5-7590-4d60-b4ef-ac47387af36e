// ignore_for_file: no_leading_underscores_for_local_identifiers

import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:inspector/app/config/constant.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/auth/third_login_store.dart';
import 'package:inspector/app/modules/store/privacy_store.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/storage_util.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/app/tools/translation_service.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'app/modules/store/setting_store.dart';
import 'flavors.dart';
import 'generated/l10n.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  String? initial;
  Get.put(PrivacyStore(), permanent: true);
  await StorageUtil.init();
  await GlobalConst.asyns();

  ThemeData _darkTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,
    primaryColor: Colors.black,
    scaffoldBackgroundColor: Colors.black,
    appBarTheme: const AppBarTheme(backgroundColor: Colors.black, foregroundColor: Colors.white, scrolledUnderElevation: 0),
  );

  ThemeData _lightTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.light,
    primaryColor: Colors.white,
    scaffoldBackgroundColor: MColor.xFFFAFAFA,
    appBarTheme: const AppBarTheme(backgroundColor: Colors.white, foregroundColor: Colors.black, scrolledUnderElevation: 0),
  );

  if (Platform.isAndroid) {
    SystemUiOverlayStyle systemUiOverlayStyle = const SystemUiOverlayStyle(statusBarColor: Colors.transparent);
    SystemChrome.setSystemUIOverlayStyle(systemUiOverlayStyle);
  }

  await StorageUtil.setBool(Constant.kStart, true);
  initial = await AppPages.INITIAL(true);

  Get.put(SettingStore(), permanent: true);

  // changeTheme();
  runApp(
    RefreshConfiguration(
      enableLoadingWhenNoData: true,
      enableLoadingWhenFailed: true,
      hideFooterWhenNotFull: true,
      headerBuilder: () {
        return ClassicHeader(
          refreshStyle: RefreshStyle.Follow,
          textStyle: MFont.regular15.apply(color: MColor.skin),
          refreshingIcon: loadingWidget,
          failedIcon: const Icon(Icons.error, color: MColor.skin),
          completeIcon: const Icon(Icons.done, color: MColor.skin),
          idleIcon: const Icon(Icons.arrow_downward, color: MColor.skin),
          releaseIcon: const Icon(Icons.refresh, color: MColor.skin),
        );
      },
      footerBuilder: () {
        return ClassicFooter(
          textStyle: MFont.regular15.apply(color: MColor.skin),
          loadStyle: LoadStyle.ShowWhenLoading,
          failedIcon: const Icon(Icons.error, color: MColor.skin),
          loadingIcon: const SpinKitCircle(
            color: MColor.skin,
            size: 20.0,
          ),
          canLoadingIcon: const Icon(Icons.autorenew, color: MColor.skin),
          idleIcon: const Icon(Icons.arrow_upward, color: MColor.skin),
        );
      },
      child: GetMaterialApp(
        debugShowCheckedModeBanner: false,
        onGenerateTitle: (context) => S.of(context).app_name,
        locale: SettingStore.to.getCurrentLocale(),
        theme: _lightTheme,
        darkTheme: _darkTheme,
        themeMode: ThemeMode.light,
        initialRoute: initial,
        initialBinding: AppBinding(),
        getPages: AppPages.routes,
        builder: EasyLoading.init(),
        localizationsDelegates: const [
          S.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate
        ],
        supportedLocales: S.delegate.supportedLocales,
        localeListResolutionCallback: (locales, supportedLocales) {
          print(locales);
          return;
        },
      ),
    ),
  );

  EasyLoading.instance
    ..displayDuration = const Duration(milliseconds: 2000)
    ..indicatorType = EasyLoadingIndicatorType.fadingCircle
    ..loadingStyle = EasyLoadingStyle.custom
    ..indicatorSize = 30.0
    ..radius = 10.0
    ..progressColor = Colors.yellow
    ..backgroundColor = MColor.skin.withAlpha(50)
    ..indicatorColor = Colors.white
    ..textColor = Colors.white
    ..maskColor = Colors.transparent
    ..maskType = EasyLoadingMaskType.black
    ..userInteractions = true
    ..dismissOnTap = false;

  await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
}

void changeTheme() {
  int _themeMode = StorageUtil.containsKey('theme') ? StorageUtil.getInt('theme') : 0;
  ThemeMode themeMode = _themeMode == 0 ? ThemeMode.system : (_themeMode == 1 ? ThemeMode.light : ThemeMode.dark);
  Get.changeThemeMode(themeMode); //
}

class AppBinding extends Bindings {
  @override
  void dependencies() {
    Get.put(ThirdLoginStore(), permanent: true);
  }
}
